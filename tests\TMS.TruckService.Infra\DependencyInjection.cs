﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using TMS.SharedKernel.EntityFrameworkCore.Configuration;
using TMS.SharedKernel.EntityFrameworkCore.Interceptors;
using TMS.SharedKernel.EntityFrameworkCore.Services;
using TMS.TruckService.Infra.Data;

namespace TMS.TruckService.Infra;

/// <summary>
/// DependencyInjection
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// AddInfrastructure
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        //services.Configure<InterceptorConfiguration>(configuration.GetSection("Interceptor"));

        //// Infrastructure services
        //services.AddScoped<AuditContextProvider>();

        //// Interceptors - registered as scoped for DI
        //services.AddScoped<AuditLogInterceptor>();

        //services.AddDbContext<ApplicationDbContext>((sp, options) =>
        //{
        //    var connectionString = configuration.GetConnectionString("SharedKernalConnection");
        //    options.UseNpgsql(connectionString, npgsqlOptions =>
        //    {
        //        npgsqlOptions.EnableRetryOnFailure(
        //            maxRetryCount: 3,
        //            maxRetryDelay: TimeSpan.FromSeconds(5),
        //            errorCodesToAdd: null);
        //    });

        //    // Add the interceptor here!
        //    var interceptor = sp.GetRequiredService<AuditLogInterceptor>();
        //    options.AddInterceptors(interceptor);
        //});

        return services;
    }
}
