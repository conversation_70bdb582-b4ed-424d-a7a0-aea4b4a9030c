﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.SharedKernel.Utilities;

public static class BulkOperationExtensions
{
    private const int BulkOperationBatchSize = 1000; // Adjust as needed

    /// <summary>
    /// Generic bulk operation method that handles batching and periodic saving
    /// </summary>
    public static async Task BulkOperationInBatchesAsync<TEntity>(
        this IList<TEntity> entities,
        Func<IList<TEntity>, Task> batchOperation,
        IUnitOfWork unitOfWork,
        int batchSize = BulkOperationBatchSize,
        CancellationToken cancellationToken = default)
        where TEntity : class
    {
        if (!entities.Any()) return;

        for (int i = 0; i < entities.Count; i += batchSize)
        {
            var batch = entities.Skip(i).Take(batchSize).ToList();

            // Execute the specific operation (insert, update, delete, etc.)
            await batchOperation(batch);

            // Save periodically to avoid memory issues
            if ((i + batchSize) % (batchSize * 5) == 0)
            {
                await unitOfWork.SaveChangesAsync(cancellationToken);
            }
        }

        // Final save
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Convenience extension for bulk insert operations
    /// </summary>
    public static async Task BulkInsertInBatchesAsync<TEntity>(
        this IList<TEntity> entities,
        IBaseRepository<TEntity> repository,
        IUnitOfWork unitOfWork,
        int batchSize = BulkOperationBatchSize,
        CancellationToken cancellationToken = default)
        where TEntity : class
    {
        await entities.BulkOperationInBatchesAsync(
            batch => repository.AddRangeAsync(batch, cancellationToken),
            unitOfWork,
            batchSize: batchSize,
            cancellationToken);
    }

    /// <summary>
    /// Convenience extension for bulk update operations
    /// </summary>
    public static async Task BulkUpdateInBatchesAsync<TEntity>(
        this IList<TEntity> entities,
        IBaseRepository<TEntity> repository,
        IUnitOfWork unitOfWork,
        int batchSize = BulkOperationBatchSize,
        CancellationToken cancellationToken = default)
        where TEntity : class
    {
        await entities.BulkOperationInBatchesAsync(
            batch =>
            {
                repository.UpdateRange(batch);
                return Task.CompletedTask;
            },
            unitOfWork,
            batchSize: batchSize,
            cancellationToken);
    }

    /// <summary>
    /// Convenience extension for bulk delete operations
    /// </summary>
    public static async Task BulkDeleteInBatchesAsync<TEntity>(
        this IList<TEntity> entities,
        IBaseRepository<TEntity> repository,
        IUnitOfWork unitOfWork,
        int batchSize = BulkOperationBatchSize,
        CancellationToken cancellationToken = default)
        where TEntity : class
    {
        await entities.BulkOperationInBatchesAsync(
            batch =>
            {
                repository.RemoveRange(batch);
                return Task.CompletedTask;
            },
            unitOfWork,
            batchSize: batchSize,
            cancellationToken);
    }

    /// <summary>
    /// Bulk update specific columns by IDs using repository pattern
    /// </summary>
    /// <typeparam name="TEntity">Entity type</typeparam>
    /// <typeparam name="TKey">Key type</typeparam>
    /// <param name="ids">Collection of entity IDs to update</param>
    /// <param name="repository">Repository instance</param>
    /// <param name="keySelector">Expression to select the key property</param>
    /// <param name="setterExpression">Expression to define what columns to update</param>
    /// <param name="batchSize">Batch size for processing</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of affected rows</returns>
    public static async Task<int> BulkUpdateColumnsByIdsAsync<TEntity, TKey>(
        this IEnumerable<TKey> ids,
        IBaseRepository<TEntity> repository,
        Expression<Func<TEntity, TKey>> keySelector,
        Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> setterExpression,
        int batchSize = BulkOperationBatchSize,
        CancellationToken cancellationToken = default)
        where TEntity : class
        where TKey : notnull
    {
        var idsList = ids.ToList();
        if (!idsList.Any()) return 0;

        var totalAffected = 0;

        for (int i = 0; i < idsList.Count; i += batchSize)
        {
            var batchIds = idsList.Skip(i).Take(batchSize).ToList();

            // Create predicate for this batch of IDs
            var parameter = Expression.Parameter(typeof(TEntity), "x");
            var keyProperty = ((MemberExpression)keySelector.Body).Member;
            var keyAccess = Expression.MakeMemberAccess(parameter, keyProperty);

            // Create: batchIds.Contains(x.KeyProperty)
            var containsMethod = typeof(List<TKey>).GetMethod("Contains")!;
            var containsCall = Expression.Call(Expression.Constant(batchIds), containsMethod, keyAccess);
            var predicate = Expression.Lambda<Func<TEntity, bool>>(containsCall, parameter);

            var affected = await repository.ExecuteUpdateAsync(predicate, setterExpression, cancellationToken);
            totalAffected += affected;
        }

        return totalAffected;
    }
}
