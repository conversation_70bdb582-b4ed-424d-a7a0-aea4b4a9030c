﻿using TMS.SharedKernel.Authentication.Helpers;

namespace TMS.SharedKernel.Authentication;

/// <summary>
/// DefaultQueryParameterHandler
/// </summary>
public class DefaultQueryParameterHandler : DelegatingHandler
{
    private readonly IDictionary<string, string> _defaultParams;

    public DefaultQueryParameterHandler(IDictionary<string, string> defaultParams)
    {
        _defaultParams = defaultParams ?? throw new ArgumentNullException(nameof(defaultParams));
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            var uriBuilder = new UriBuilder(request.RequestUri);
            var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);

            foreach (var kvp in _defaultParams)
            {
                if (!query.AllKeys.Contains(kvp.Key))
                    query[kvp.Key] = kvp.Value;
            }

            uriBuilder.Query = query.ToString();
            request.RequestUri = uriBuilder.Uri;

            return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
        }
        catch
        {
            return HttpResponseHelper.CreateSafeEmptyResponse();
        }
    }
}


