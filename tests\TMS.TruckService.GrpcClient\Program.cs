using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using TMS.TruckService.GrpcClient;
using TMS.TruckService.GrpcClient.Extensions;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("╔════════════════════════════════════════════════╗");
        Console.WriteLine("║   TMS Order Service - gRPC Client Tester      ║");
        Console.WriteLine("╚════════════════════════════════════════════════╝");
        Console.WriteLine();

        // Get server address from args or use default
        var serverAddress = args.Length > 0 ? args[0] : "http://localhost:5000";

        // Build host with dependency injection
        var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // Register the gRPC client
                services.AddOrderServiceClient(serverAddress);
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Warning); // Only show warnings and errors
            })
            .Build();

        // Get the client from DI
        var client = host.Services.GetRequiredService<IOrderServiceClient>();

        // Test connection
        Console.WriteLine($"Connecting to server: {serverAddress}");
        var connected = await client.TestConnectionAsync();

        if (!connected)
        {
            Console.WriteLine("\n⚠ Make sure the gRPC server is running!");
            Console.WriteLine("Start the server with:");
            Console.WriteLine("  cd tests/TMS.TruckService.Api");
            Console.WriteLine("  dotnet run");
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
            return;
        }

        Console.WriteLine();

        // Main menu loop
        while (true)
        {
            DisplayMenu();

            var choice = Console.ReadKey(true).KeyChar;
            Console.WriteLine();

            try
            {
                switch (choice)
                {
                    case '1':
                        await TestScenarios.TestGetOrder(client);
                        break;

                    case '2':
                        await TestScenarios.TestGetNonExistentOrder(client);
                        break;

                    case '3':
                        await TestScenarios.TestCreateOrder(client);
                        break;

                    case '4':
                        await TestScenarios.TestCreateOrderValidation(client);
                        break;

                    case '5':
                        await TestScenarios.TestListOrders(client);
                        break;

                    case '6':
                        await TestScenarios.TestUpdateOrderStatus(client);
                        break;

                    case '7':
                        await TestScenarios.TestDeleteOrder(client);
                        break;

                    case '8':
                        await TestScenarios.TestConcurrentRequests(client);
                        break;

                    case '9':
                        await TestScenarios.RunAllTests(client);
                        break;

                    case 'c':
                    case 'C':
                        await CustomTest(client);
                        break;

                    case 'q':
                    case 'Q':
                        Console.WriteLine("Goodbye!");
                        return;

                    default:
                        Console.WriteLine("Invalid option. Please try again.");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ Unexpected error: {ex.Message}");
            }

            Console.WriteLine("\nPress any key to continue...");
            Console.ReadKey(true);
            Console.Clear();
        }
    }

    static void DisplayMenu()
    {
        Console.WriteLine("╔════════════════════════════════════════════════╗");
        Console.WriteLine("║              Test Menu                         ║");
        Console.WriteLine("╠════════════════════════════════════════════════╣");
        Console.WriteLine("║ 1. Get Existing Order (ID: 1)                  ║");
        Console.WriteLine("║ 2. Get Non-Existent Order (Error Handling)     ║");
        Console.WriteLine("║ 3. Create New Order                            ║");
        Console.WriteLine("║ 4. Create Order - Validation Error             ║");
        Console.WriteLine("║ 5. List Orders (Pagination)                    ║");
        Console.WriteLine("║ 6. Update Order Status                         ║");
        Console.WriteLine("║ 7. Delete Order                                ║");
        Console.WriteLine("║ 8. Concurrent Requests (Performance)           ║");
        Console.WriteLine("║ 9. Run All Tests                               ║");
        Console.WriteLine("║                                                ║");
        Console.WriteLine("║ C. Custom Test                                 ║");
        Console.WriteLine("║ Q. Quit                                        ║");
        Console.WriteLine("╚════════════════════════════════════════════════╝");
        Console.Write("\nSelect an option: ");
    }

    static async Task CustomTest(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Custom Test ===");
        Console.WriteLine("Choose an operation:");
        Console.WriteLine("1. Get Order");
        Console.WriteLine("2. Create Order");
        Console.WriteLine("3. List Orders");
        Console.WriteLine("4. Update Order Status");
        Console.WriteLine("5. Delete Order");

        var choice = Console.ReadKey(true).KeyChar;
        Console.WriteLine();

        switch (choice)
        {
            case '1':
                Console.Write("Enter Order ID: ");
                if (int.TryParse(Console.ReadLine(), out var orderId))
                {
                    var reply = await client.GetOrderAsync(orderId);
                    if (reply.Success)
                    {
                        Console.WriteLine("✓ Order found:");
                        Console.WriteLine($"  Order Number: {reply.Order.OrderNumber}");
                        Console.WriteLine($"  Customer: {reply.Order.CustomerName}");
                        Console.WriteLine($"  Amount: ${reply.Order.Amount}");
                        Console.WriteLine($"  Status: {reply.Order.Status}");
                    }
                    else
                    {
                        Console.WriteLine($"✗ {reply.ErrorMessage}");
                    }
                }
                break;

            case '2':
                Console.Write("Customer Name: ");
                var customerName = Console.ReadLine() ?? "";

                Console.Write("Amount: ");
                if (double.TryParse(Console.ReadLine(), out var amount))
                {
                    Console.Write("Description (optional): ");
                    var description = Console.ReadLine();

                    var reply = await client.CreateOrderAsync(customerName, amount, description);
                    if (reply.Success)
                    {
                        Console.WriteLine($"✓ Order created: {reply.Order.OrderNumber}");
                    }
                    else
                    {
                        Console.WriteLine($"✗ {reply.ErrorMessage}");
                    }
                }
                break;

            case '3':
                Console.Write("Page Number (default 1): ");
                var pageNumStr = Console.ReadLine();
                var pageNum = string.IsNullOrEmpty(pageNumStr) ? 1 : int.Parse(pageNumStr);

                Console.Write("Page Size (default 10): ");
                var pageSizeStr = Console.ReadLine();
                var pageSize = string.IsNullOrEmpty(pageSizeStr) ? 10 : int.Parse(pageSizeStr);

                Console.Write("Status Filter (optional, e.g., Pending, Completed): ");
                var status = Console.ReadLine();

                var listReply = await client.ListOrdersAsync(pageNum, pageSize, status);
                if (listReply.Success)
                {
                    Console.WriteLine($"✓ Found {listReply.TotalCount} orders (showing page {listReply.PageNumber}):");
                    foreach (var order in listReply.Orders)
                    {
                        Console.WriteLine($"  - {order.OrderNumber}: {order.CustomerName} - ${order.Amount} ({order.Status})");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ {listReply.ErrorMessage}");
                }
                break;

            case '4':
                Console.Write("Order ID: ");
                if (int.TryParse(Console.ReadLine(), out var updateOrderId))
                {
                    Console.Write("New Status: ");
                    var newStatus = Console.ReadLine() ?? "";

                    Console.Write("Notes (optional): ");
                    var notes = Console.ReadLine();

                    var reply = await client.UpdateOrderStatusAsync(updateOrderId, newStatus, notes);
                    if (reply.Success)
                    {
                        Console.WriteLine("✓ Order status updated");
                    }
                    else
                    {
                        Console.WriteLine($"✗ {reply.ErrorMessage}");
                    }
                }
                break;

            case '5':
                Console.Write("Order ID to delete: ");
                if (int.TryParse(Console.ReadLine(), out var deleteOrderId))
                {
                    Console.Write($"Are you sure you want to delete order {deleteOrderId}? (y/n): ");
                    if (Console.ReadKey().KeyChar == 'y')
                    {
                        Console.WriteLine();
                        var reply = await client.DeleteOrderAsync(deleteOrderId);
                        if (reply.Success)
                        {
                            Console.WriteLine("✓ Order deleted");
                        }
                        else
                        {
                            Console.WriteLine($"✗ {reply.ErrorMessage}");
                        }
                    }
                }
                break;

            default:
                Console.WriteLine("Invalid option");
                break;
        }
    }
}
