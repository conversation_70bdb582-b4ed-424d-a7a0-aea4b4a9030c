﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.TruckService.Domain.Events;

namespace TMS.TruckService.Application.Events;

public class OrderCreatedHandler : IEventHandler<OrderCreatedEvent>
{
    private readonly ILogger<OrderCreatedHandler> _logger;
    private readonly IEventPublisher _eventPublisher;

    public OrderCreatedHandler(ILogger<OrderCreatedHandler> logger, [FromKeyedServices(Constants.RabbitMqCompanyEvents)] IEventPublisher eventPublisher)
    {
        _logger = logger;
        _eventPublisher = eventPublisher;
    }

    /// <summary>
    /// process order + publish payment event
    /// </summary>
    /// <param name="eventData"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<bool> HandleAsync(OrderCreatedEvent eventData, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing order {OrderId} for {CustomerName}",
                eventData.OrderId, eventData.CustomerName);

            // Simulate order processing
            await Task.Delay(2000, cancellationToken);

            // Publish follow-up event
            var paymentEvent = new PaymentProcessedEvent
            {
                PaymentId = Guid.NewGuid().ToString(),
                OrderId = eventData.OrderId,
                Amount = eventData.Amount,
                PaymentMethod = "CreditCard"
            };

            await _eventPublisher.PublishAsync(paymentEvent, cancellationToken: cancellationToken);

            _logger.LogInformation("Order {OrderId} processed successfully", eventData.OrderId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process order {OrderId}", eventData.OrderId);
            return false;
        }
    }
}
