﻿using TMS.SharedKernel.Constants;

namespace TMS.SharedKernel.Constants.Extensions;

/// <summary>
/// Common messages shared across all bounded contexts and microservices
/// </summary>
public static class MessageFormatter
{
    public static string FormatNotFound(string entityName, object id)
        => string.Format(CommonMessages.EntityNotFound, entityName, id);

    public static string FormatAlreadyExists(string entityName, object id)
        => string.Format(CommonMessages.EntityAlreadyExists, entityName, id);

    public static string FormatValidationError(string fieldName, string error)
        => string.Format(ValidationMessages.InvalidValue, fieldName, error);

    public static string FormatOperationResult(string operation, bool success, string details = null)
        => success
            ? string.Format(CommonMessages.OperationSuccessful, operation)
            : string.Format(CommonMessages.OperationFailed, operation, details);
}
