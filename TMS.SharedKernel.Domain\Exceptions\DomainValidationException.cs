﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TMS.SharedKernel.Domain.Exceptions;

#region Domain Validation Exceptions

/// <summary>
/// Base exception for all domain validation errors
/// </summary>
public abstract class DomainValidationException : Exception
{
    public Dictionary<string, string[]> ValidationErrors { get; }
    public string? ErrorCode { get; }

    /// <summary>
    /// DomainValidationException
    /// </summary>
    /// <param name="message"></param>
    /// <param name="errorCode"></param>
    protected DomainValidationException(string message, string? errorCode = null) : base(message)
    {
        ValidationErrors = new Dictionary<string, string[]>();
        ErrorCode = errorCode;
    }

    /// <summary>
    /// DomainValidationException
    /// </summary>
    /// <param name="message"></param>
    /// <param name="innerException"></param>
    /// <param name="errorCode"></param>
    protected DomainValidationException(string message, Exception innerException, string? errorCode = null) : base(message, innerException)
    {
        ValidationErrors = new Dictionary<string, string[]>();
        ErrorCode = errorCode;
    }

    /// <summary>
    /// DomainValidationException
    /// </summary>
    /// <param name="field"></param>
    /// <param name="error"></param>
    /// <param name="errorCode"></param>
    protected DomainValidationException(string field, string error, string? errorCode = null) : base($"Validation failed for {field}: {error}")
    {
        ValidationErrors = new Dictionary<string, string[]>
        {
            { field, new[] { error } }
        };
        ErrorCode = errorCode;
    }

    /// <summary>
    /// DomainValidationException
    /// </summary>
    /// <param name="validationErrors"></param>
    /// <param name="errorCode"></param>
    protected DomainValidationException(Dictionary<string, string[]> validationErrors, string? errorCode = null)
        : base("One or more domain validation errors occurred")
    {
        ValidationErrors = validationErrors ?? new Dictionary<string, string[]>();
        ErrorCode = errorCode;
    }
}

/// <summary>
/// Exception for business rule validation failures
/// </summary>
public class BusinessRuleValidationException : DomainValidationException
{
    public BusinessRuleValidationException(string message, string? errorCode = null) : base(message, errorCode) { }
    public BusinessRuleValidationException(string field, string error, string? errorCode = null) : base(field, error, errorCode) { }
    public BusinessRuleValidationException(Dictionary<string, string[]> validationErrors, string? errorCode = null) : base(validationErrors, errorCode) { }
}

/// <summary>
/// Exception for entity state validation failures
/// </summary>
public class EntityValidationException : DomainValidationException
{
    public string EntityType { get; }

    public EntityValidationException(string entityType, string message, string? errorCode = null) : base(message, errorCode)
    {
        EntityType = entityType;
    }

    public EntityValidationException(string entityType, string field, string error, string? errorCode = null) : base(field, error, errorCode)
    {
        EntityType = entityType;
    }

    public EntityValidationException(string entityType, Dictionary<string, string[]> validationErrors, string? errorCode = null) : base(validationErrors, errorCode)
    {
        EntityType = entityType;
    }
}

/// <summary>
/// Exception for aggregate root validation failures
/// </summary>
public class AggregateValidationException : DomainValidationException
{
    public string AggregateId { get; }
    public string AggregateType { get; }

    public AggregateValidationException(string aggregateType, string aggregateId, string message, string? errorCode = null) : base(message, errorCode)
    {
        AggregateType = aggregateType;
        AggregateId = aggregateId;
    }

    public AggregateValidationException(string aggregateType, string aggregateId, Dictionary<string, string[]> validationErrors, string? errorCode = null) : base(validationErrors, errorCode)
    {
        AggregateType = aggregateType;
        AggregateId = aggregateId;
    }
}

#endregion
