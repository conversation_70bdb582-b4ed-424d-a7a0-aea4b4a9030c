﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.7.5</Version>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="errorCodes.js" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Elyspio.Utils.Telemetry.MongoDB" Version="1.0.1" />
    <PackageReference Include="Elyspio.Utils.Telemetry.Redis" Version="1.0.1" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
    <PackageReference Include="DotNetCore.CAP" Version="8.3.5" />
    <PackageReference Include="OpenTelemetry" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Api" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Process" Version="1.12.0-beta.1" />
    <!--<PackageReference Include="OpenTelemetry.Instrumentation.SqlClient" Version="1.12.0" />-->
    <PackageReference Include="Npgsql.OpenTelemetry" Version="9.0.3" />
    <!--<PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.12.0-beta.2" />-->

    <PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="Polly" Version="8.6.4" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="Quartz" Version="3.15.0" />
    <PackageReference Include="Quartz.Extensions.DependencyInjection" Version="3.15.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.15.0" />
    <PackageReference Include="Quartz.Serialization.Json" Version="3.15.0" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />

    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.14.0" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.SharedKernel.EntityFrameworkCore\TMS.SharedKernel.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\TMS.SharedKernel.Constants\TMS.SharedKernel.Constants.csproj" />
  </ItemGroup>

</Project>
