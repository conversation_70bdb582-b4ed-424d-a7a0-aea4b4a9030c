using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.Domain.Tests.TestEntities;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.SharedKernel.Domain.Tests.TestDbContext;

/// <summary>
/// Test DbContext for verifying tenant isolation behavior
/// </summary>
public class TestTenantDbContext : BaseDbContext
{
    public TestTenantDbContext(
        DbContextOptions<TestTenantDbContext> options,
        ICurrentFactorProvider currentFactorProvider)
        : base(options, currentFactorProvider)
    {
    }

    public DbSet<TenantScopedEntity> TenantScopedEntities { get; set; }
    public DbSet<SharedEntity> SharedEntities { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure TenantScopedEntity
        modelBuilder.Entity<TenantScopedEntity>(entity =>
        {
            entity.ToTable("TenantScopedEntities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CompanyId).IsRequired();
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
        });

        // Configure SharedEntity
        modelBuilder.Entity<SharedEntity>(entity =>
        {
            entity.ToTable("SharedEntities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
        });
    }
}
