﻿
namespace TMS.SharedKernal.Kafka.Services;

using System.Collections.Concurrent;
using System.Diagnostics;
using Confluent.Kafka;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.Kafka.Configuration;

public class KafkaMessageProducer : IMessageProducer, IDisposable
{
    private static readonly ActivitySource ActivitySource = new("TMS.Kafka.Producer");
    private static readonly TextMapPropagator Propagator = Propagators.DefaultTextMapPropagator;

    private readonly ConcurrentDictionary<string, IProducer<string, string>> _producers;
    private readonly IMessageSerializer _serializer;
    private readonly ILogger<KafkaMessageProducer> _logger;
    private readonly KafkaFlowOptions _options;
    private readonly Dictionary<Type, string> _messageTopicMapping;

    public KafkaMessageProducer(
        KafkaFlowOptions options,
        IMessageSerializer serializer,
        ILogger<KafkaMessageProducer> logger)
    {
        _options = options;
        _serializer = serializer;
        _logger = logger;
        _producers = new ConcurrentDictionary<string, IProducer<string, string>>();
        _messageTopicMapping = CreateMessageTopicMapping();
    }

    private Dictionary<Type, string> CreateMessageTopicMapping()
    {
        return _options.Topics.ToDictionary(t => t.MessageType, t => t.TopicName);
    }

    private IProducer<string, string> GetOrCreateProducer(string topic)
    {
        return _producers.GetOrAdd(topic, _ =>
        {
            var config = new ProducerConfig
            {
                BootstrapServers = _options.BootstrapServers,
                Acks = Acks.All,
                MessageSendMaxRetries = _options.Producer.MessageSendMaxRetries,
                MessageTimeoutMs = _options.Producer.MessageTimeoutMs,
                EnableIdempotence = _options.Producer.EnableIdempotence,
                CompressionType = Enum.Parse<CompressionType>(_options.Producer.CompressionType, true),
                BatchSize = _options.Producer.BatchSize,
                LingerMs = _options.Producer.LingerMs,
                RequestTimeoutMs = _options.Producer.RequestTimeoutMs,
                //DeliveryTimeoutMs = _options.Producer.DeliveryTimeoutMs
            };

            // Apply security configuration
            ApplySecurityConfig(config, _options.Security);

            return new ProducerBuilder<string, string>(config)
                .SetErrorHandler((_, e) => _logger.LogError($"Producer error for topic {topic}: {e.Reason}"))
                .Build();
        });
    }

    private static void ApplySecurityConfig(ClientConfig config, SecurityOptions security)
    {
        // Configure SASL Authentication
        if (security.Sasl.Enabled)
        {
            config.SecurityProtocol = security.EnableSsl ? SecurityProtocol.SaslSsl : SecurityProtocol.SaslPlaintext;
            config.SaslMechanism = Enum.Parse<SaslMechanism>(security.Sasl.Mechanism, true);
            config.SaslUsername = security.Sasl.Username;
            config.SaslPassword = security.Sasl.Password;
        }
        // Configure SSL only
        else if (security.EnableSsl)
        {
            config.SecurityProtocol = SecurityProtocol.Ssl;
        }

        // Configure SSL settings
        if (security.EnableSsl)
        {
            if (!string.IsNullOrEmpty(security.Ssl.CaLocation))
                config.SslCaLocation = security.Ssl.CaLocation;

            if (!string.IsNullOrEmpty(security.Ssl.CertificateLocation))
                config.SslCertificateLocation = security.Ssl.CertificateLocation;

            if (!string.IsNullOrEmpty(security.Ssl.KeyLocation))
                config.SslKeyLocation = security.Ssl.KeyLocation;

            if (!string.IsNullOrEmpty(security.Ssl.KeyPassword))
                config.SslKeyPassword = security.Ssl.KeyPassword;

            config.EnableSslCertificateVerification = security.Ssl.VerifyHostname;
        }
    }

    public async Task ProduceAsync<T>(string topic, T message, string key = null, CancellationToken cancellationToken = default)
        where T : class, IMessage
    {
        // Start an activity for this publish operation with proper naming convention
        using var activity = ActivitySource.StartActivity($"{topic} publish", ActivityKind.Producer);

        try
        {
            var producer = GetOrCreateProducer(topic);
            var serializedMessage = _serializer.Serialize(message);

            var kafkaMessage = new Message<string, string>
            {
                Key = key ?? message.Id,
                Value = serializedMessage,
                Timestamp = new Timestamp(message.Timestamp),
                Headers = new Headers
                {
                    { "MessageType", System.Text.Encoding.UTF8.GetBytes(typeof(T).Name) },
                    { "MessageId", System.Text.Encoding.UTF8.GetBytes(message.Id) }
                }
            };

            // Inject trace context into message headers for distributed tracing
            var propagationContext = activity?.Context ?? Activity.Current?.Context ?? default;
            Propagator.Inject(new PropagationContext(propagationContext, Baggage.Current), kafkaMessage.Headers, InjectTraceContext);

            // Add OpenTelemetry messaging semantic convention tags (v1.21.0)
            // Reference: https://opentelemetry.io/docs/specs/semconv/messaging/kafka/
            activity?.SetTag("messaging.system", "kafka");
            activity?.SetTag("messaging.destination.name", topic);
            activity?.SetTag("messaging.destination.kind", "topic");
            activity?.SetTag("messaging.operation", "publish");
            activity?.SetTag("messaging.message.id", message.Id);
            activity?.SetTag("messaging.conversation_id", message.Id);
            activity?.SetTag("messaging.message.payload_size_bytes", System.Text.Encoding.UTF8.GetByteCount(serializedMessage));
            activity?.SetTag("messaging.client_id", producer.Name);
            activity?.SetTag("messaging.kafka.message.key", kafkaMessage.Key);
            activity?.SetTag("net.peer.name", _options.BootstrapServers);

            // Add peer.service for service map visualization in Signoz
            activity?.SetTag("peer.service", "kafka");

            // Custom tags
            activity?.SetTag("messaging.message_type", typeof(T).Name);

            var result = await producer.ProduceAsync(topic, kafkaMessage, cancellationToken);

            // Add result tags after production
            activity?.SetTag("messaging.destination.partition.id", result.Partition.Value);
            activity?.SetTag("messaging.kafka.destination.partition", result.Partition.Value);
            activity?.SetTag("messaging.kafka.message.offset", result.Offset.Value);

            _logger.LogInformation($"Message {message.Id} produced to {result.Topic}[{result.Partition}] at offset {result.Offset}");
        }
        catch (ProduceException<string, string> ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("exception.type", ex.GetType().FullName);
            activity?.SetTag("exception.message", ex.Message);
            activity?.SetTag("exception.stacktrace", ex.StackTrace);

            _logger.LogError(ex, $"Failed to produce message {message.Id} to topic {topic}");
            throw;
        }
    }

    private static void InjectTraceContext(Headers headers, string key, string value)
    {
        headers.Add(key, System.Text.Encoding.UTF8.GetBytes(value));
    }

    public async Task ProduceAsync<T>(T message, string key = null, CancellationToken cancellationToken = default)
        where T : class, IMessage
    {
        if (!_messageTopicMapping.TryGetValue(typeof(T), out var topic))
        {
            throw new InvalidOperationException($"No topic mapping found for message type {typeof(T).Name}");
        }

        await ProduceAsync(topic, message, key, cancellationToken);
    }

    public async Task ProduceBatchAsync<T>(string topic, IEnumerable<T> messages, CancellationToken cancellationToken = default)
        where T : class, IMessage
    {
        var producer = GetOrCreateProducer(topic);
        var tasks = messages.Select(message => ProduceAsync(topic, message, null, cancellationToken));

        await Task.WhenAll(tasks);
        _logger.LogInformation($"Batch of {tasks.Count()} messages produced to {topic}");
    }

    public void Dispose()
    {
        foreach (var producer in _producers.Values)
        {
            producer?.Flush(TimeSpan.FromSeconds(10));
            producer?.Dispose();
        }
        _producers.Clear();
    }
}
