﻿using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.SmoothRedis;
using TMS.TruckService.Domain.EventsKafka;

namespace TMS.TruckService.Application.Features.OrdersKafka;

public class OrderCreatedEventKafkaHandler : IMessageHandler<OrderCreatedEventKafka>
{
    private readonly ILogger<OrderCreatedEventKafkaHandler> _logger;
    private readonly ISmoothRedis _redis;
    private readonly IMessageProducer _messageProducer;

    public OrderCreatedEventKafkaHandler(ISmoothRedis redis, 
        ILogger<OrderCreatedEventKafkaHandler> logger,
        [FromKeyedServices(Constants.KafkaFlow02)] IMessageProducer messageProducer)
    {
        _logger = logger;
        _redis = redis;
        _messageProducer = messageProducer;
    }

    public async Task HandleAsync(OrderCreatedEventKafka message, MessageContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation($"Processing order {message.OrderId} for customer {message.CustomerId}");
        var req = await _redis.Cache.GetAsync("pairing:truckId:driverId");
        _logger.LogInformation($"Retrieved pairing from Redis: {req}");

        // Process payment, reserve inventory, etc.
        await Task.Delay(200, cancellationToken);

        await _messageProducer.ProduceAsync(new PaymentProcessedEventKafka
        {
            OrderId = message.OrderId,
            CustomerId = message.CustomerId,
            TotalAmount = message.TotalAmount,
        });

        _logger.LogInformation($"Order {message.OrderId} processing completed");
    }
}
