using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernel.Domain.Events.AuditLog;

namespace TMS.SharedKernel.Domain.Tests.Mocks;

/// <summary>
/// Mock implementation of IMessageProducer for testing audit log interceptor
/// </summary>
public class MockMessageProducer : IMessageProducer
{
    public List<AuditLogMessage> PublishedMessages { get; } = new();

    public Task ProduceAsync<T>(string topic, T message, string? key = null, CancellationToken cancellationToken = default) where T : class, IMessage
    {
        if (message is AuditLogMessage auditLog)
        {
            PublishedMessages.Add(auditLog);
        }
        return Task.CompletedTask;
    }

    public Task ProduceAsync<T>(T message, string? key = null, CancellationToken cancellationToken = default) where T : class, IMessage
    {
        if (message is AuditLogMessage auditLog)
        {
            PublishedMessages.Add(auditLog);
        }
        return Task.CompletedTask;
    }

    public Task ProduceBatchAsync<T>(string topic, IEnumerable<T> messages, CancellationToken cancellationToken = default) where T : class, IMessage
    {
        foreach (var message in messages)
        {
            if (message is AuditLogMessage auditLog)
            {
                PublishedMessages.Add(auditLog);
            }
        }
        return Task.CompletedTask;
    }

    public void Clear()
    {
        PublishedMessages.Clear();
    }
}
