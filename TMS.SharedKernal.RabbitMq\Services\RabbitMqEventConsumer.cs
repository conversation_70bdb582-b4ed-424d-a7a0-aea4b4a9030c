﻿using System.Diagnostics;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.SharedKernal.RabbitMq.Configuration;
using TMS.SharedKernal.RabbitMq.Serialization;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace TMS.SharedKernal.RabbitMq.Services;

/// <summary>
/// RabbitMqEventConsumer
/// </summary>
public class RabbitMqEventConsumer : IEventConsumer, IDisposable
{
    private static readonly ActivitySource ActivitySource = new("TMS.RabbitMQ.Consumer");
    private static readonly TextMapPropagator Propagator = Propagators.DefaultTextMapPropagator;

    private readonly IConnection _connection;
    private readonly IModel _channel;
    private readonly RabbitMqOptions _options;
    private readonly IEventSerializer _serializer;
    private readonly ILogger<RabbitMqEventConsumer> _logger;
    private readonly List<string> _consumerTags = new();

    /// <summary>
    /// RabbitMqEventConsumer
    /// </summary>
    /// <param name="options"></param>
    /// <param name="serializer"></param>
    /// <param name="logger"></param>
    public RabbitMqEventConsumer(
        RabbitMqOptions options,
        IEventSerializer serializer,
        ILogger<RabbitMqEventConsumer> logger)
    {
        _options = options;
        _serializer = serializer;
        _logger = logger;

        var factory = new ConnectionFactory()
        {
            HostName = _options.HostName,
            Port = _options.Port,
            UserName = _options.UserName,
            Password = _options.Password,
            VirtualHost = _options.VirtualHost
        };

        _connection = factory.CreateConnection();
        _channel = _connection.CreateModel();

        SetupQueues();
    }

    /// <summary>
    /// SetupQueues
    /// </summary>
    private void SetupQueues()
    {
        var exchangeType = _options.Exchange.Type switch
        {
            Configuration.ExchangeType.Direct => global::RabbitMQ.Client.ExchangeType.Direct,
            Configuration.ExchangeType.Topic => global::RabbitMQ.Client.ExchangeType.Topic,
            Configuration.ExchangeType.Fanout => global::RabbitMQ.Client.ExchangeType.Fanout,
            Configuration.ExchangeType.Headers => global::RabbitMQ.Client.ExchangeType.Headers,
            _ => global::RabbitMQ.Client.ExchangeType.Topic
        };

        _channel.ExchangeDeclare(_options.Exchange.Name, exchangeType, _options.Exchange.Durable);

        foreach (var queueConfig in _options.Exchange.Queues.Values)
        {
            _channel.QueueDeclare(
                queue: queueConfig.QueueName,
                durable: queueConfig.Durable,
                exclusive: queueConfig.Exclusive,
                autoDelete: queueConfig.AutoDelete,
                arguments: queueConfig.Arguments
            );

            _channel.QueueBind(queueConfig.QueueName, _options.Exchange.Name, queueConfig.RoutingKey);
            _logger.LogInformation("Queue {QueueName} bound to routing key {RoutingKey}",
                queueConfig.QueueName, queueConfig.RoutingKey);
        }
    }

    /// <summary>
    /// StartConsuming
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="queueName"></param>
    /// <param name="messageHandler"></param>
    public void StartConsuming<T>(string queueName, Func<T, Task<bool>> messageHandler) where T : class, IEvent
    {
        StartConsuming(queueName, async (json, eventType) =>
        {
            try
            {
                var eventData = _serializer.Deserialize<T>(json);
                if (eventData == null) return false;

                return await messageHandler(eventData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to deserialize event of type {EventType}", eventType);
                return false;
            }
        });
    }

    /// <summary>
    /// StartConsuming
    /// </summary>
    /// <param name="queueName"></param>
    /// <param name="messageHandler"></param>
    /// <exception cref="ArgumentException"></exception>
    public void StartConsuming(string queueName, Func<string, string, Task<bool>> messageHandler)
    {
        if (!_options.Exchange.Queues.TryGetValue(queueName, out var queueConfig))
        {
            throw new ArgumentException($"Queue configuration for '{queueName}' not found");
        }

        var consumer = new EventingBasicConsumer(_channel);

        consumer.Received += async (model, ea) =>
        {
            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            var eventType = ea.BasicProperties.Type ?? "Unknown";
            var messageId = ea.BasicProperties.MessageId ?? "Unknown";

            // Extract trace context from message headers
            var parentContext = Propagator.Extract(default, ea.BasicProperties.Headers, ExtractTraceContext);
            Baggage.Current = parentContext.Baggage;

            // Start a new activity as a child of the extracted context with proper naming convention
            using var activity = ActivitySource.StartActivity($"{queueConfig.QueueName} process", ActivityKind.Consumer, parentContext.ActivityContext);

            try
            {
                // Add OpenTelemetry messaging semantic convention tags
                activity?.SetTag("messaging.system", "rabbitmq");
                activity?.SetTag("messaging.destination.name", queueConfig.QueueName);
                activity?.SetTag("messaging.destination.kind", "queue");
                activity?.SetTag("messaging.operation", "process");
                activity?.SetTag("messaging.message.id", messageId);
                activity?.SetTag("messaging.message.conversation_id", messageId);
                activity?.SetTag("messaging.rabbitmq.destination.routing_key", ea.RoutingKey);
                activity?.SetTag("net.peer.name", _options.HostName);
                activity?.SetTag("net.peer.port", _options.Port);

                // Custom tags
                activity?.SetTag("messaging.event_type", eventType);
                activity?.SetTag("messaging.message.body.size", body.Length);

                _logger.LogDebug("Received event {EventType} with ID {MessageId} on queue {QueueName}",
                    eventType, messageId, queueConfig.QueueName);

                bool processed = await messageHandler(message, eventType);

                if (processed)
                {
                    // Successfully processed - acknowledge the message
                    _channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);

                    _logger.LogInformation("Successfully processed event {EventType} with ID {MessageId}",
                        eventType, messageId);
                }
                else
                {
                    // Processing failed - reject the message without requeuing
                    _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: false);

                    activity?.SetStatus(ActivityStatusCode.Error, "Message processing returned false");

                    _logger.LogWarning("Failed to process event {EventType} with ID {MessageId}, requeued=false",
                        eventType, messageId);
                }
            }
            catch (Exception ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                activity?.SetTag("exception.type", ex.GetType().FullName);
                activity?.SetTag("exception.message", ex.Message);
                activity?.SetTag("exception.stacktrace", ex.StackTrace);

                _logger.LogError(ex, "Error processing event {EventType} with ID {MessageId}",
                    eventType, messageId);

                // On exception, reject the message without requeuing to avoid infinite loops
                _channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, requeue: false);
            }
        };

        _channel.BasicQos(prefetchSize: 0, prefetchCount: (ushort)queueConfig.PrefetchCount, global: false);

        var consumerTag = _channel.BasicConsume(
            queue: queueConfig.QueueName,
            autoAck: false,
            consumer: consumer
        );

        _consumerTags.Add(consumerTag);
        _logger.LogInformation("Started consuming from queue {QueueName}", queueConfig.QueueName);
    }

    private static IEnumerable<string> ExtractTraceContext(IDictionary<string, object>? headers, string key)
    {
        if (headers == null || !headers.TryGetValue(key, out var value))
        {
            return Enumerable.Empty<string>();
        }

        if (value is byte[] bytes)
        {
            return new[] { Encoding.UTF8.GetString(bytes) };
        }

        return value is string str ? new[] { str } : Enumerable.Empty<string>();
    }

    /// <summary>
    /// StopConsuming
    /// </summary>
    public void StopConsuming()
    {
        foreach (var consumerTag in _consumerTags)
        {
            _channel.BasicCancel(consumerTag);
        }
        _consumerTags.Clear();
        _logger.LogInformation("Stopped all consumers");
    }

    /// <summary>
    /// Dispose
    /// </summary>
    public void Dispose()
    {
        StopConsuming();
        _channel?.Dispose();
        _connection?.Dispose();
    }
}
