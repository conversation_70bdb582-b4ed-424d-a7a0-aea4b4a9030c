using Confluent.Kafka;
using Confluent.Kafka.Admin;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.Kafka.Configuration;

namespace TMS.SharedKernal.Kafka.Services;

/// <summary>
/// Manages Kafka topics including auto-creation
/// </summary>
public class KafkaTopicManager : ITopicManager
{
    private readonly KafkaFlowOptions _options;
    private readonly ILogger<KafkaTopicManager> _logger;
    private readonly IAdminClient _adminClient;

    public KafkaTopicManager(
        KafkaFlowOptions options,
        ILogger<KafkaTopicManager> logger)
    {
        _options = options;
        _logger = logger;

        var adminConfig = new AdminClientConfig
        {
            BootstrapServers = _options.BootstrapServers,
            // Add security configuration if needed
        };

        ApplySecurityConfig(adminConfig, _options.Security);

        _adminClient = new AdminClientBuilder(adminConfig).Build();
    }

    public async Task<bool> EnsureTopicExistsAsync(
        string topicName,
        int partitions = 1,
        short replicationFactor = 1,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if topic exists
            var exists = await TopicExistsAsync(topicName, cancellationToken);
            if (exists)
            {
                _logger.LogDebug($"Topic '{topicName}' already exists");
                return true;
            }

            _logger.LogInformation($"Creating topic '{topicName}' with {partitions} partitions and replication factor {replicationFactor}");

            var topicSpec = new TopicSpecification
            {
                Name = topicName,
                NumPartitions = partitions,
                ReplicationFactor = replicationFactor,
                Configs = new Dictionary<string, string>
                {
                    // Retention settings (7 days default)
                    { "retention.ms", "604800000" },
                    // Enable compression
                    { "compression.type", "snappy" },
                    // Minimum in-sync replicas (for acks=all)
                    { "min.insync.replicas", Math.Min((short)1, replicationFactor).ToString() }
                }
            };

            await _adminClient.CreateTopicsAsync(new[] { topicSpec });

            _logger.LogInformation($"Successfully created topic '{topicName}'");
            return true;
        }
        catch (CreateTopicsException ex)
        {
            // Check if error is because topic already exists
            foreach (var result in ex.Results)
            {
                if (result.Error.Code == ErrorCode.TopicAlreadyExists)
                {
                    _logger.LogInformation($"Topic '{topicName}' already exists (concurrent creation)");
                    return true;
                }
            }

            _logger.LogError(ex, $"Failed to create topic '{topicName}': {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Unexpected error creating topic '{topicName}'");
            return false;
        }
    }

    public async Task CreateAllTopicsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting automatic topic creation...");

        var topicsToCreate = new List<(string name, int partitions, short replicationFactor)>();

        // 1. Create main topics
        foreach (var topicConfig in _options.Topics)
        {
            if (topicConfig.AutoCreateTopic)
            {
                topicsToCreate.Add((
                    topicConfig.TopicName,
                    topicConfig.Partitions,
                    topicConfig.ReplicationFactor
                ));

                // Also create retry topic for each main topic
                var retryTopicName = $"{topicConfig.TopicName}.retry";
                topicsToCreate.Add((
                    retryTopicName,
                    topicConfig.Partitions,
                    topicConfig.ReplicationFactor
                ));
            }
        }

        // 2. Create DLQ topic if enabled
        if (_options.DeadLetter.Enabled)
        {
            topicsToCreate.Add((
                _options.DeadLetter.TopicName,
                1, // DLQ typically needs only 1 partition
                1  // DLQ can have lower replication
            ));
        }

        // 3. Create all topics
        var creationTasks = topicsToCreate
            .Distinct()
            .Select(t => EnsureTopicExistsAsync(t.name, t.partitions, t.replicationFactor, cancellationToken));

        var results = await Task.WhenAll(creationTasks);

        var successCount = results.Count(r => r);
        var failureCount = results.Length - successCount;

        if (failureCount > 0)
        {
            _logger.LogWarning($"Topic creation completed with {successCount} successes and {failureCount} failures");
        }
        else
        {
            _logger.LogInformation($"Successfully created/verified {successCount} topics");
        }
    }

    public async Task<bool> TopicExistsAsync(string topicName, CancellationToken cancellationToken = default)
    {
        try
        {
            var metadata = await GetTopicMetadataAsync(topicName, cancellationToken);
            return metadata != null;
        }
        catch
        {
            return false;
        }
    }

    public async Task<TopicMetadata?> GetTopicMetadataAsync(string topicName, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get cluster metadata
            var metadata = _adminClient.GetMetadata(topicName, TimeSpan.FromSeconds(10));

            var topicMeta = metadata.Topics.FirstOrDefault(t => t.Topic == topicName);
            if (topicMeta == null || topicMeta.Error.Code != ErrorCode.NoError)
            {
                return null;
            }

            return new TopicMetadata
            {
                TopicName = topicName,
                Partitions = topicMeta.Partitions.Count,
                ReplicationFactor = (short)(topicMeta.Partitions.FirstOrDefault()?.Replicas?.Length ?? 0),
                Config = new Dictionary<string, string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, $"Topic '{topicName}' does not exist or is not accessible");
            return null;
        }
    }

    private static void ApplySecurityConfig(ClientConfig config, SecurityOptions security)
    {
        // Configure SASL Authentication
        if (security.Sasl.Enabled)
        {
            config.SecurityProtocol = security.EnableSsl ? SecurityProtocol.SaslSsl : SecurityProtocol.SaslPlaintext;
            config.SaslMechanism = Enum.Parse<SaslMechanism>(security.Sasl.Mechanism, true);
            config.SaslUsername = security.Sasl.Username;
            config.SaslPassword = security.Sasl.Password;
        }
        // Configure SSL only
        else if (security.EnableSsl)
        {
            config.SecurityProtocol = SecurityProtocol.Ssl;
        }

        // Configure SSL settings
        if (security.EnableSsl)
        {
            if (!string.IsNullOrEmpty(security.Ssl.CaLocation))
                config.SslCaLocation = security.Ssl.CaLocation;

            if (!string.IsNullOrEmpty(security.Ssl.CertificateLocation))
                config.SslCertificateLocation = security.Ssl.CertificateLocation;

            if (!string.IsNullOrEmpty(security.Ssl.KeyLocation))
                config.SslKeyLocation = security.Ssl.KeyLocation;

            if (!string.IsNullOrEmpty(security.Ssl.KeyPassword))
                config.SslKeyPassword = security.Ssl.KeyPassword;

            config.EnableSslCertificateVerification = security.Ssl.VerifyHostname;
        }
    }

    public void Dispose()
    {
        _adminClient?.Dispose();
    }
}
