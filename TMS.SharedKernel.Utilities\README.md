# TMS.SharedKernel.Utilities

Common utilities and extension methods for TMS SharedKernel projects.

## Features

- **Logging Extensions**: Serilog and OpenTelemetry integration
- **Exception Handling**: Global exception handler with structured responses
- **MediatR Extensions**: Easy MediatR registration and configuration
- **AutoMapper Extensions**: Simplified AutoMapper setup
- **API Versioning**: Built-in API versioning support
- **CORS Extensions**: Environment-aware CORS configuration
- **Infrastructure Services**: Repository and Unit of Work registration

## Usage

### Basic Setup
```csharp
var builder = WebApplication.CreateBuilder(args);

// Add logging
builder.AddTMSLogger();

// Add exception handling
builder.AddExceptionHandler();

// Add MediatR
builder.AddMediatR(typeof(Program));

// Add AutoMapper
builder.AddAutoMapper<Program>();

// Add API versioning
builder.Services.AddApiVersioningAndExplorer();

// Add CORS
builder.AddCors("https://localhost:3000");

// Add repositories
builder.Services.AddRepositories();

var app = builder.Build();

// Use CORS
app.UseCors();
```

### Logging Extensions
```csharp
// Structured logging with high performance
logger.LogKafkaServicesRegistered();
logger.LogServicesRegistered("MyService");
```

### Enum Extensions
```csharp
public enum Status
{
    [Description("Active Status")]
    Active,
    [Description("Inactive Status")]
    Inactive
}

// Usage
var description = Status.Active.GetDescription(); // "Active Status"
var allDescriptions = EnumExtensions.GetDescriptions<Status>();
```