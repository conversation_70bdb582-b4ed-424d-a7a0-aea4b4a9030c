﻿namespace TMS.SharedKernel.Domain.Events.Interfaces;

/// <summary>
/// Base interface for the event messages to follow the lightweight Kafka convention.
/// For more details, see the
/// </summary>
[Obsolete("Use the new IEventMessage interface in TMS.SharedKernel.MessageService package instead.")]
public interface IEventMessage : IEventMessage<string>
{
}

/// <summary>
/// Generic interface for lightweight event messages.
/// </summary>
/// <typeparam name="TK<PERSON>">The type of the key for the event message.</typeparam>
[Obsolete("Use the new IEventMessage interface in TMS.SharedKernel.MessageService package instead.")]
public interface IEventMessage<TKey> where TKey : IEquatable<TKey>
{
    /// <summary>
    /// Gets or sets the item identifier.
    /// </summary>
    TKey ItemId { get; set; }

    /// <summary>
    /// Gets or sets the action performed on the item.
    /// </summary>
    string Action { get; set; }

    /// <summary>
    /// Gets or sets the timestamp of the event.
    /// </summary>
    DateTime Timestamp { get; set; }

    /// <summary>
    /// Gets or sets the version of the event message.
    /// </summary>
    string Version { get; set; }
}
