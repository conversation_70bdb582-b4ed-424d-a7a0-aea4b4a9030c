﻿using Asp.Versioning;
using Microsoft.Extensions.DependencyInjection;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for adding API versioning and API explorer services.
/// </summary>
public static class ApiVersioningExtensions
{
    /// <summary>
    /// Add Api versioning to the specified WebApplicationBuilder.
    /// </summary>
    /// <param name="services">The IServiceCollection to add the services to.</param>
    /// <returns>The updated IServiceCollection.</returns>
    public static IServiceCollection AddApiVersioningAndExplorer(this IServiceCollection services)
    {
        services.AddApiVersioning(options =>
        {
            options.DefaultApiVersion = new ApiVersion(1, 0);
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.ReportApiVersions = true;
            options.ApiVersionReader = new UrlSegmentApiVersionReader();
        }).AddApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'VVV";
            options.SubstituteApiVersionInUrl = true;
        });

        return services;
    }
}
