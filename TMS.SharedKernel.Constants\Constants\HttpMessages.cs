﻿namespace TMS.SharedKernel.Constants;

/// <summary>
/// Common messages shared across all bounded contexts and microservices
/// </summary>
public static class HttpMessages
{
    // 2xx Success
    public const string Ok = "Request processed successfully.";
    public const string Created = "Resource created successfully.";
    public const string NoContent = "Request processed successfully.";

    // 4xx Client Errors
    public const string BadRequest = "Bad request. Please check your input.";
    public const string Unauthorized = "Authentication required.";
    public const string Forbidden = "Access denied.";
    public const string NotFound = "The requested resource was not found.";
    public const string MethodNotAllowed = "HTTP method not allowed.";
    public const string Conflict = "Resource already exists or conflicts with current state.";
    public const string UnprocessableEntity = "Request contains invalid data.";
    public const string TooManyRequests = "Rate limit exceeded. Please try again later.";

    // 5xx Server Errors
    public const string InternalServerError = "An internal server error occurred.";
    public const string BadGateway = "Bad gateway response from upstream service.";
    public const string ServiceUnavailable = "Service temporarily unavailable.";
    public const string GatewayTimeout = "Gateway timeout from upstream service.";
}
