using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Tests.Mocks;
using TMS.SharedKernel.Domain.Tests.TestDbContext;
using TMS.SharedKernel.Domain.Tests.TestEntities;

namespace TMS.SharedKernel.Domain.Tests;

/// <summary>
/// Tests for soft delete global query filters
/// </summary>
public class SoftDeleteFilterTests : IDisposable
{
    private readonly MockCurrentFactorProvider _mockProvider;
    private readonly TestTenantDbContext _context;
    private readonly Guid _tenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

    public SoftDeleteFilterTests()
    {
        _mockProvider = new MockCurrentFactorProvider
        {
            CompanyId = _tenantId,
            CurrentFactorId = Guid.NewGuid()
        };

        var options = new DbContextOptionsBuilder<TestTenantDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TestTenantDbContext(options, _mockProvider);
    }

    [Fact]
    public async Task SoftDeleteFilter_Should_ExcludeDeletedEntities()
    {
        // Arrange
        var entity1 = new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "Active Entity",
            IsDeleted = false
        };

        var entity2 = new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "Deleted Entity",
            IsDeleted = true
        };

        _context.TenantScopedEntities.AddRange(entity1, entity2);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        // Act
        var results = await _context.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.Single(results);
        Assert.Equal("Active Entity", results[0].Name);
        Assert.False(results[0].IsDeleted);
    }

    [Fact]
    public async Task SoftDeleteFilter_Should_WorkWith_TenantFilter_Combined()
    {
        // Arrange
        var tenant1 = Guid.NewGuid();
        var tenant2 = Guid.NewGuid();

        // Set up for tenant1
        _mockProvider.CompanyId = tenant1;

        _context.TenantScopedEntities.AddRange(
            new TenantScopedEntity { CompanyId = tenant1, Name = "Tenant1-Active", IsDeleted = false },
            new TenantScopedEntity { CompanyId = tenant1, Name = "Tenant1-Deleted", IsDeleted = true }
        );
        await _context.SaveChangesAsync();

        // Set up for tenant2
        _mockProvider.CompanyId = tenant2;

        _context.TenantScopedEntities.AddRange(
            new TenantScopedEntity { CompanyId = tenant2, Name = "Tenant2-Active", IsDeleted = false },
            new TenantScopedEntity { CompanyId = tenant2, Name = "Tenant2-Deleted", IsDeleted = true }
        );
        await _context.SaveChangesAsync();

        _context.ChangeTracker.Clear();

        // Act - Query as Tenant1
        _mockProvider.CompanyId = tenant1;
        var tenant1Results = await _context.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.Single(tenant1Results);
        Assert.Equal("Tenant1-Active", tenant1Results[0].Name);
        Assert.Equal(tenant1, tenant1Results[0].CompanyId);
        Assert.False(tenant1Results[0].IsDeleted);
    }

    [Fact]
    public async Task SoftDelete_Should_ConvertToUpdate_NotActualDelete()
    {
        // Arrange
        var entity = new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "To Be Deleted"
        };

        _context.TenantScopedEntities.Add(entity);
        await _context.SaveChangesAsync();
        var entityId = entity.Id;

        _context.ChangeTracker.Clear();

        // Act - Delete the entity
        var toDelete = await _context.TenantScopedEntities.FindAsync(entityId);
        Assert.NotNull(toDelete);

        _context.TenantScopedEntities.Remove(toDelete);
        await _context.SaveChangesAsync();

        _context.ChangeTracker.Clear();

        // Assert - Entity should not appear in normal queries
        var normalQuery = await _context.TenantScopedEntities.ToListAsync();
        Assert.DoesNotContain(normalQuery, e => e.Id == entityId);

        // But should exist with IgnoreQueryFilters
        var allRecords = await _context.TenantScopedEntities
            .IgnoreQueryFilters()
            .Where(e => e.Id == entityId)
            .FirstOrDefaultAsync();

        Assert.NotNull(allRecords);
        Assert.True(allRecords.IsDeleted);
        Assert.Equal("To Be Deleted", allRecords.Name);
    }

    [Fact]
    public async Task SoftDeleteFilter_CanBeBypassed_With_IgnoreQueryFilters()
    {
        // Arrange
        _context.TenantScopedEntities.AddRange(
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Active", IsDeleted = false },
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Deleted", IsDeleted = true }
        );
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        // Act
        var withFilter = await _context.TenantScopedEntities.CountAsync();
        var withoutFilter = await _context.TenantScopedEntities
            .IgnoreQueryFilters()
            .CountAsync();

        // Assert
        Assert.Equal(1, withFilter); // Only active
        Assert.Equal(2, withoutFilter); // Both active and deleted
    }

    [Fact]
    public async Task SoftDeleteFilter_Should_WorkWith_CountAsync()
    {
        // Arrange
        _context.TenantScopedEntities.AddRange(
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Active1", IsDeleted = false },
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Active2", IsDeleted = false },
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Deleted1", IsDeleted = true },
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Deleted2", IsDeleted = true }
        );
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        // Act
        var count = await _context.TenantScopedEntities.CountAsync();

        // Assert
        Assert.Equal(2, count); // Only active records
    }

    [Fact]
    public async Task SoftDeleteFilter_Should_WorkWith_AnyAsync()
    {
        // Arrange
        var deletedEntity = new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "Deleted",
            IsDeleted = true
        };

        _context.TenantScopedEntities.Add(deletedEntity);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        // Act
        var exists = await _context.TenantScopedEntities.AnyAsync(e => e.Name == "Deleted");

        // Assert
        Assert.False(exists); // Should not find deleted entity
    }

    [Fact]
    public async Task SoftDeleteFilter_Should_WorkWith_FirstOrDefaultAsync()
    {
        // Arrange
        var entity = new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "Target",
            IsDeleted = true
        };

        _context.TenantScopedEntities.Add(entity);
        await _context.SaveChangesAsync();
        _context.ChangeTracker.Clear();

        // Act
        var result = await _context.TenantScopedEntities
            .FirstOrDefaultAsync(e => e.Name == "Target");

        // Assert
        Assert.Null(result); // Should not find deleted entity
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
