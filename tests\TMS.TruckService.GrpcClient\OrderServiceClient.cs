using Grpc.Core;
using Microsoft.Extensions.Logging;
using TMS.TruckService.Api.Grpc;

namespace TMS.TruckService.GrpcClient;

/// <summary>
/// Client wrapper for OrderService gRPC operations
/// </summary>
public class OrderServiceClient : IOrderServiceClient
{
    private readonly OrderService.OrderServiceClient _client;
    private readonly ILogger<OrderServiceClient> _logger;

    /// <summary>
    /// Creates a new OrderServiceClient (for DI)
    /// </summary>
    /// <param name="client">The generated gRPC client</param>
    /// <param name="logger">Logger instance</param>
    public OrderServiceClient(
        OrderService.OrderServiceClient client,
        ILogger<OrderServiceClient> logger)
    {
        _client = client ?? throw new ArgumentNullException(nameof(client));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get an order by ID
    /// </summary>
    public async Task<GetOrderReply> GetOrderAsync(int orderId, CallOptions? options = null)
    {
        var request = new GetOrderRequest { OrderId = orderId };

        try
        {
            _logger.LogDebug("Getting order with ID: {OrderId}", orderId);
            var reply = await _client.GetOrderAsync(request, options ?? new CallOptions());
            _logger.LogDebug("Successfully retrieved order {OrderId}", orderId);
            return reply;
        }
        catch (RpcException ex)
        {
            _logger.LogError(ex, "gRPC Error getting order {OrderId}: {StatusCode} - {Detail}",
                orderId, ex.Status.StatusCode, ex.Status.Detail);

            // Log metadata (error codes, trace IDs, validation errors)
            foreach (var entry in ex.Trailers)
            {
                _logger.LogDebug("  {Key}: {Value}", entry.Key, entry.Value);
            }

            throw;
        }
    }

    /// <summary>
    /// Create a new order
    /// </summary>
    public async Task<CreateOrderReply> CreateOrderAsync(
        string customerName,
        double amount,
        string? description = null,
        CallOptions? options = null)
    {
        var request = new CreateOrderRequest
        {
            CustomerName = customerName,
            Amount = amount,
            Description = description ?? string.Empty
        };

        try
        {
            _logger.LogDebug("Creating order for customer: {CustomerName}, Amount: {Amount}", customerName, amount);
            var reply = await _client.CreateOrderAsync(request, options ?? new CallOptions());
            _logger.LogDebug("Successfully created order");
            return reply;
        }
        catch (RpcException ex)
        {
            _logger.LogError(ex, "gRPC Error creating order: {StatusCode} - {Detail}",
                ex.Status.StatusCode, ex.Status.Detail);

            foreach (var entry in ex.Trailers)
            {
                _logger.LogDebug("  {Key}: {Value}", entry.Key, entry.Value);
            }

            throw;
        }
    }

    /// <summary>
    /// List orders with pagination
    /// </summary>
    public async Task<ListOrdersReply> ListOrdersAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? status = null,
        CallOptions? options = null)
    {
        var request = new ListOrdersRequest
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            Status = status ?? string.Empty
        };

        try
        {
            _logger.LogDebug("Listing orders: Page {PageNumber}, Size {PageSize}, Status: {Status}",
                pageNumber, pageSize, status ?? "All");
            var reply = await _client.ListOrdersAsync(request, options ?? new CallOptions());
            _logger.LogDebug("Successfully listed orders");
            return reply;
        }
        catch (RpcException ex)
        {
            _logger.LogError(ex, "gRPC Error listing orders: {StatusCode} - {Detail}",
                ex.Status.StatusCode, ex.Status.Detail);

            foreach (var entry in ex.Trailers)
            {
                _logger.LogDebug("  {Key}: {Value}", entry.Key, entry.Value);
            }

            throw;
        }
    }

    /// <summary>
    /// Update order status
    /// </summary>
    public async Task<UpdateOrderStatusReply> UpdateOrderStatusAsync(
        int orderId,
        string newStatus,
        string? notes = null,
        CallOptions? options = null)
    {
        var request = new UpdateOrderStatusRequest
        {
            OrderId = orderId,
            NewStatus = newStatus,
            Notes = notes ?? string.Empty
        };

        try
        {
            _logger.LogDebug("Updating order {OrderId} status to {NewStatus}", orderId, newStatus);
            var reply = await _client.UpdateOrderStatusAsync(request, options ?? new CallOptions());
            _logger.LogDebug("Successfully updated order {OrderId} status", orderId);
            return reply;
        }
        catch (RpcException ex)
        {
            _logger.LogError(ex, "gRPC Error updating order {OrderId} status: {StatusCode} - {Detail}",
                orderId, ex.Status.StatusCode, ex.Status.Detail);

            foreach (var entry in ex.Trailers)
            {
                _logger.LogDebug("  {Key}: {Value}", entry.Key, entry.Value);
            }

            throw;
        }
    }

    /// <summary>
    /// Delete an order
    /// </summary>
    public async Task<DeleteOrderReply> DeleteOrderAsync(int orderId, CallOptions? options = null)
    {
        var request = new DeleteOrderRequest { OrderId = orderId };

        try
        {
            _logger.LogDebug("Deleting order {OrderId}", orderId);
            var reply = await _client.DeleteOrderAsync(request, options ?? new CallOptions());
            _logger.LogDebug("Successfully deleted order {OrderId}", orderId);
            return reply;
        }
        catch (RpcException ex)
        {
            _logger.LogError(ex, "gRPC Error deleting order {OrderId}: {StatusCode} - {Detail}",
                orderId, ex.Status.StatusCode, ex.Status.Detail);

            foreach (var entry in ex.Trailers)
            {
                _logger.LogDebug("  {Key}: {Value}", entry.Key, entry.Value);
            }

            throw;
        }
    }

    /// <summary>
    /// Test server connectivity
    /// </summary>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogDebug("Testing connection to gRPC server");
            // Try to get order 1 (the sample order)
            var reply = await GetOrderAsync(1);
            _logger.LogInformation("Successfully connected to gRPC server");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to gRPC server");
            return false;
        }
    }
}
