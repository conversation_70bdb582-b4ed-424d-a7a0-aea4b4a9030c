﻿namespace TMS.SharedKernel.Domain.Entities.Interfaces;

/// <summary>
/// Interface for tracking created date
/// </summary>
public interface ICreatedDateTracking
{
    /// <summary>
    /// The date tracked when object is created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Interface for tracking modified date
/// </summary>
public interface IUpdatedDateTracking
{
    /// <summary>
    /// The date tracked when object is modified
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Interface for tracking audit date
/// </summary>
public interface IAuditDateTracking : ICreatedDateTracking, IUpdatedDateTracking { }
