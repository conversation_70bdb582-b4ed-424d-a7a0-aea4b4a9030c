﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.DependencyInjection;
using Quartz;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for configuring exception handling in a WebApplicationBuilder.
/// </summary>
[ExcludeFromCodeCoverage]
public static class QuartzServiceCollectionExtensions
{
    /// <summary>
    /// AddClusteredQuartz
    /// </summary>
    /// <param name="services"></param>
    /// <param name="connectionString"></param>
    /// <param name="schedulerName"></param>
    /// <returns></returns>
    public static IServiceCollection AddClusteredQuartz(
        this IServiceCollection services,
        string connectionString,
        string schedulerName = "ClusteredScheduler")
    {
        // Register listeners as singletons
        services.AddSingleton<QuartzSchedulerListener>();
        services.AddSingleton<QuartzJobListener>();

        services.AddQuartz(q =>
        {
            q.SchedulerName = schedulerName;
            q.SchedulerId = "AUTO";

            // Increase max concurrent jobs for better throughput
            q.UseDefaultThreadPool(tp =>
            {
                tp.MaxConcurrency = 10;
            });

            q.UsePersistentStore(store =>
            {
                store.UseProperties = true;
                store.UseClustering(c =>
                {
                    c.CheckinInterval = TimeSpan.FromSeconds(30);
                    c.CheckinMisfireThreshold = TimeSpan.FromSeconds(180);
                });
                store.UsePostgres(postgres =>
                {
                    postgres.ConnectionString = connectionString;
                    postgres.TablePrefix = "qrtz_";
                    postgres.UseDriverDelegate<Quartz.Impl.AdoJobStore.PostgreSQLDelegate>();
                });
                store.UseNewtonsoftJsonSerializer();

                // Enhanced retry configuration for better resilience
                store.RetryInterval = TimeSpan.FromSeconds(5);
                // Perform initialization check with retry to ensure tables exist
                store.PerformSchemaValidation = true;
            });

            // Add listeners to catch and handle errors gracefully
            q.AddSchedulerListener<QuartzSchedulerListener>();
            q.AddJobListener<QuartzJobListener>();
        });

        services.AddQuartzHostedService(opt =>
        {
            opt.WaitForJobsToComplete = true;
            // Add startup delay to ensure database is fully ready and connection pool is cleared
            opt.StartDelay = TimeSpan.FromSeconds(5);
        });

        return services;
    }
}
