﻿using Microsoft.Extensions.DependencyInjection;

namespace TMS.SharedKernal.Caching;

/// <summary>
/// Extension methods for registering metadata caching services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds metadata caching service with entity type configurations
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureAction">Action to configure entity type cache keys</param>
    public static IServiceCollection AddMetadataCaching(
        this IServiceCollection services,
        Action<MetadataCacheConfigurationBuilder> configureAction)
    {
        var builder = new MetadataCacheConfigurationBuilder();
        configureAction(builder);

        // Register configurations as singleton
        services.AddSingleton<IEnumerable<MetadataCacheKeyConfiguration>>(builder.Build());

        // Register the service
        services.AddSingleton<IMetadataCacheService, MetadataCacheService>();

        return services;
    }
}

/// <summary>
/// Builder for configuring metadata cache key mappings
/// </summary>
public class MetadataCacheConfigurationBuilder
{
    private readonly List<MetadataCacheKeyConfiguration> _configurations = new();

    /// <summary>
    /// Registers a cache key configuration for a specific entity type
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="redisKeyTemplate">Redis key template. Use {prefix} for dynamic prefixes.</param>
    /// <param name="requiresPrefix">Whether a prefix is required</param>
    public MetadataCacheConfigurationBuilder RegisterEntityType<T>(
        string redisKeyTemplate,
        bool requiresPrefix = false) where T : class
    {
        _configurations.Add(new MetadataCacheKeyConfiguration
        {
            EntityType = typeof(T),
            RedisKeyTemplate = redisKeyTemplate,
            RequiresPrefix = requiresPrefix
        });

        return this;
    }

    internal IEnumerable<MetadataCacheKeyConfiguration> Build()
    {
        return _configurations;
    }
}
