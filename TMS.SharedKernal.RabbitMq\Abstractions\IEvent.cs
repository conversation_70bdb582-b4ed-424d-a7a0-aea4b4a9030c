﻿namespace TMS.SharedKernal.RabbitMq.Abstractions;

public interface IEvent
{
    string EventId { get; }
    DateTime CreatedAt { get; }
    string EventType { get; }
}

public interface IEventPublisher
{
    Task PublishAsync<T>(T eventData, string? routingKey = null, CancellationToken cancellationToken = default) where T : class, IEvent;
    void Publish<T>(T eventData, string? routingKey = null) where T : class, IEvent;
}

public interface IEventConsumer
{
    void StartConsuming<T>(string queueName, Func<T, Task<bool>> messageHandler) where T : class, IEvent;
    void StartConsuming(string queueName, Func<string, string, Task<bool>> messageHandler);
    void StopConsuming();
}

public interface IEventHandler<in T> where T : class, IEvent
{
    Task<bool> HandleAsync(T eventData, CancellationToken cancellationToken = default);
}
