﻿using Microsoft.Extensions.DependencyInjection;
using TMS.SharedKernal.RabbitMq.Configuration;
using TMS.SharedKernal.RabbitMq.Events;

namespace TMS.SharedKernal.RabbitMq.Extensions;

public static class HealthCheckExtensions
{
    public static IServiceCollection AddRabbitMqHealthCheck(this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck<RabbitMqHealthCheck>("rabbitmq");

        return services;
    }
}
