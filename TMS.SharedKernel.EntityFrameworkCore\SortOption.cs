﻿using System.Linq.Expressions;
using TMS.SharedKernel.Domain;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// Represents a sorting option for queryable entities with strongly-typed key selection.
/// Implements both initial ordering (OrderBy/OrderByDescending) and subsequent ordering (ThenBy/ThenByDescending).
/// </summary>
/// <typeparam name="T">The entity type to sort</typeparam>
/// <typeparam name="TKey">The type of the sort key</typeparam>
public class SortOption<T, TKey> : ISortOption<T>
{
    /// <summary>
    /// Gets the expression that selects the property to sort by.
    /// </summary>
    public Expression<Func<T, TKey>> KeySelector { get; }

    /// <summary>
    /// Gets a value indicating whether the sort order is descending.
    /// </summary>
    public bool Descending { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="SortOption{T, TKey}"/> class.
    /// </summary>
    /// <param name="keySelector">Expression that selects the property to sort by</param>
    /// <param name="descending">True for descending order, false for ascending</param>
    /// <exception cref="ArgumentNullException">Thrown when keySelector is null</exception>
    public SortOption(Expression<Func<T, TKey>> keySelector, bool descending)
    {
        KeySelector = keySelector ?? throw new ArgumentNullException(nameof(keySelector));
        Descending = descending;
    }

    /// <summary>
    /// Applies the sorting operation to the query.
    /// </summary>
    /// <param name="query">The query to sort</param>
    /// <param name="first">True if this is the first sort operation (uses OrderBy/OrderByDescending),
    /// false for subsequent sorts (uses ThenBy/ThenByDescending)</param>
    /// <returns>An ordered queryable with the sort applied</returns>
    /// <exception cref="InvalidOperationException">Thrown when first=false but query is not an IOrderedQueryable</exception>
    public IOrderedQueryable<T> Apply(IQueryable<T> query, bool first)
    {
        if (first)
        {
            return Descending
                ? query.OrderByDescending(KeySelector)
                : query.OrderBy(KeySelector);
        }
        else
        {
            return Descending
                ? ((IOrderedQueryable<T>)query).ThenByDescending(KeySelector)
                : ((IOrderedQueryable<T>)query).ThenBy(KeySelector);
        }
    }
}
