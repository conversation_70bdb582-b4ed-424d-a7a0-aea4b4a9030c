using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Tests.Mocks;
using TMS.SharedKernel.Domain.Tests.TestDbContext;
using TMS.SharedKernel.Domain.Tests.TestEntities;

namespace TMS.SharedKernel.Domain.Tests;

/// <summary>
/// Comprehensive tests for tenant isolation using global query filters
/// </summary>
public class TenantIsolationTests : IDisposable
{
    private readonly MockCurrentFactorProvider _mockProvider;
    private readonly TestTenantDbContext _context;
    private readonly Guid _tenant1Id = Guid.Parse("11111111-1111-1111-1111-111111111111");
    private readonly Guid _tenant2Id = Guid.Parse("*************-2222-2222-************");
    private readonly Guid _tenant3Id = Guid.Parse("*************-3333-3333-************");

    public TenantIsolationTests()
    {
        _mockProvider = new MockCurrentFactorProvider();

        var options = new DbContextOptionsBuilder<TestTenantDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()) // Unique DB per test
            .Options;

        _context = new TestTenantDbContext(options, _mockProvider);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        // Create tenant-scoped entities for 3 different tenants
        var tenantEntities = new List<TenantScopedEntity>
        {
            // Tenant 1 entities
            new() { CompanyId = _tenant1Id, Name = "Tenant1-Entity1", Description = "First entity for tenant 1" },
            new() { CompanyId = _tenant1Id, Name = "Tenant1-Entity2", Description = "Second entity for tenant 1" },
            new() { CompanyId = _tenant1Id, Name = "Tenant1-Entity3", Description = "Third entity for tenant 1" },

            // Tenant 2 entities
            new() { CompanyId = _tenant2Id, Name = "Tenant2-Entity1", Description = "First entity for tenant 2" },
            new() { CompanyId = _tenant2Id, Name = "Tenant2-Entity2", Description = "Second entity for tenant 2" },

            // Tenant 3 entities
            new() { CompanyId = _tenant3Id, Name = "Tenant3-Entity1", Description = "First entity for tenant 3" },
        };

        // Create shared entities (no tenant)
        var sharedEntities = new List<SharedEntity>
        {
            new() { Name = "Shared1", Code = "SH001" },
            new() { Name = "Shared2", Code = "SH002" },
            new() { Name = "Shared3", Code = "SH003" },
        };

        _context.TenantScopedEntities.AddRange(tenantEntities);
        _context.SharedEntities.AddRange(sharedEntities);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_FilterByTenant_When_ValidTenantContext()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act
        var results = await _context.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.NotEmpty(results);
        Assert.Equal(3, results.Count); // Should only return Tenant 1's 3 entities
        Assert.All(results, entity => Assert.Equal(_tenant1Id, entity.CompanyId));
        Assert.All(results, entity => Assert.StartsWith("Tenant1-", entity.Name));
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_ReturnDifferentData_When_DifferentTenant()
    {
        // Arrange - Query as Tenant 1
        _mockProvider.CompanyId = _tenant1Id;
        var tenant1Results = await _context.TenantScopedEntities.ToListAsync();

        // Act - Change to Tenant 2
        _mockProvider.CompanyId = _tenant2Id;

        // Force new query (clear tracked entities)
        _context.ChangeTracker.Clear();

        var tenant2Results = await _context.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.Equal(3, tenant1Results.Count);
        Assert.Equal(2, tenant2Results.Count);
        Assert.All(tenant1Results, e => Assert.Equal(_tenant1Id, e.CompanyId));
        Assert.All(tenant2Results, e => Assert.Equal(_tenant2Id, e.CompanyId));

        // Verify no overlap
        var tenant1Ids = tenant1Results.Select(e => e.Id).ToList();
        var tenant2Ids = tenant2Results.Select(e => e.Id).ToList();
        Assert.Empty(tenant1Ids.Intersect(tenant2Ids));
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_ReturnEmpty_When_NoMatchingTenant()
    {
        // Arrange - Use a tenant ID that doesn't exist in test data
        var nonExistentTenant = Guid.Parse("*************-9999-9999-************");
        _mockProvider.CompanyId = nonExistentTenant;

        // Act
        var results = await _context.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.Empty(results);
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_ReturnEmpty_When_CompanyIdIsEmpty()
    {
        // Arrange
        _mockProvider.CompanyId = Guid.Empty;

        // Act
        var results = await _context.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.Empty(results); // Should return no records for security
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_NotApply_ToNonTenantEntities()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act
        var results = await _context.SharedEntities.ToListAsync();

        // Assert
        Assert.Equal(3, results.Count); // Should return ALL shared entities regardless of tenant
        Assert.Contains(results, e => e.Code == "SH001");
        Assert.Contains(results, e => e.Code == "SH002");
        Assert.Contains(results, e => e.Code == "SH003");
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_WorkWith_WhereClause()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act
        var results = await _context.TenantScopedEntities
            .Where(e => e.Name.Contains("Entity2"))
            .ToListAsync();

        // Assert
        Assert.Single(results);
        Assert.Equal("Tenant1-Entity2", results[0].Name);
        Assert.Equal(_tenant1Id, results[0].CompanyId);
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_WorkWith_FirstOrDefaultAsync()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant2Id;

        // Act
        var result = await _context.TenantScopedEntities
            .FirstOrDefaultAsync(e => e.Name == "Tenant2-Entity1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_tenant2Id, result.CompanyId);
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_PreventAccess_ToOtherTenantData_ViaFirstOrDefault()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act - Try to access Tenant 2's data while logged in as Tenant 1
        var result = await _context.TenantScopedEntities
            .FirstOrDefaultAsync(e => e.Name == "Tenant2-Entity1");

        // Assert
        Assert.Null(result); // Should NOT be able to access other tenant's data
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_WorkWith_CountAsync()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act
        var count = await _context.TenantScopedEntities.CountAsync();

        // Assert
        Assert.Equal(3, count);
    }

    [Fact]
    public async Task GlobalQueryFilter_Should_WorkWith_AnyAsync()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act
        var existsInTenant = await _context.TenantScopedEntities
            .AnyAsync(e => e.Name == "Tenant1-Entity1");

        var notExistsInTenant = await _context.TenantScopedEntities
            .AnyAsync(e => e.Name == "Tenant2-Entity1"); // Different tenant

        // Assert
        Assert.True(existsInTenant);
        Assert.False(notExistsInTenant); // Should not see other tenant's data
    }

    [Fact]
    public async Task GlobalQueryFilter_CanBeBypassed_With_IgnoreQueryFilters()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        // Act - Query without filter (system admin scenario)
        var allResults = await _context.TenantScopedEntities
            .IgnoreQueryFilters()
            .ToListAsync();

        // Assert
        Assert.Equal(6, allResults.Count); // All 6 entities across all tenants
        Assert.Contains(allResults, e => e.CompanyId == _tenant1Id);
        Assert.Contains(allResults, e => e.CompanyId == _tenant2Id);
        Assert.Contains(allResults, e => e.CompanyId == _tenant3Id);
    }

    [Fact]
    public async Task Creating_TenantEntity_Should_AutoPopulate_CompanyId()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;
        _mockProvider.CurrentFactorId = Guid.NewGuid();

        var newEntity = new TenantScopedEntity
        {
            Name = "New Entity",
            Description = "Test",
            // CompanyId not set - should be auto-populated
        };

        // Act
        _context.TenantScopedEntities.Add(newEntity);
        await _context.SaveChangesAsync();

        // Assert
        Assert.Equal(_tenant1Id, newEntity.CompanyId);
    }

    [Fact]
    public async Task Creating_TenantEntity_Should_Fail_When_NoTenantContext()
    {
        // Arrange
        _mockProvider.CompanyId = Guid.Empty; // No tenant context
        _mockProvider.CurrentFactorId = Guid.NewGuid();

        var newEntity = new TenantScopedEntity
        {
            Name = "New Entity",
            Description = "Test",
        };

        // Act & Assert
        _context.TenantScopedEntities.Add(newEntity);

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            async () => await _context.SaveChangesAsync()
        );

        Assert.Contains("CompanyId", exception.Message);
    }

    [Fact]
    public async Task Modifying_CompanyId_Should_Throw_Exception()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;
        _mockProvider.CurrentFactorId = Guid.NewGuid();

        var entity = await _context.TenantScopedEntities.FirstAsync();
        var originalCompanyId = entity.CompanyId;

        // Act
        entity.CompanyId = _tenant2Id; // Try to move to different tenant

        // Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            async () => await _context.SaveChangesAsync()
        );

        Assert.Contains("Cannot modify CompanyId", exception.Message);
    }

    [Fact]
    public async Task Find_Should_RespectTenantFilter()
    {
        // Arrange
        _mockProvider.CompanyId = _tenant1Id;

        var tenant1Entity = await _context.TenantScopedEntities.FirstAsync();
        var entityId = tenant1Entity.Id;

        // Change to different tenant
        _mockProvider.CompanyId = _tenant2Id;
        _context.ChangeTracker.Clear();

        // Act - Try to find entity from Tenant 1 while logged in as Tenant 2
        var result = await _context.TenantScopedEntities.FindAsync(entityId);

        // Assert
        // Note: Find() might bypass query filters in some EF versions
        // This test documents the behavior
        Assert.Null(result); // Should not find entity from different tenant
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
