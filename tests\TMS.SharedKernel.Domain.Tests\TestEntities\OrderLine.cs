using TMS.SharedKernel.Domain.Entities;

namespace TMS.SharedKernel.Domain.Tests.TestEntities;

/// <summary>
/// Test entity representing an OrderLine (Detail entity)
/// </summary>
public class OrderLine : AuditableEntity
{
    public Guid OrderId { get; set; }
    public string ProductName { get; set; } = null!;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }

    // Navigation property
    public Order Order { get; set; } = null!;
}
