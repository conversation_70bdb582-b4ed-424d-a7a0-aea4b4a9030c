﻿-- ===================================================================
-- ===================================================================
-- 1. ENHANCE VEHICLES TABLE WITH STATUS COLUMNS
-- ===================================================================

-- Add status tracking columns to Vehicles table
ALTER TABLE public."Vehicles" 
ADD COLUMN IF NOT EXISTS "LifecycleStatus" VARCHAR(20) DEFAULT 'NEW',
ADD COLUMN IF NOT EXISTS "TechnicalStatus" VARCHAR(20) DEFAULT 'GOOD',
ADD COLUMN IF NOT EXISTS "LegalStatus" VARCHAR(20) DEFAULT 'VALID',
ADD COLUMN IF NOT EXISTS "OperationStatus" VARCHAR(20) DEFAULT 'NOT_READY',
ADD COLUMN IF NOT EXISTS "OverallStatus" VARCHAR(20) DEFAULT 'NEW';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_vehicles_lifecycle_status"
    ON public."Vehicles" ("LifecycleStatus") WHERE "IsDeleted" = FALSE;

CREATE INDEX IF NOT EXISTS "idx_vehicles_overall_status" 
    ON public."Vehicles" ("OverallStatus") WHERE "IsDeleted" = FALSE;

CREATE INDEX IF NOT EXISTS "idx_vehicles_status_dates"
    ON public."Vehicles" ("RegistrationExpiry", "InsuranceExpiry", "NextMaintenanceDate", "BadgeExpiryDate") 
    WHERE "IsDeleted" = FALSE;

-- ===================================================================
-- 2. STATUS CALCULATION FUNCTIONS
-- ===================================================================

-- Function to determine lifecycle status based on vehicle data
CREATE OR REPLACE FUNCTION calculate_lifecycle_status(
    p_status SMALLINT,
    p_registration_date TIMESTAMP WITH TIME ZONE,
    p_prohibition_start_date TIMESTAMP WITH TIME ZONE,
    p_prohibition_expiry_date TIMESTAMP WITH TIME ZONE
)
RETURNS VARCHAR(20)
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    -- Check if vehicle is prohibited
    IF p_prohibition_start_date IS NOT NULL AND 
       (p_prohibition_expiry_date IS NULL OR p_prohibition_expiry_date > CURRENT_TIMESTAMP) THEN
        RETURN 'SUSPENDED';
    END IF;
    
    -- Check vehicle status and registration
    CASE 
        WHEN p_status = 0 OR p_registration_date IS NULL THEN 
            RETURN 'NEW';
        WHEN p_status IN (1, 2) THEN 
            RETURN 'OPERATING';
        WHEN p_status IN (3, 4, 5) THEN 
            RETURN 'SUSPENDED';
        ELSE 
            RETURN 'NEW';
    END CASE;
END;
$$;

-- Function to determine technical status based on maintenance and condition
CREATE OR REPLACE FUNCTION calculate_technical_status(
    p_next_maintenance_date TIMESTAMP WITH TIME ZONE,
    p_production_year INTEGER,
    p_status SMALLINT
)
RETURNS VARCHAR(20)
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    v_vehicle_age INTEGER;
    v_days_since_maintenance INTEGER;
BEGIN
    -- Calculate vehicle age
    v_vehicle_age := EXTRACT(YEAR FROM CURRENT_DATE) - COALESCE(p_production_year, EXTRACT(YEAR FROM CURRENT_DATE));
    
    -- Calculate days since maintenance due
    IF p_next_maintenance_date IS NOT NULL THEN
        v_days_since_maintenance := EXTRACT(DAY FROM CURRENT_TIMESTAMP - p_next_maintenance_date);
    END IF;
    
    -- Determine technical status
    CASE
        -- Broken if explicitly marked or very overdue maintenance
        WHEN p_status = 5 OR v_days_since_maintenance > 180 THEN
            RETURN 'BROKEN';
        -- Needs attention if overdue maintenance or old vehicle
        WHEN v_days_since_maintenance > 30 OR v_vehicle_age > 15 OR p_status = 4 THEN
            RETURN 'NEEDS_ATTENTION';
        -- Good condition
        ELSE
            RETURN 'GOOD';
    END CASE;
END;
$$;

-- Function to determine legal status based on documents
CREATE OR REPLACE FUNCTION calculate_legal_status(
    p_registration_expiry TIMESTAMP WITH TIME ZONE,
    p_insurance_expiry TIMESTAMP WITH TIME ZONE,
    p_badge_expiry_date TIMESTAMP WITH TIME ZONE
)
RETURNS VARCHAR(20)
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    v_registration_expired BOOLEAN := FALSE;
    v_insurance_expired BOOLEAN := FALSE;
    v_badge_expired BOOLEAN := FALSE;
BEGIN
    -- Check document expiration
    v_registration_expired := p_registration_expiry IS NOT NULL AND p_registration_expiry < CURRENT_TIMESTAMP;
    v_insurance_expired := p_insurance_expiry IS NOT NULL AND p_insurance_expiry < CURRENT_TIMESTAMP;
    v_badge_expired := p_badge_expiry_date IS NOT NULL AND p_badge_expiry_date < CURRENT_TIMESTAMP;
    
    -- Determine legal status
    IF v_registration_expired OR v_insurance_expired OR v_badge_expired THEN
        RETURN 'INVALID';
    ELSE
        RETURN 'VALID';
    END IF;
END;
$$;

-- Function to determine operation status based on readiness
CREATE OR REPLACE FUNCTION calculate_operation_status(
    p_status SMALLINT,
    p_employee_id UUID,
    p_registration_expiry TIMESTAMP WITH TIME ZONE,
    p_insurance_expiry TIMESTAMP WITH TIME ZONE,
    p_next_maintenance_date TIMESTAMP WITH TIME ZONE
)
RETURNS VARCHAR(20)
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    v_has_documents BOOLEAN;
    v_has_driver BOOLEAN;
    v_maintenance_ok BOOLEAN;
BEGIN
    -- Check if vehicle has necessary documents
    v_has_documents := p_registration_expiry IS NOT NULL AND p_registration_expiry > CURRENT_TIMESTAMP
                      AND p_insurance_expiry IS NOT NULL AND p_insurance_expiry > CURRENT_TIMESTAMP;
    
    -- Check if vehicle has assigned driver
    v_has_driver := p_employee_id IS NOT NULL;
    
    -- Check maintenance status
    v_maintenance_ok := p_next_maintenance_date IS NULL OR p_next_maintenance_date > CURRENT_TIMESTAMP;
    
    -- Determine operation status based on vehicle status and conditions
    CASE p_status
        WHEN 0 THEN RETURN 'NOT_READY';  -- New/Inactive
        WHEN 1 THEN 
            CASE 
                WHEN v_has_documents AND v_has_driver AND v_maintenance_ok THEN RETURN 'READY';
                ELSE RETURN 'CONNECTING';
            END CASE;
        WHEN 2 THEN RETURN 'OPERATING';  -- In Use
        WHEN 3 THEN RETURN 'CONNECTED';  -- Assigned but not in use
        ELSE RETURN 'NOT_READY';
    END CASE;
END;
$$;

-- Master function to calculate all statuses
CREATE OR REPLACE FUNCTION calculate_vehicle_statuses(
    p_vehicle_id UUID
)
RETURNS TABLE(
    lifecycle_status VARCHAR(20),
    technical_status VARCHAR(20),
    legal_status VARCHAR(20),
    operation_status VARCHAR(20),
    overall_status VARCHAR(20),
    calculation_reason TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_vehicle RECORD;
    v_lifecycle VARCHAR(20);
    v_technical VARCHAR(20);
    v_legal VARCHAR(20);
    v_operation VARCHAR(20);
    v_overall VARCHAR(20);
    v_reason TEXT := '';
BEGIN
    -- Get vehicle data
    SELECT * INTO v_vehicle 
    FROM public."Vehicles" 
    WHERE "Id" = p_vehicle_id AND "IsDeleted" = FALSE;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Vehicle not found: %', p_vehicle_id;
    END IF;
    
    -- Calculate individual statuses
    v_lifecycle := calculate_lifecycle_status(
        v_vehicle."Status", 
        v_vehicle."RegistrationDate", 
        v_vehicle."ProhibitionStartDate", 
        v_vehicle."ProhibitionExpiryDate"
    );
    
    v_technical := calculate_technical_status(
        v_vehicle."NextMaintenanceDate", 
        v_vehicle."ProductionYear", 
        v_vehicle."Status"
    );
    
    v_legal := calculate_legal_status(
        v_vehicle."RegistrationExpiry", 
        v_vehicle."InsuranceExpiry", 
        v_vehicle."BadgeExpiryDate"
    );
    
    v_operation := calculate_operation_status(
        v_vehicle."Status", 
        v_vehicle."EmployeeId", 
        v_vehicle."RegistrationExpiry", 
        v_vehicle."InsuranceExpiry", 
        v_vehicle."NextMaintenanceDate"
    );
    
    -- Get overall status from mapping
    SELECT result_status INTO v_overall
    FROM get_overall_status(v_lifecycle, v_technical, v_legal, v_operation)
    LIMIT 1;
    
    -- Build calculation reason
    v_reason := format('Lifecycle: %s, Technical: %s, Legal: %s, Operation: %s', 
                      v_lifecycle, v_technical, v_legal, v_operation);
    
    RETURN QUERY SELECT v_lifecycle, v_technical, v_legal, v_operation, v_overall, v_reason;
END;
$$;

-- ===================================================================
-- 3. CORE VEHICLE VIEWS WITH STATUS INTEGRATION
-- ===================================================================

-- Primary vehicle view with computed statuses
CREATE OR REPLACE VIEW v_vehicles_with_status AS
SELECT 
    v."Id",
    v."Code",
    v."LicensePlate",
    v."DeptManagerCode",
    v."OwnershipType",
    v."OwningUnit",
    
    -- Dates
    v."RegistrationDate",
    v."RegistrationExpiry",
    v."NextMaintenanceDate", 
    v."InsuranceDate",
    v."InsuranceExpiry",
    v."BadgeRegistrationDate",
    v."BadgeExpiryDate",
    v."ProhibitionStartDate",
    v."ProhibitionExpiryDate",
    
    -- Vehicle details
    v."VehicleTypeId",
    v."ChassisNumber",
    v."EngineNumber", 
    v."VehicleBrandId",
    v."Model",
    v."FuelTypeId",
    v."Color",
    v."ProductionYear",
    v."CompanyId",
    v."DepartmentId",
    v."EmployeeId",
    
    -- Physical specifications
    v."Length",
    v."Width", 
    v."Height",
    v."InternalLength",
    v."InternalWidth",
    v."InternalHeight",
    v."TotalWeight",
    v."TotalWeightUnit",
    v."VehicleWeight",
    v."VehicleWeightUnit",
    v."CylinderCapacity",
    
    -- Computed statuses (real-time calculation)
    calculate_lifecycle_status(v."Status", v."RegistrationDate", v."ProhibitionStartDate", v."ProhibitionExpiryDate") as computed_lifecycle_status,
    calculate_technical_status(v."NextMaintenanceDate", v."ProductionYear", v."Status") as computed_technical_status,
    calculate_legal_status(v."RegistrationExpiry", v."InsuranceExpiry", v."BadgeExpiryDate") as computed_legal_status,
    calculate_operation_status(v."Status", v."EmployeeId", v."RegistrationExpiry", v."InsuranceExpiry", v."NextMaintenanceDate") as computed_operation_status,
    
    -- Stored statuses (for comparison)
    v."LifecycleStatus" as stored_lifecycle_status,
    v."TechnicalStatus" as stored_technical_status,
    v."LegalStatus" as stored_legal_status,
    v."OperationStatus" as stored_operation_status,
    v."OverallStatus" as stored_overall_status,
    
    -- Document expiration warnings
    CASE 
        WHEN v."RegistrationExpiry" < CURRENT_TIMESTAMP THEN 'REGISTRATION_EXPIRED'
        WHEN v."InsuranceExpiry" < CURRENT_TIMESTAMP THEN 'INSURANCE_EXPIRED'
        WHEN v."BadgeExpiryDate" < CURRENT_TIMESTAMP THEN 'BADGE_EXPIRED'
        WHEN v."RegistrationExpiry" < CURRENT_TIMESTAMP + INTERVAL '30 days' THEN 'REGISTRATION_EXPIRING_SOON'
        WHEN v."InsuranceExpiry" < CURRENT_TIMESTAMP + INTERVAL '30 days' THEN 'INSURANCE_EXPIRING_SOON'
        WHEN v."BadgeExpiryDate" < CURRENT_TIMESTAMP + INTERVAL '30 days' THEN 'BADGE_EXPIRING_SOON'
        ELSE 'DOCUMENTS_OK'
    END as document_alert_status,
    
    -- Maintenance alerts
    CASE 
        WHEN v."NextMaintenanceDate" < CURRENT_TIMESTAMP THEN 'MAINTENANCE_OVERDUE'
        WHEN v."NextMaintenanceDate" < CURRENT_TIMESTAMP + INTERVAL '7 days' THEN 'MAINTENANCE_DUE_SOON'
        ELSE 'MAINTENANCE_OK'
    END as maintenance_alert_status,
    
    -- Audit fields
    v."CreatedAt",
    v."UpdatedAt", 
    v."CreatedBy",
    v."UpdatedBy",
    v."IsDeleted"
    
FROM public."Vehicles" v
WHERE v."IsDeleted" = FALSE;
