﻿
// SmoothRedis.cs - A fluent, developer-friendly Redis library
using System.Text.Json;
using System.Text.Json.Serialization;
using StackExchange.Redis;

namespace TMS.SharedKernal.SmoothRedis;

// Main fluent interface
public interface ISmoothRedis
{
    ICacheBuilder Cache { get; }
    ICollectionBuilder Collections { get; }
    IPubSubBuilder PubSub { get; }
    IDistributedLockBuilder Locks { get; }
    IBatchBuilder Batch { get; }
    Task<bool> HealthCheckAsync();
    ISmoothRedis WithDatabase(int database);
}

// Cache operations with automatic serialization
public interface ICacheBuilder
{
    Task<T?> GetAsync<T>(string key) where T : class;
    Task<string?> GetAsync(string key);
    Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    Task SetAsync(string key, string value, TimeSpan? expiry = null);
    Task<bool> ExistsAsync(string key);
    Task<bool> RemoveAsync(string key);
    Task<long> RemoveAsync(params string[] keys);
    Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null) where T : class;
    Task<string> GetOrSetAsync(string key, Func<Task<string>> factory, TimeSpan? expiry = null);
    Task<TimeSpan?> GetKeyTTLAsync(string key);
    ICacheBuilder WithPrefix(string prefix);
    ICacheBuilder WithDefaultExpiry(TimeSpan expiry);
}

// Collection operations
public interface ICollectionBuilder
{
    IListBuilder<T> List<T>(string key) where T : class;
    ISetBuilder<T> Set<T>(string key) where T : class;
    ISortedSetBuilder<T> SortedSet<T>(string key) where T : class;
    IHashBuilder Hash(string key);
    ICounterBuilder Counter(string key);
}

// Typed list operations
public interface IListBuilder<T> where T : class
{
    Task<long> PushAsync(T item, bool toLeft = true);
    Task<long> PushAsync(IEnumerable<T> items, bool toLeft = true);
    Task<T?> PopAsync(bool fromLeft = true);
    Task<List<T>> RangeAsync(int start = 0, int end = -1);
    Task<long> LengthAsync();
    Task<List<T>> TrimAndGetAsync(int maxLength);
}

// Typed set operations
public interface ISetBuilder<T> where T : class
{
    Task<bool> AddAsync(T item);
    Task<long> AddAsync(IEnumerable<T> items);
    Task<bool> RemoveAsync(T item);
    Task<bool> ContainsAsync(T item);
    Task<List<T>> MembersAsync();
    Task<long> CountAsync();
    Task<T?> RandomMemberAsync();
}

// Typed sorted set operations
public interface ISortedSetBuilder<T> where T : class
{
    Task<bool> AddAsync(T item, double score);
    Task<long> AddAsync(Dictionary<T, double> items);
    Task<List<T>> RangeAsync(int start = 0, int end = -1, bool reverse = false);
    Task<List<(T item, double score)>> RangeWithScoresAsync(int start = 0, int end = -1, bool reverse = false);
    Task<double?> ScoreAsync(T item);
    Task<long?> RankAsync(T item, bool reverse = false);
    Task<List<T>> TopAsync(int count);
}

// Hash operations with dynamic fields
public interface IHashBuilder
{
    Task<T?> GetAsync<T>(string field) where T : class;
    Task<string?> GetAsync(string field);
    Task SetAsync<T>(string field, T value) where T : class;
    Task SetAsync(string field, string value);
    Task SetAsync(Dictionary<string, object> fields);
    Task<bool> ExistsAsync(string field);
    Task<bool> RemoveAsync(string field);
    Task<Dictionary<string, string>> GetAllAsync();
    Task<T?> GetAsTypeAsync<T>() where T : class, new();
    Task SetAsTypeAsync<T>(T obj) where T : class;
}

// Counter operations
public interface ICounterBuilder
{
    Task<long> IncrementAsync(long by = 1);
    Task<long> DecrementAsync(long by = 1);
    Task<long> GetAsync();
    Task SetAsync(long value);
    Task<bool> ResetAsync();
}

// Pub/Sub operations
public interface IPubSubBuilder
{
    Task<long> PublishAsync<T>(string channel, T message) where T : class;
    Task<long> PublishAsync(string channel, string message);
    Task SubscribeAsync<T>(string channel, Func<T, Task> handler) where T : class;
    Task SubscribeAsync(string channel, Func<string, Task> handler);
    Task UnsubscribeAsync(string channel);
}

// Distributed locking
public interface IDistributedLockBuilder
{
    Task<IRedisLock?> AcquireAsync(string resource, TimeSpan expiry, TimeSpan? timeout = null);
}

public interface IRedisLock : IAsyncDisposable
{
    string Resource { get; }
    TimeSpan Expiry { get; }
    Task<bool> ExtendAsync(TimeSpan additionalTime);
    Task<bool> IsStillValidAsync();
}

// Batch operations for high-performance scenarios
public interface IBatchBuilder
{
    IBatchCacheBuilder Cache { get; }
    Task<IBatchTransaction> CreateTransactionAsync();
    Task<IBatchPipeline> CreatePipelineAsync();
}

// Batch cache operations
public interface IBatchCacheBuilder
{
    Task<Dictionary<string, T?>> GetManyAsync<T>(params string[] keys) where T : class;
    Task<Dictionary<string, string?>> GetManyAsync(params string[] keys);
    Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null) where T : class;
    Task SetManyAsync(Dictionary<string, string> keyValuePairs, TimeSpan? expiry = null);
    Task<long> RemoveManyAsync(params string[] keys);
    Task<Dictionary<string, bool>> ExistManyAsync(params string[] keys);
}

// Transaction (MULTI/EXEC) for atomic operations
public interface IBatchTransaction : IAsyncDisposable
{
    IBatchTransactionCacheBuilder Cache { get; }
    Task<bool> ExecuteAsync();
    void Discard();
}

public interface IBatchTransactionCacheBuilder
{
    void Set<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    void Set(string key, string value, TimeSpan? expiry = null);
    void Remove(string key);
    void Increment(string key, long by = 1);
    void Decrement(string key, long by = 1);
}

// Pipeline for batching commands (better performance for multiple operations)
public interface IBatchPipeline : IAsyncDisposable
{
    IBatchPipelineCacheBuilder Cache { get; }
    Task ExecuteAsync();
}

public interface IBatchPipelineCacheBuilder
{
    Task<T?> GetAsync<T>(string key) where T : class;
    Task<string?> GetAsync(string key);
    Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
    Task SetAsync(string key, string value, TimeSpan? expiry = null);
    Task<bool> RemoveAsync(string key);
    Task<bool> ExistsAsync(string key);
}

// Configuration class for Redis connection
public class RedisConfiguration
{
    public string ConnectionString { get; set; } = "localhost:6379";
    public string? Password { get; set; }
    public int Database { get; set; } = 0;
    public bool UseSsl { get; set; } = false;
    public string? SslHost { get; set; }
    public bool AbortOnConnectFail { get; set; } = false;
    public int ConnectTimeout { get; set; } = 5000;
    public int SyncTimeout { get; set; } = 5000;
    public int AsyncTimeout { get; set; } = 5000;
    public string? ClientName { get; set; }
    public bool AllowAdmin { get; set; } = false;
    public int ConnectRetry { get; set; } = 3;
    public int KeepAlive { get; set; } = -1;
    public string? User { get; set; } // For Redis 6+ ACL
    public SerializerType SerializerType { get; set; } = SerializerType.Newtonsoft;
    public IJsonSerializer? CustomSerializer { get; set; }

    public ConfigurationOptions ToConfigurationOptions()
    {
        var config = ConfigurationOptions.Parse(ConnectionString);

        if (!string.IsNullOrEmpty(Password))
            config.Password = Password;

        if (!string.IsNullOrEmpty(User))
            config.User = User;

        config.Ssl = UseSsl;
        if (!string.IsNullOrEmpty(SslHost))
            config.SslHost = SslHost;

        config.AbortOnConnectFail = AbortOnConnectFail;
        config.ConnectTimeout = ConnectTimeout;
        config.SyncTimeout = SyncTimeout;
        config.AsyncTimeout = AsyncTimeout;
        config.ConnectRetry = ConnectRetry;
        config.KeepAlive = KeepAlive;
        config.AllowAdmin = AllowAdmin;

        if (!string.IsNullOrEmpty(ClientName))
            config.ClientName = ClientName;

        return config;
    }
}

// Implementation
public class SmoothRedisClient : ISmoothRedis, IAsyncDisposable
{
    private readonly ConnectionMultiplexer _connection;
    private readonly IDatabase _database;
    private readonly int _databaseNumber;
    private readonly IJsonSerializer _jsonSerializer;

    public ICacheBuilder Cache { get; }
    public ICollectionBuilder Collections { get; }
    public IPubSubBuilder PubSub { get; }
    public IDistributedLockBuilder Locks { get; }
    public IBatchBuilder Batch { get; }

    private SmoothRedisClient(ConnectionMultiplexer connection, int database = 0, IJsonSerializer? jsonSerializer = null)
    {
        _connection = connection;
        _databaseNumber = database;
        _database = connection.GetDatabase(database);

        // Default to Newtonsoft.Json if no serializer provided
        _jsonSerializer = jsonSerializer ?? new NewtonsoftJsonSerializer();

        Cache = new CacheBuilder(_database, _jsonSerializer);
        Collections = new CollectionBuilder(_database, _jsonSerializer);
        PubSub = new PubSubBuilder(_connection.GetSubscriber(), _jsonSerializer);
        Locks = new DistributedLockBuilder(_database);
        Batch = new BatchBuilder(_database, _jsonSerializer);
    }

    // Connect with connection string (supports password)
    public static async Task<ISmoothRedis> ConnectAsync(string connectionString, int database = 0, SerializerType serializerType = SerializerType.Newtonsoft)
    {
        var config = ParseConnectionString(connectionString);
        config.Database = database;
        config.SerializerType = serializerType;
        return await ConnectAsync(config);
    }

    // Connect with RedisConfiguration
    public static async Task<ISmoothRedis> ConnectAsync(RedisConfiguration config)
    {
        var configOptions = config.ToConfigurationOptions();
        var connection = await ConnectionMultiplexer.ConnectAsync(configOptions);

        // Use custom serializer if provided, otherwise create based on SerializerType
        IJsonSerializer serializer = config.CustomSerializer ?? (config.SerializerType == SerializerType.Newtonsoft
            ? (IJsonSerializer)new NewtonsoftJsonSerializer()
            : new SystemTextJsonSerializer());

        return new SmoothRedisClient(connection, config.Database, serializer);
    }

    // Connect with ConfigurationOptions
    public static async Task<ISmoothRedis> ConnectAsync(ConfigurationOptions config, int database = 0, SerializerType serializerType = SerializerType.Newtonsoft)
    {
        var connection = await ConnectionMultiplexer.ConnectAsync(config);
        IJsonSerializer serializer = serializerType == SerializerType.Newtonsoft
            ? new NewtonsoftJsonSerializer()
            : new SystemTextJsonSerializer();
        return new SmoothRedisClient(connection, database, serializer);
    }

    // Parse various connection string formats
    private static RedisConfiguration ParseConnectionString(string connectionString)
    {
        var config = new RedisConfiguration();

        // Handle different formats:
        // localhost:6379
        // localhost:6379,password=mypass
        // server1:6379,server2:6379,password=mypass
        // redis://username:password@hostname:port/database
        // rediss://username:password@hostname:port/database (SSL)

        if (connectionString.StartsWith("redis://") || connectionString.StartsWith("rediss://"))
        {
            var uri = new Uri(connectionString);
            config.UseSsl = uri.Scheme == "rediss";
            config.ConnectionString = $"{uri.Host}:{uri.Port}";

            if (!string.IsNullOrEmpty(uri.UserInfo))
            {
                var userInfo = uri.UserInfo.Split(':');
                if (userInfo.Length == 2)
                {
                    config.User = Uri.UnescapeDataString(userInfo[0]);
                    config.Password = Uri.UnescapeDataString(userInfo[1]);
                }
                else if (userInfo.Length == 1)
                {
                    config.Password = Uri.UnescapeDataString(userInfo[0]);
                }
            }

            if (!string.IsNullOrEmpty(uri.PathAndQuery) && uri.PathAndQuery.Length > 1)
            {
                var dbString = uri.PathAndQuery.Substring(1);
                if (int.TryParse(dbString, out var dbNumber))
                {
                    config.Database = dbNumber;
                }
            }
        }
        else
        {
            // Traditional format: host:port,password=pass,ssl=true,etc.
            var configOptions = ConfigurationOptions.Parse(connectionString);
            config.ConnectionString = connectionString;
            config.Password = configOptions.Password;
            config.User = configOptions.User;
            config.UseSsl = configOptions.Ssl;
            config.SslHost = configOptions.SslHost;
            config.ConnectTimeout = configOptions.ConnectTimeout;
            config.SyncTimeout = configOptions.SyncTimeout;
            config.AsyncTimeout = configOptions.AsyncTimeout;
            config.AbortOnConnectFail = configOptions.AbortOnConnectFail;
            config.AllowAdmin = configOptions.AllowAdmin;
            config.ClientName = configOptions.ClientName;
            config.ConnectRetry = configOptions.ConnectRetry;
            config.KeepAlive = configOptions.KeepAlive;
        }

        return config;
    }

    public ISmoothRedis WithDatabase(int database)
    {
        return new SmoothRedisClient(_connection, database, _jsonSerializer);
    }

    public async Task<bool> HealthCheckAsync()
    {
        try
        {
            await _database.PingAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_connection != null)
        {
            await _connection.CloseAsync();
            _connection.Dispose();
        }
    }
}

// Static factory for easier access
public static class SmoothRedis
{
    /// <summary>
    /// Connect to Redis with connection string
    /// </summary>
    public static async Task<ISmoothRedis> ConnectAsync(string connectionString, int database = 0, SerializerType serializerType = SerializerType.Newtonsoft)
    {
        return await SmoothRedisClient.ConnectAsync(connectionString, database, serializerType);
    }

    /// <summary>
    /// Connect to Redis with RedisConfiguration
    /// </summary>
    public static async Task<ISmoothRedis> ConnectAsync(RedisConfiguration config)
    {
        return await SmoothRedisClient.ConnectAsync(config);
    }

    /// <summary>
    /// Connect to Redis with ConfigurationOptions
    /// </summary>
    public static async Task<ISmoothRedis> ConnectAsync(ConfigurationOptions config, int database = 0, SerializerType serializerType = SerializerType.Newtonsoft)
    {
        return await SmoothRedisClient.ConnectAsync(config, database, serializerType);
    }
}

// Cache implementation with automatic serialization
internal class CacheBuilder : ICacheBuilder
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private string _prefix = "";
    private TimeSpan? _defaultExpiry;

    public CacheBuilder(IDatabase database, IJsonSerializer jsonSerializer)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
    }

    private string GetKey(string key) => string.IsNullOrEmpty(_prefix) ? key : $"{_prefix}:{key}";

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        var value = await _database.StringGetAsync(GetKey(key));
        return value.IsNull ? null : _jsonSerializer.Deserialize<T>(value!);
    }

    public async Task<string?> GetAsync(string key)
    {
        var value = await _database.StringGetAsync(GetKey(key));
        return value.IsNull ? null : value.ToString();
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        var json = _jsonSerializer.Serialize(value);
        await _database.StringSetAsync(GetKey(key), json, expiry ?? _defaultExpiry);
    }

    public async Task SetAsync(string key, string value, TimeSpan? expiry = null)
    {
        await _database.StringSetAsync(GetKey(key), value, expiry ?? _defaultExpiry);
    }

    public async Task<bool> ExistsAsync(string key)
    {
        return await _database.KeyExistsAsync(GetKey(key));
    }

    public async Task<bool> RemoveAsync(string key)
    {
        return await _database.KeyDeleteAsync(GetKey(key));
    }

    public async Task<long> RemoveAsync(params string[] keys)
    {
        var redisKeys = keys.Select(k => (RedisKey)GetKey(k)).ToArray();
        return await _database.KeyDeleteAsync(redisKeys);
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null) where T : class
    {
        var cached = await GetAsync<T>(key);
        if (cached != null) return cached;

        var value = await factory();
        await SetAsync(key, value, expiry);
        return value;
    }

    public async Task<string> GetOrSetAsync(string key, Func<Task<string>> factory, TimeSpan? expiry = null)
    {
        var cached = await GetAsync(key);
        if (cached != null) return cached;

        var value = await factory();
        await SetAsync(key, value, expiry);
        return value;
    }

    public async Task<TimeSpan?> GetKeyTTLAsync(string key)
    {
        return await _database.KeyTimeToLiveAsync(GetKey(key));
    }

    public ICacheBuilder WithPrefix(string prefix)
    {
        return new CacheBuilder(_database, _jsonSerializer) { _prefix = prefix, _defaultExpiry = _defaultExpiry };
    }

    public ICacheBuilder WithDefaultExpiry(TimeSpan expiry)
    {
        return new CacheBuilder(_database, _jsonSerializer) { _prefix = _prefix, _defaultExpiry = expiry };
    }
}

// Collection builder
internal class CollectionBuilder : ICollectionBuilder
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;

    public CollectionBuilder(IDatabase database, IJsonSerializer jsonSerializer)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
    }

    public IListBuilder<T> List<T>(string key) where T : class
        => new ListBuilder<T>(_database, _jsonSerializer, key);

    public ISetBuilder<T> Set<T>(string key) where T : class
        => new SetBuilder<T>(_database, _jsonSerializer, key);

    public ISortedSetBuilder<T> SortedSet<T>(string key) where T : class
        => new SortedSetBuilder<T>(_database, _jsonSerializer, key);

    public IHashBuilder Hash(string key)
        => new HashBuilder(_database, _jsonSerializer, key);

    public ICounterBuilder Counter(string key)
        => new CounterBuilder(_database, key);
}

// List implementation
internal class ListBuilder<T> : IListBuilder<T> where T : class
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly string _key;

    public ListBuilder(IDatabase database, IJsonSerializer jsonSerializer, string key)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        _key = key;
    }

    public async Task<long> PushAsync(T item, bool toLeft = true)
    {
        var json = _jsonSerializer.Serialize(item);
        return toLeft
            ? await _database.ListLeftPushAsync(_key, json)
            : await _database.ListRightPushAsync(_key, json);
    }

    public async Task<long> PushAsync(IEnumerable<T> items, bool toLeft = true)
    {
        var values = items.Select(item => (RedisValue)_jsonSerializer.Serialize(item)).ToArray();
        return toLeft
            ? await _database.ListLeftPushAsync(_key, values)
            : await _database.ListRightPushAsync(_key, values);
    }

    public async Task<T?> PopAsync(bool fromLeft = true)
    {
        var value = fromLeft
            ? await _database.ListLeftPopAsync(_key)
            : await _database.ListRightPopAsync(_key);

        return value.IsNull ? null : _jsonSerializer.Deserialize<T>(value!);
    }

    public async Task<List<T>> RangeAsync(int start = 0, int end = -1)
    {
        var values = await _database.ListRangeAsync(_key, start, end);
        return values.Select(v => _jsonSerializer.Deserialize<T>(v!)!).ToList();
    }

    public async Task<long> LengthAsync()
    {
        return await _database.ListLengthAsync(_key);
    }

    public async Task<List<T>> TrimAndGetAsync(int maxLength)
    {
        var length = await LengthAsync();
        if (length > maxLength)
        {
            await _database.ListTrimAsync(_key, 0, maxLength - 1);
        }
        return await RangeAsync();
    }
}

// Set implementation
internal class SetBuilder<T> : ISetBuilder<T> where T : class
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly string _key;

    public SetBuilder(IDatabase database, IJsonSerializer jsonSerializer, string key)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        _key = key;
    }

    public async Task<bool> AddAsync(T item)
    {
        var json = _jsonSerializer.Serialize(item);
        return await _database.SetAddAsync(_key, json);
    }

    public async Task<long> AddAsync(IEnumerable<T> items)
    {
        var values = items.Select(item => (RedisValue)_jsonSerializer.Serialize(item)).ToArray();
        return await _database.SetAddAsync(_key, values);
    }

    public async Task<bool> RemoveAsync(T item)
    {
        var json = _jsonSerializer.Serialize(item);
        return await _database.SetRemoveAsync(_key, json);
    }

    public async Task<bool> ContainsAsync(T item)
    {
        var json = _jsonSerializer.Serialize(item);
        return await _database.SetContainsAsync(_key, json);
    }

    public async Task<List<T>> MembersAsync()
    {
        var values = await _database.SetMembersAsync(_key);
        return values.Select(v => _jsonSerializer.Deserialize<T>(v!)!).ToList();
    }

    public async Task<long> CountAsync()
    {
        return await _database.SetLengthAsync(_key);
    }

    public async Task<T?> RandomMemberAsync()
    {
        var value = await _database.SetRandomMemberAsync(_key);
        return value.IsNull ? null : _jsonSerializer.Deserialize<T>(value!);
    }
}

// Sorted Set implementation
internal class SortedSetBuilder<T> : ISortedSetBuilder<T> where T : class
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly string _key;

    public SortedSetBuilder(IDatabase database, IJsonSerializer jsonSerializer, string key)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        _key = key;
    }

    public async Task<bool> AddAsync(T item, double score)
    {
        var json = _jsonSerializer.Serialize(item);
        return await _database.SortedSetAddAsync(_key, json, score);
    }

    public async Task<long> AddAsync(Dictionary<T, double> items)
    {
        var entries = items.Select(kvp => new SortedSetEntry(
            _jsonSerializer.Serialize(kvp.Key), kvp.Value)).ToArray();
        return await _database.SortedSetAddAsync(_key, entries);
    }

    public async Task<List<T>> RangeAsync(int start = 0, int end = -1, bool reverse = false)
    {
        var order = reverse ? Order.Descending : Order.Ascending;
        var values = await _database.SortedSetRangeByRankAsync(_key, start, end, order);
        return values.Select(v => _jsonSerializer.Deserialize<T>(v!)!).ToList();
    }

    public async Task<List<(T item, double score)>> RangeWithScoresAsync(int start = 0, int end = -1, bool reverse = false)
    {
        var order = reverse ? Order.Descending : Order.Ascending;
        var values = await _database.SortedSetRangeByRankWithScoresAsync(_key, start, end, order);
        return values.Select(v => (
            _jsonSerializer.Deserialize<T>(v.Element!)!,
            v.Score
        )).ToList();
    }

    public async Task<double?> ScoreAsync(T item)
    {
        var json = _jsonSerializer.Serialize(item);
        return await _database.SortedSetScoreAsync(_key, json);
    }

    public async Task<long?> RankAsync(T item, bool reverse = false)
    {
        var json = _jsonSerializer.Serialize(item);
        var order = reverse ? Order.Descending : Order.Ascending;
        return await _database.SortedSetRankAsync(_key, json, order);
    }

    public async Task<List<T>> TopAsync(int count)
    {
        return await RangeAsync(0, count - 1, reverse: true);
    }
}

// Hash implementation
internal class HashBuilder : IHashBuilder
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly string _key;

    public HashBuilder(IDatabase database, IJsonSerializer jsonSerializer, string key)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        _key = key;
    }

    public async Task<T?> GetAsync<T>(string field) where T : class
    {
        var value = await _database.HashGetAsync(_key, field);
        return value.IsNull ? null : _jsonSerializer.Deserialize<T>(value!);
    }

    public async Task<string?> GetAsync(string field)
    {
        var value = await _database.HashGetAsync(_key, field);
        return value.IsNull ? null : value.ToString();
    }

    public async Task SetAsync<T>(string field, T value) where T : class
    {
        var json = _jsonSerializer.Serialize(value);
        await _database.HashSetAsync(_key, field, json);
    }

    public async Task SetAsync(string field, string value)
    {
        await _database.HashSetAsync(_key, field, value);
    }

    public async Task SetAsync(Dictionary<string, object> fields)
    {
        var entries = fields.Select(kvp => new HashEntry(kvp.Key,
            kvp.Value is string str ? str : _jsonSerializer.Serialize(kvp.Value))).ToArray();
        await _database.HashSetAsync(_key, entries);
    }

    public async Task<bool> ExistsAsync(string field)
    {
        return await _database.HashExistsAsync(_key, field);
    }

    public async Task<bool> RemoveAsync(string field)
    {
        return await _database.HashDeleteAsync(_key, field);
    }

    public async Task<Dictionary<string, string>> GetAllAsync()
    {
        var hash = await _database.HashGetAllAsync(_key);
        return hash.ToDictionary(h => h.Name.ToString(), h => h.Value.ToString());
    }

    public async Task<T?> GetAsTypeAsync<T>() where T : class, new()
    {
        var hash = await GetAllAsync();
        if (!hash.Any()) return null;

        var json = _jsonSerializer.Serialize(hash);
        return _jsonSerializer.Deserialize<T>(json);
    }

    public async Task SetAsTypeAsync<T>(T obj) where T : class
    {
        var json = _jsonSerializer.Serialize(obj);
        var dict = _jsonSerializer.Deserialize<Dictionary<string, object>>(json);
        if (dict != null)
        {
            await SetAsync(dict);
        }
    }
}

// Counter implementation
internal class CounterBuilder : ICounterBuilder
{
    private readonly IDatabase _database;
    private readonly string _key;

    public CounterBuilder(IDatabase database, string key)
    {
        _database = database;
        _key = key;
    }

    public async Task<long> IncrementAsync(long by = 1)
    {
        return await _database.StringIncrementAsync(_key, by);
    }

    public async Task<long> DecrementAsync(long by = 1)
    {
        return await _database.StringDecrementAsync(_key, by);
    }

    public async Task<long> GetAsync()
    {
        var value = await _database.StringGetAsync(_key);
        return value.IsNull ? 0 : (long)value;
    }

    public async Task SetAsync(long value)
    {
        await _database.StringSetAsync(_key, value);
    }

    public async Task<bool> ResetAsync()
    {
        return await _database.KeyDeleteAsync(_key);
    }
}

// PubSub implementation
internal class PubSubBuilder : IPubSubBuilder
{
    private readonly ISubscriber _subscriber;
    private readonly IJsonSerializer _jsonSerializer;

    public PubSubBuilder(ISubscriber subscriber, IJsonSerializer jsonSerializer)
    {
        _subscriber = subscriber;
        _jsonSerializer = jsonSerializer;
    }

    public async Task<long> PublishAsync<T>(string channel, T message) where T : class
    {
        var json = _jsonSerializer.Serialize(message);
        return await _subscriber.PublishAsync(RedisChannel.Literal(channel), json);
    }

    public async Task<long> PublishAsync(string channel, string message)
    {
        return await _subscriber.PublishAsync(RedisChannel.Literal(channel), message);
    }

    public async Task SubscribeAsync<T>(string channel, Func<T, Task> handler) where T : class
    {
        await _subscriber.SubscribeAsync(RedisChannel.Literal(channel), async (ch, message) =>
        {
            var obj = _jsonSerializer.Deserialize<T>(message!);
            if (obj != null)
            {
                await handler(obj);
            }
        });
    }

    public async Task SubscribeAsync(string channel, Func<string, Task> handler)
    {
        await _subscriber.SubscribeAsync(RedisChannel.Literal(channel), async (ch, message) =>
        {
            await handler(message!);
        });
    }

    public async Task UnsubscribeAsync(string channel)
    {
        await _subscriber.UnsubscribeAsync(RedisChannel.Literal(channel));
    }
}

// Distributed lock implementation
internal class DistributedLockBuilder : IDistributedLockBuilder
{
    private readonly IDatabase _database;

    public DistributedLockBuilder(IDatabase database)
    {
        _database = database;
    }

    public async Task<IRedisLock?> AcquireAsync(string resource, TimeSpan expiry, TimeSpan? timeout = null)
    {
        var lockKey = $"lock:{resource}";
        var lockValue = Guid.NewGuid().ToString();
        var deadline = timeout.HasValue ? DateTime.UtcNow.Add(timeout.Value) : (DateTime?)null;

        while (true)
        {
            if (await _database.StringSetAsync(lockKey, lockValue, expiry, When.NotExists))
            {
                return new RedisLock(_database, lockKey, lockValue, expiry);
            }

            if (deadline.HasValue && DateTime.UtcNow > deadline)
            {
                break;
            }

            await Task.Delay(20);
        }

        return null;
    }
}

internal class RedisLock : IRedisLock
{
    private readonly IDatabase _database;
    private readonly string _key;
    private readonly string _value;

    public string Resource { get; }
    public TimeSpan Expiry { get; }

    public RedisLock(IDatabase database, string key, string value, TimeSpan expiry)
    {
        _database = database;
        _key = key;
        _value = value;
        Resource = key.Substring(5); // Remove "lock:" prefix
        Expiry = expiry;
    }

    public async Task<bool> ExtendAsync(TimeSpan additionalTime)
    {
        const string script = @"
                if redis.call('GET', KEYS[1]) == ARGV[1] then
                    return redis.call('PEXPIRE', KEYS[1], ARGV[2])
                else
                    return 0
                end";

        var result = await _database.ScriptEvaluateAsync(script, new RedisKey[] { _key },
            new RedisValue[] { _value, (long)additionalTime.TotalMilliseconds });

        return (int)result == 1;
    }

    public async Task<bool> IsStillValidAsync()
    {
        var value = await _database.StringGetAsync(_key);
        return value == _value;
    }

    public async ValueTask DisposeAsync()
    {
        const string script = @"
                if redis.call('GET', KEYS[1]) == ARGV[1] then
                    return redis.call('DEL', KEYS[1])
                else
                    return 0
                end";

        await _database.ScriptEvaluateAsync(script, new RedisKey[] { _key }, new RedisValue[] { _value });
    }
}

// Batch operations implementation
internal class BatchBuilder : IBatchBuilder
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;

    public IBatchCacheBuilder Cache { get; }

    public BatchBuilder(IDatabase database, IJsonSerializer jsonSerializer)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        Cache = new BatchCacheBuilder(database, jsonSerializer);
    }

    public async Task<IBatchTransaction> CreateTransactionAsync()
    {
        return await Task.FromResult<IBatchTransaction>(new BatchTransaction(_database, _jsonSerializer));
    }

    public async Task<IBatchPipeline> CreatePipelineAsync()
    {
        return await Task.FromResult<IBatchPipeline>(new BatchPipeline(_database, _jsonSerializer));
    }
}

// Batch cache operations implementation
internal class BatchCacheBuilder : IBatchCacheBuilder
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;

    public BatchCacheBuilder(IDatabase database, IJsonSerializer jsonSerializer)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
    }

    public async Task<Dictionary<string, T?>> GetManyAsync<T>(params string[] keys) where T : class
    {
        if (keys == null || keys.Length == 0)
            return new Dictionary<string, T?>();

        var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
        var values = await _database.StringGetAsync(redisKeys);

        var result = new Dictionary<string, T?>();
        for (int i = 0; i < keys.Length; i++)
        {
            result[keys[i]] = values[i].IsNull ? null : _jsonSerializer.Deserialize<T>(values[i]!);
        }

        return result;
    }

    public async Task<Dictionary<string, string?>> GetManyAsync(params string[] keys)
    {
        if (keys == null || keys.Length == 0)
            return new Dictionary<string, string?>();

        var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
        var values = await _database.StringGetAsync(redisKeys);

        var result = new Dictionary<string, string?>();
        for (int i = 0; i < keys.Length; i++)
        {
            result[keys[i]] = values[i].IsNull ? null : values[i].ToString();
        }

        return result;
    }

    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null) where T : class
    {
        if (keyValuePairs == null || keyValuePairs.Count == 0)
            return;

        var batch = _database.CreateBatch();
        var tasks = new List<Task>();

        foreach (var kvp in keyValuePairs)
        {
            var json = _jsonSerializer.Serialize(kvp.Value);
            tasks.Add(batch.StringSetAsync(kvp.Key, json, expiry));
        }

        batch.Execute();
        await Task.WhenAll(tasks);
    }

    public async Task SetManyAsync(Dictionary<string, string> keyValuePairs, TimeSpan? expiry = null)
    {
        if (keyValuePairs == null || keyValuePairs.Count == 0)
            return;

        var batch = _database.CreateBatch();
        var tasks = new List<Task>();

        foreach (var kvp in keyValuePairs)
        {
            tasks.Add(batch.StringSetAsync(kvp.Key, kvp.Value, expiry));
        }

        batch.Execute();
        await Task.WhenAll(tasks);
    }

    public async Task<long> RemoveManyAsync(params string[] keys)
    {
        if (keys == null || keys.Length == 0)
            return 0;

        var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
        return await _database.KeyDeleteAsync(redisKeys);
    }

    public async Task<Dictionary<string, bool>> ExistManyAsync(params string[] keys)
    {
        if (keys == null || keys.Length == 0)
            return new Dictionary<string, bool>();

        var batch = _database.CreateBatch();
        var tasks = new Dictionary<string, Task<bool>>();

        foreach (var key in keys)
        {
            tasks[key] = batch.KeyExistsAsync(key);
        }

        batch.Execute();
        await Task.WhenAll(tasks.Values);

        return tasks.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Result);
    }
}

// Pipeline implementation for batching commands
internal class BatchPipeline : IBatchPipeline
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly IBatch _batch;
    private readonly List<Task> _tasks;

    public IBatchPipelineCacheBuilder Cache { get; }

    public BatchPipeline(IDatabase database, IJsonSerializer jsonSerializer)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        _batch = database.CreateBatch();
        _tasks = new List<Task>();
        Cache = new BatchPipelineCacheBuilder(_batch, _jsonSerializer, _tasks);
    }

    public async Task ExecuteAsync()
    {
        _batch.Execute();
        await Task.WhenAll(_tasks);
    }

    public async ValueTask DisposeAsync()
    {
        await Task.CompletedTask;
    }
}

// Pipeline cache builder
internal class BatchPipelineCacheBuilder : IBatchPipelineCacheBuilder
{
    private readonly IBatch _batch;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly List<Task> _tasks;

    public BatchPipelineCacheBuilder(IBatch batch, IJsonSerializer jsonSerializer, List<Task> tasks)
    {
        _batch = batch;
        _jsonSerializer = jsonSerializer;
        _tasks = tasks;
    }

    public Task<T?> GetAsync<T>(string key) where T : class
    {
        var task = _batch.StringGetAsync(key);
        _tasks.Add(task);

        return task.ContinueWith(t =>
        {
            if (t.Result.IsNull)
                return null;
            return _jsonSerializer.Deserialize<T>(t.Result!);
        });
    }

    public Task<string?> GetAsync(string key)
    {
        var task = _batch.StringGetAsync(key);
        _tasks.Add(task);

        return task.ContinueWith(t =>
        {
            if (t.Result.IsNull)
                return null;
            return t.Result.ToString();
        });
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        var json = _jsonSerializer.Serialize(value);
        var task = _batch.StringSetAsync(key, json, expiry);
        _tasks.Add(task);
        return task;
    }

    public Task SetAsync(string key, string value, TimeSpan? expiry = null)
    {
        var task = _batch.StringSetAsync(key, value, expiry);
        _tasks.Add(task);
        return task;
    }

    public Task<bool> RemoveAsync(string key)
    {
        var task = _batch.KeyDeleteAsync(key);
        _tasks.Add(task);
        return task;
    }

    public Task<bool> ExistsAsync(string key)
    {
        var task = _batch.KeyExistsAsync(key);
        _tasks.Add(task);
        return task;
    }
}

// Transaction implementation for atomic operations (MULTI/EXEC)
internal class BatchTransaction : IBatchTransaction
{
    private readonly IDatabase _database;
    private readonly IJsonSerializer _jsonSerializer;
    private readonly ITransaction _transaction;
    private bool _isDiscarded;

    public IBatchTransactionCacheBuilder Cache { get; }

    public BatchTransaction(IDatabase database, IJsonSerializer jsonSerializer)
    {
        _database = database;
        _jsonSerializer = jsonSerializer;
        _transaction = database.CreateTransaction();
        _isDiscarded = false;
        Cache = new BatchTransactionCacheBuilder(_transaction, _jsonSerializer);
    }

    public async Task<bool> ExecuteAsync()
    {
        if (_isDiscarded)
            throw new InvalidOperationException("Transaction has been discarded");

        return await _transaction.ExecuteAsync();
    }

    public void Discard()
    {
        _isDiscarded = true;
    }

    public async ValueTask DisposeAsync()
    {
        await Task.CompletedTask;
    }
}

// Transaction cache builder
internal class BatchTransactionCacheBuilder : IBatchTransactionCacheBuilder
{
    private readonly ITransaction _transaction;
    private readonly IJsonSerializer _jsonSerializer;

    public BatchTransactionCacheBuilder(ITransaction transaction, IJsonSerializer jsonSerializer)
    {
        _transaction = transaction;
        _jsonSerializer = jsonSerializer;
    }

    public void Set<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        var json = _jsonSerializer.Serialize(value);
        _transaction.StringSetAsync(key, json, expiry);
    }

    public void Set(string key, string value, TimeSpan? expiry = null)
    {
        _transaction.StringSetAsync(key, value, expiry);
    }

    public void Remove(string key)
    {
        _transaction.KeyDeleteAsync(key);
    }

    public void Increment(string key, long by = 1)
    {
        _transaction.StringIncrementAsync(key, by);
    }

    public void Decrement(string key, long by = 1)
    {
        _transaction.StringDecrementAsync(key, by);
    }
}
