﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.SharedKernal.RabbitMq.Configuration;
using TMS.SharedKernal.RabbitMq.Serialization;
using TMS.SharedKernal.RabbitMq.Services;

namespace TMS.SharedKernal.RabbitMq.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRabbitMq(
        this IServiceCollection services,
        IConfiguration configuration, params string[] serviceKeys)
    {
        services.TryAddSingleton<IEventSerializer, JsonEventSerializer>();

        // server: rabbitmq, rabbitmq2
        foreach (var serviceKey in serviceKeys) 
        {
            var exchanges = new RabbitMQConfiguration();
            configuration.GetSection(serviceKey).Bind(exchanges);

            // exchanges: orders, payments
            foreach (var item in exchanges.Exchanges)
            {
                var options = new RabbitMqOptions()
                {
                    HostName = exchanges.HostName,
                    Port = exchanges.Port,
                    UserName = exchanges.UserName,
                    Password = exchanges.Password,
                    VirtualHost = exchanges.VirtualHost,
                    ConnectionRetryCount = exchanges.ConnectionRetryCount,
                    ConnectionRetryDelay = exchanges.ConnectionRetryDelay,
                    Exchange = item
                };

                // Register keyed services
                services.AddKeyedSingleton<IEventPublisher>($"{serviceKey}:{item.Name}", (provider, key) =>
                {
                    var eventSerializer = provider.GetRequiredService<IEventSerializer>();
                    var logger = provider.GetRequiredService<ILogger<RabbitMqEventPublisher>>();
                    return new RabbitMqEventPublisher(options, eventSerializer, logger);
                });

                services.AddKeyedScoped<IEventConsumer>($"{serviceKey}:{item.Name}", (provider, key) =>
                {
                    var serializer = provider.GetRequiredService<IEventSerializer>();
                    var logger = provider.GetRequiredService<ILogger<RabbitMqEventConsumer>>();
                    return new RabbitMqEventConsumer(options, serializer, logger);
                });

            }
        }

        return services;
    }

    public static IServiceCollection AddEventHandler<TEvent, THandler>(this IServiceCollection services)
        where TEvent : class, IEvent
        where THandler : class, IEventHandler<TEvent>
    {
        services.AddScoped<IEventHandler<TEvent>, THandler>();
        return services;
    }

    public static IServiceCollection AddEventConsumer<T>(
        this IServiceCollection services,
        string serviceKey,
        string queueName) where T : class, IEvent
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentException.ThrowIfNullOrWhiteSpace(serviceKey);
        ArgumentException.ThrowIfNullOrWhiteSpace(queueName);

        services.AddHostedService(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<RabbitMqBackgroundService<T>>>();

            try
            {
                return new RabbitMqBackgroundService<T>(
                    provider,
                    logger,
                    serviceKey,
                    queueName);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create RabbitMqBackgroundService for event type: {EventType}, ServiceKey: {ServiceKey}, QueueName: {QueueName}",
                    typeof(T).Name, serviceKey, queueName);
                throw;
            }
        });

        return services;
    }
}
