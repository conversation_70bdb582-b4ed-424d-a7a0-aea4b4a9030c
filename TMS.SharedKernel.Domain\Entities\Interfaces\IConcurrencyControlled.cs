﻿using System.ComponentModel.DataAnnotations;

namespace TMS.SharedKernel.Domain.Entities.Interfaces;

/// <summary>
/// Interface for entities that are concurrency-controlled based on
/// native database-generated concurrency tokens.
/// For more details, see the
/// <see href="https://learn.microsoft.com/en-us/ef/core/saving/concurrency?tabs=data-annotations#native-database-generated-concurrency-tokens">Microsoft documentation on concurrency tokens</see>.
/// </summary>
public interface IConcurrencyControlled
{
    /// <summary>
    /// Version is version row of the object in the database, which is used to check concurrency
    /// </summary>
    [Timestamp] public byte[] Version { get; set; }
}
