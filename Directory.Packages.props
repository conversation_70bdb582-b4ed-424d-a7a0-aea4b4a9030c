﻿<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <!--<ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" Version="9.0.5" />
    </ItemGroup>-->
  <ItemGroup>
    <!-- Logging -->
    <PackageVersion Include="Microsoft.AspNetCore.Authorization" Version="9.0.8" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageVersion Include="Serilog" Version="4.3.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.Testing" Version="9.4.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- JSON Handling -->
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageVersion Include="System.Text.Json" Version="9.0.3" />
    <PackageVersion Include="JsonPath.Net" Version="2.1.1" />
  </ItemGroup>
  <ItemGroup>
    <!-- Swashbuckle -->
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="8.1.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- AutoMapper -->
    <PackageVersion Include="AutoMapper" Version="14.0.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- MediatR -->
    <PackageVersion Include="MediatR" Version="13.0.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- Quartz -->
    <PackageVersion Include="Quartz.AspNetCore" Version="3.14.0" />
    <PackageVersion Include="Quartz.Serialization.SystemTextJson" Version="3.14.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- Validation -->
    <PackageVersion Include="FluentValidation" Version="11.5.1" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.5.1" />
  </ItemGroup>
  <ItemGroup>
    <!-- Dependency Injection -->
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup>
    <!-- Entity Framework Core -->
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.14" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.14" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.14" />
  </ItemGroup>
  <ItemGroup>
    <!-- Messaging -->
    <PackageVersion Include="Microsoft.Identity.Web" Version="3.8.2" />
    <PackageVersion Include="DotNetCore.CAP" Version="8.3.5" />
    <PackageVersion Include="DotNetCore.CAP.Kafka" Version="8.3.4" />
    <PackageVersion Include="DotNetCore.CAP.SqlServer" Version="8.3.4" />
  </ItemGroup>
  <ItemGroup>
    <!-- Benchmarking -->
    <PackageVersion Include="BenchmarkDotNet" Version="0.14.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- Bogus for Data Generation -->
    <PackageVersion Include="Bogus" Version="35.6.3" />
  </ItemGroup>
  <ItemGroup>
    <!-- OpenTelemetry for Tracing -->
    <PackageVersion Include="OpenTelemetry" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Api" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- HTTP Client -->
    <PackageVersion Include="MailKit" Version="4.11.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <!-- Other -->
    <PackageVersion Include="DocumentFormat.OpenXml" Version="3.3.0" />
    <PackageVersion Include="MartinCostello.SqlLocalDb" Version="3.4.0" />
    <PackageVersion Include="Asp.Versioning.Http" Version="8.1.0" />
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageVersion Include="Vsxmd" Version="1.4.6-rc1" />
  </ItemGroup>
  <ItemGroup>
    <!-- Testing -->
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
    <PackageVersion Include="MSTest.TestAdapter" Version="3.4.3" />
    <PackageVersion Include="MSTest.TestFramework" Version="3.4.3" />
    <PackageVersion Include="FakeItEasy" Version="8.3.0" />
    <PackageVersion Include="FluentAssertions" Version="6.12.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Shouldly" Version="4.3.0" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.4.0" />
  </ItemGroup>
</Project>