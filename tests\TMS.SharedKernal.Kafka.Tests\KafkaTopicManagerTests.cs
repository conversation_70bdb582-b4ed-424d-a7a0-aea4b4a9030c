using Microsoft.Extensions.Logging;
using Moq;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.Kafka.Configuration;
using TMS.SharedKernal.Kafka.Services;
using Xunit;

namespace TMS.SharedKernal.Kafka.Tests;

public class KafkaTopicManagerTests
{
    private readonly Mock<ILogger<KafkaTopicManager>> _loggerMock;
    private readonly KafkaFlowOptions _options;

    public KafkaTopicManagerTests()
    {
        _loggerMock = new Mock<ILogger<KafkaTopicManager>>();
        _options = new KafkaFlowOptions
        {
            BootstrapServers = "localhost:9092",
            DeadLetter = new DeadLetterOptions
            {
                Enabled = true,
                TopicName = "test-dlq",
                MaxRetries = 3
            },
            Topics = new List<TopicConfiguration>
            {
                new TopicConfiguration
                {
                    TopicName = "test-topic",
                    ConsumerGroup = "test-group",
                    Partitions = 3,
                    ReplicationFactor = 2,
                    AutoCreateTopic = true,
                    MessageType = typeof(TestMessage),
                    HandlerType = typeof(TestMessageHandler)
                },
                new TopicConfiguration
                {
                    TopicName = "test-topic-2",
                    ConsumerGroup = "test-group-2",
                    Partitions = 1,
                    ReplicationFactor = 1,
                    AutoCreateTopic = true,
                    MessageType = typeof(TestMessage),
                    HandlerType = typeof(TestMessageHandler)
                }
            }
        };
    }

    [Fact]
    public void Constructor_WithValidOptions_CreatesInstance()
    {
        // Act
        var topicManager = new KafkaTopicManager(_options, _loggerMock.Object);

        // Assert
        Assert.NotNull(topicManager);
    }

    [Fact]
    public void Constructor_WithEmptyBootstrapServers_CreatesInstance()
    {
        // Arrange
        var invalidOptions = new KafkaFlowOptions
        {
            BootstrapServers = string.Empty
        };

        // Act
        // Constructor doesn't validate - validation happens at Kafka client level
        var topicManager = new KafkaTopicManager(invalidOptions, _loggerMock.Object);

        // Assert
        Assert.NotNull(topicManager);
    }

    [Fact]
    public async Task CreateAllTopicsAsync_WithMultipleTopics_CreatesAllTopicsAndRetryTopics()
    {
        // This is an integration test that would require a real Kafka broker
        // For unit testing, we'll verify the configuration is correct
        Assert.Equal(2, _options.Topics.Count);
        Assert.True(_options.Topics.All(t => t.AutoCreateTopic));

        // Verify retry topics would be created
        var expectedTopics = new List<string>
        {
            "test-topic",
            "test-topic.retry",
            "test-topic-2",
            "test-topic-2.retry",
            "test-dlq"
        };

        // In a real scenario, these topics would be created
        Assert.Equal(5, expectedTopics.Count);
    }

    [Fact]
    public void TopicConfiguration_WithAutoCreate_IsCorrect()
    {
        // Arrange
        var topic = _options.Topics[0];

        // Assert
        Assert.Equal("test-topic", topic.TopicName);
        Assert.Equal(3, topic.Partitions);
        Assert.Equal(2, topic.ReplicationFactor);
        Assert.True(topic.AutoCreateTopic);
    }

    [Fact]
    public void DeadLetterOptions_WhenEnabled_IsConfiguredCorrectly()
    {
        // Assert
        Assert.True(_options.DeadLetter.Enabled);
        Assert.Equal("test-dlq", _options.DeadLetter.TopicName);
        Assert.Equal(3, _options.DeadLetter.MaxRetries);
    }

    [Fact]
    public void RetryTopicNaming_FollowsConvention()
    {
        // Arrange
        var mainTopic = "orders";
        var expectedRetryTopic = "orders.retry";

        // Assert
        Assert.Equal(expectedRetryTopic, $"{mainTopic}.retry");
    }

    [Theory]
    [InlineData("orders", "orders.retry")]
    [InlineData("payments", "payments.retry")]
    [InlineData("user-events", "user-events.retry")]
    [InlineData("system.notifications", "system.notifications.retry")]
    public void RetryTopicNaming_WithVariousTopics_FollowsConvention(string mainTopic, string expectedRetryTopic)
    {
        // Act
        var actualRetryTopic = $"{mainTopic}.retry";

        // Assert
        Assert.Equal(expectedRetryTopic, actualRetryTopic);
    }

    [Fact]
    public void TopicList_WithAutoCreate_IncludesRetryTopics()
    {
        // Arrange
        var topicsToCreate = new List<string>();

        foreach (var topicConfig in _options.Topics)
        {
            if (topicConfig.AutoCreateTopic)
            {
                topicsToCreate.Add(topicConfig.TopicName);
                topicsToCreate.Add($"{topicConfig.TopicName}.retry");
            }
        }

        // Add DLQ
        if (_options.DeadLetter.Enabled)
        {
            topicsToCreate.Add(_options.DeadLetter.TopicName);
        }

        // Assert
        Assert.Contains("test-topic", topicsToCreate);
        Assert.Contains("test-topic.retry", topicsToCreate);
        Assert.Contains("test-topic-2", topicsToCreate);
        Assert.Contains("test-topic-2.retry", topicsToCreate);
        Assert.Contains("test-dlq", topicsToCreate);
        Assert.Equal(5, topicsToCreate.Count);
    }

    [Fact]
    public void TopicList_WithDisabledAutoCreate_ExcludesTopi()
    {
        // Arrange
        _options.Topics[0].AutoCreateTopic = false;
        _options.Topics[1].AutoCreateTopic = false;

        var topicsToCreate = new List<string>();

        foreach (var topicConfig in _options.Topics)
        {
            if (topicConfig.AutoCreateTopic)
            {
                topicsToCreate.Add(topicConfig.TopicName);
            }
        }

        // Assert
        Assert.Empty(topicsToCreate);
    }

    [Fact]
    public void TopicConfiguration_Partitions_CanBeCustomized()
    {
        // Arrange & Act
        var topic1 = _options.Topics[0];
        var topic2 = _options.Topics[1];

        // Assert
        Assert.Equal(3, topic1.Partitions);
        Assert.Equal(1, topic2.Partitions);
    }

    [Fact]
    public void TopicConfiguration_ReplicationFactor_CanBeCustomized()
    {
        // Arrange & Act
        var topic1 = _options.Topics[0];
        var topic2 = _options.Topics[1];

        // Assert
        Assert.Equal(2, topic1.ReplicationFactor);
        Assert.Equal(1, topic2.ReplicationFactor);
    }

    [Fact]
    public void TopicConfiguration_WithZeroPartitions_IsInvalid()
    {
        // Arrange
        var invalidTopic = new TopicConfiguration
        {
            TopicName = "invalid-topic",
            Partitions = 0,
            ReplicationFactor = 1
        };

        // Assert
        Assert.True(invalidTopic.Partitions <= 0, "Partitions should be greater than 0");
    }

    [Fact]
    public void TopicConfiguration_WithNegativePartitions_IsInvalid()
    {
        // Arrange
        var invalidTopic = new TopicConfiguration
        {
            TopicName = "invalid-topic",
            Partitions = -1,
            ReplicationFactor = 1
        };

        // Assert
        Assert.True(invalidTopic.Partitions < 0, "Partitions should not be negative");
    }

    [Theory]
    [InlineData(1, 1)] // Development
    [InlineData(3, 2)] // Staging
    [InlineData(6, 3)] // Production
    [InlineData(12, 3)] // High-throughput
    public void TopicConfiguration_WithCommonScenarios_IsValid(int partitions, short replicationFactor)
    {
        // Arrange
        var topic = new TopicConfiguration
        {
            TopicName = "test-topic",
            Partitions = partitions,
            ReplicationFactor = replicationFactor,
            AutoCreateTopic = true
        };

        // Assert
        Assert.True(topic.Partitions > 0);
        Assert.True(topic.ReplicationFactor > 0);
        Assert.Equal(partitions, topic.Partitions);
        Assert.Equal(replicationFactor, topic.ReplicationFactor);
    }

    [Fact]
    public void DLQConfiguration_WithDefaultValues_IsCorrect()
    {
        // Arrange
        var dlqOptions = new DeadLetterOptions();

        // Assert
        Assert.True(dlqOptions.Enabled);
        Assert.Equal("dead-letter-queue", dlqOptions.TopicName);
        Assert.Equal(3, dlqOptions.MaxRetries);
    }

    [Fact]
    public void DLQConfiguration_CanBeCustomized()
    {
        // Arrange
        var dlqOptions = new DeadLetterOptions
        {
            Enabled = true,
            TopicName = "custom-dlq",
            MaxRetries = 5
        };

        // Assert
        Assert.True(dlqOptions.Enabled);
        Assert.Equal("custom-dlq", dlqOptions.TopicName);
        Assert.Equal(5, dlqOptions.MaxRetries);
    }

    [Fact]
    public void TopicMetadata_Structure_IsCorrect()
    {
        // Arrange
        var metadata = new TopicMetadata
        {
            TopicName = "test-topic",
            Partitions = 6,
            ReplicationFactor = 3,
            Config = new Dictionary<string, string>
            {
                { "retention.ms", "604800000" },
                { "compression.type", "snappy" }
            }
        };

        // Assert
        Assert.Equal("test-topic", metadata.TopicName);
        Assert.Equal(6, metadata.Partitions);
        Assert.Equal(3, metadata.ReplicationFactor);
        Assert.Equal(2, metadata.Config.Count);
        Assert.Equal("604800000", metadata.Config["retention.ms"]);
        Assert.Equal("snappy", metadata.Config["compression.type"]);
    }

    [Fact]
    public void SecurityConfiguration_SASL_CanBeConfigured()
    {
        // Arrange
        var securityOptions = new SecurityOptions
        {
            Sasl = new SaslOptions
            {
                Enabled = true,
                Mechanism = "SCRAM-SHA-256",
                Username = "kafka-user",
                Password = "kafka-password"
            }
        };

        // Assert
        Assert.True(securityOptions.Sasl.Enabled);
        Assert.Equal("SCRAM-SHA-256", securityOptions.Sasl.Mechanism);
        Assert.Equal("kafka-user", securityOptions.Sasl.Username);
        Assert.Equal("kafka-password", securityOptions.Sasl.Password);
    }

    [Fact]
    public void SecurityConfiguration_SSL_CanBeConfigured()
    {
        // Arrange
        var securityOptions = new SecurityOptions
        {
            EnableSsl = true,
            Ssl = new SslOptions
            {
                CaLocation = "/path/to/ca-cert",
                CertificateLocation = "/path/to/client-cert",
                KeyLocation = "/path/to/client-key",
                VerifyHostname = true
            }
        };

        // Assert
        Assert.True(securityOptions.EnableSsl);
        Assert.Equal("/path/to/ca-cert", securityOptions.Ssl.CaLocation);
        Assert.Equal("/path/to/client-cert", securityOptions.Ssl.CertificateLocation);
        Assert.Equal("/path/to/client-key", securityOptions.Ssl.KeyLocation);
        Assert.True(securityOptions.Ssl.VerifyHostname);
    }

    [Fact]
    public void TopicManager_CalculatesCorrectNumberOfTopics()
    {
        // Arrange
        var expectedCount = 0;

        // Count main topics with auto-create enabled
        foreach (var topic in _options.Topics.Where(t => t.AutoCreateTopic))
        {
            expectedCount++; // Main topic
            expectedCount++; // Retry topic
        }

        // Count DLQ
        if (_options.DeadLetter.Enabled)
        {
            expectedCount++;
        }

        // Assert - should create 5 topics total
        // test-topic, test-topic.retry, test-topic-2, test-topic-2.retry, test-dlq
        Assert.Equal(5, expectedCount);
    }

    [Fact]
    public void TopicManager_WithNoAutoCreateTopics_CreatesOnlyDLQ()
    {
        // Arrange
        _options.Topics[0].AutoCreateTopic = false;
        _options.Topics[1].AutoCreateTopic = false;

        var expectedCount = 0;

        foreach (var topic in _options.Topics.Where(t => t.AutoCreateTopic))
        {
            expectedCount += 2; // Main + retry
        }

        if (_options.DeadLetter.Enabled)
        {
            expectedCount++;
        }

        // Assert - only DLQ should be created
        Assert.Equal(1, expectedCount);
    }

    [Fact]
    public void TopicManager_WithDLQDisabled_DoesNotCreateDLQ()
    {
        // Arrange
        _options.DeadLetter.Enabled = false;

        var expectedCount = 0;

        foreach (var topic in _options.Topics.Where(t => t.AutoCreateTopic))
        {
            expectedCount += 2; // Main + retry
        }

        if (_options.DeadLetter.Enabled)
        {
            expectedCount++;
        }

        // Assert - 4 topics (2 main + 2 retry), no DLQ
        Assert.Equal(4, expectedCount);
    }

    [Theory]
    [InlineData("localhost:9092")]
    [InlineData("kafka-broker-1:9092,kafka-broker-2:9092")]
    [InlineData("kafka.internal.company.com:9092")]
    public void BootstrapServers_WithValidFormats_IsAccepted(string bootstrapServers)
    {
        // Arrange
        var options = new KafkaFlowOptions
        {
            BootstrapServers = bootstrapServers
        };

        // Assert
        Assert.Equal(bootstrapServers, options.BootstrapServers);
        Assert.False(string.IsNullOrEmpty(options.BootstrapServers));
    }

    [Fact]
    public void TopicConfiguration_WithAllProperties_IsComplete()
    {
        // Arrange
        var topic = new TopicConfiguration
        {
            TopicName = "orders",
            ConsumerGroup = "order-service",
            Partitions = 6,
            ReplicationFactor = 3,
            AutoCreateTopic = true,
            MessageType = typeof(TestMessage),
            HandlerType = typeof(TestMessageHandler)
        };

        // Assert
        Assert.Equal("orders", topic.TopicName);
        Assert.Equal("order-service", topic.ConsumerGroup);
        Assert.Equal(6, topic.Partitions);
        Assert.Equal(3, topic.ReplicationFactor);
        Assert.True(topic.AutoCreateTopic);
        Assert.Equal(typeof(TestMessage), topic.MessageType);
        Assert.Equal(typeof(TestMessageHandler), topic.HandlerType);
    }

    [Fact]
    public void RetryTopicConfiguration_InheritsSamePartitionsAndReplication()
    {
        // Arrange
        var mainTopic = _options.Topics[0];
        var retryTopicName = $"{mainTopic.TopicName}.retry";

        // In the actual implementation, retry topics should have same partitions and replication
        var expectedRetryPartitions = mainTopic.Partitions;
        var expectedRetryReplication = mainTopic.ReplicationFactor;

        // Assert
        Assert.Equal(3, expectedRetryPartitions);
        Assert.Equal(2, expectedRetryReplication);
        Assert.Equal("test-topic.retry", retryTopicName);
    }

    [Fact]
    public void DLQTopic_UsesDefaultPartitionsAndReplication()
    {
        // DLQ typically uses 1 partition and 1 replication factor
        // since it's for failed messages that don't need high throughput

        var expectedDLQPartitions = 1;
        var expectedDLQReplication = 1;

        // Assert
        Assert.Equal(1, expectedDLQPartitions);
        Assert.Equal(1, expectedDLQReplication);
    }

    // Test helper classes
    public class TestMessage : BaseMessage
    {
        public string Data { get; set; } = string.Empty;
    }

    public class TestMessageHandler : IMessageHandler<TestMessage>
    {
        public Task HandleAsync(TestMessage message, MessageContext context, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
    }
}
