using System.Text.Json;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace TMS.SharedKernal.SmoothRedis;

/// <summary>
/// Abstraction for JSON serialization to support multiple serializers
/// </summary>
public interface IJsonSerializer
{
    string Serialize<T>(T value);
    T? Deserialize<T>(string json);
}

/// <summary>
/// Newtonsoft.Json serializer implementation
/// </summary>
public class NewtonsoftJsonSerializer : IJsonSerializer
{
    private readonly JsonSerializerSettings _settings;

    public NewtonsoftJsonSerializer(JsonSerializerSettings? settings = null)
    {
        _settings = settings ?? new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            Formatting = Formatting.None
        };
    }

    public string Serialize<T>(T value)
    {
        return JsonConvert.SerializeObject(value, _settings);
    }

    public T? Deserialize<T>(string json)
    {
        return JsonConvert.DeserializeObject<T>(json, _settings);
    }
}

/// <summary>
/// System.Text.Json serializer implementation
/// </summary>
public class SystemTextJsonSerializer : IJsonSerializer
{
    private readonly JsonSerializerOptions _options;

    public SystemTextJsonSerializer(JsonSerializerOptions? options = null)
    {
        _options = options ?? new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }

    public string Serialize<T>(T value)
    {
        return System.Text.Json.JsonSerializer.Serialize(value, _options);
    }

    public T? Deserialize<T>(string json)
    {
        return System.Text.Json.JsonSerializer.Deserialize<T>(json, _options);
    }
}

/// <summary>
/// Serializer type options
/// </summary>
public enum SerializerType
{
    /// <summary>
    /// Use Newtonsoft.Json for serialization
    /// </summary>
    Newtonsoft,

    /// <summary>
    /// Use System.Text.Json for serialization
    /// </summary>
    SystemTextJson
}
