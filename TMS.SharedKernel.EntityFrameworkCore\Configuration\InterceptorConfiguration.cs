﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.SharedKernel.EntityFrameworkCore.Configuration;

public class InterceptorConfiguration
{
    public bool EnableEventPublishing { get; set; } = true;
    public bool CaptureOldValues { get; set; } = true;
    public bool CaptureNewValues { get; set; } = true;
    public bool CaptureChangedPropertiesOnly { get; set; } = true;

    /// <summary>
    /// Group all changes within a single SaveChanges call with a TransactionId
    /// </summary>
    public bool GroupByTransaction { get; set; } = true;

    /// <summary>
    /// Capture foreign key relationships to parent/master entities
    /// </summary>
    public bool CaptureRelationships { get; set; } = true;

    /// <summary>
    /// Wait for all audit logs in a transaction before publishing (atomic batch)
    /// If false, publishes each audit log immediately with the same TransactionId
    /// </summary>
    public bool PublishAsAtomic { get; set; } = false;

    /// <summary>
    /// Maximum number of changes to track in a single transaction
    /// If exceeded, a warning will be logged
    /// </summary>
    public int MaxChangesPerTransaction { get; set; } = 1000;

    public HashSet<Type> ExcludedEntityTypes { get; set; } = new();
    public HashSet<string> ExcludedTables { get; set; } = new()
        {
            "__EFMigrationsHistory"
        };
    public HashSet<string> ExcludedProperties { get; set; } = new()
        {
            "CreatedAt",
            "CreatedBy",
            "UpdatedAt",
            "UpdatedBy",
            "RowVersion"
        };

    /// <summary>
    /// Strategy to determine master-detail relationships for hierarchical subject structure
    /// </summary>
    public MasterDetailStrategy MasterDetailStrategy { get; set; } = MasterDetailStrategy.DeleteBehavior;

    /// <summary>
    /// Explicit master-detail relationship configuration
    /// Key: Detail entity type, Value: Master entity type
    /// Example: { typeof(OrderLine), typeof(Order) }
    /// </summary>
    public Dictionary<Type, Type> MasterDetailRelationships { get; set; } = new();

    /// <summary>
    /// The keyed service name for the IMessageProducer used in audit logging
    /// Default: "KafkaFlow"
    /// </summary>
    public string MessageProducerServiceKey { get; set; } = "KafkaFlow";
}

/// <summary>
/// Strategy for determining master-detail relationships
/// </summary>
public enum MasterDetailStrategy
{
    /// <summary>
    /// Use the first foreign key as master (original behavior)
    /// </summary>
    FirstForeignKey,

    /// <summary>
    /// Use EF Core's delete behavior (Cascade = master-detail)
    /// This is the recommended approach as it aligns with database semantics
    /// </summary>
    DeleteBehavior,

    /// <summary>
    /// Use explicit configuration from MasterDetailRelationships dictionary
    /// Most precise but requires manual configuration
    /// </summary>
    ExplicitConfiguration,

    /// <summary>
    /// Disable master-detail hierarchy in subject (all entities use simple subject)
    /// </summary>
    None
}
