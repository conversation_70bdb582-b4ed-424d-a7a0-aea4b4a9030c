﻿using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TMS.SharedKernel.Domain.Entities.Interfaces;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// A custom DbContext with configured audit fields, soft delete filter, and UTC DateTime conversion.
/// </summary>
public abstract class BaseDbContext : DbContext
{
    private const string DEFAULT_SCHEMA = "";
    private readonly ICurrentFactorProvider? _factorProvider;

    protected BaseDbContext(DbContextOptions options) : base(options)
    {
        ChangeTracker.StateChanged += OnEntityStateChanged;
        ChangeTracker.Tracked += OnEntityTracked;
    }

    protected BaseDbContext(DbContextOptions options, ICurrentFactorProvider factorProvider) : this(options)
    {
        _factorProvider = factorProvider;
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        ProcessTenantEntities();
        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        ProcessTenantEntities();
        return base.SaveChanges();
    }

    /// <summary>
    /// Processes tenant-related properties before saving:
    /// - Automatically populates CompanyId for new entities
    /// - Prevents modification of CompanyId to ensure tenant isolation integrity
    /// - Validates that CompanyId is not empty for tenant entities
    /// Note: DepartmentId is NOT automatically managed - handle it manually in specific entities if needed.
    /// </summary>
    private void ProcessTenantEntities()
    {
        foreach (var entry in ChangeTracker.Entries<ITenant>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    // Automatically populate CompanyId from current tenant context
                    if (_factorProvider != null)
                    {
                        if (entry.Entity.CompanyId == Guid.Empty && _factorProvider.CompanyId != Guid.Empty)
                        {
                            entry.Entity.CompanyId = _factorProvider.CompanyId;
                        }
                    }

                    // Validate that CompanyId is set (critical for tenant isolation)
                    if (entry.Entity.CompanyId == Guid.Empty)
                    {
                        throw new InvalidOperationException(
                            $"Cannot save entity of type '{entry.Entity.GetType().Name}' without a valid CompanyId. " +
                            "Ensure the current tenant context is properly set.");
                    }
                    break;

                case EntityState.Modified:
                    // CRITICAL SECURITY: Prevent modification of CompanyId after entity creation
                    // This ensures entities cannot be moved between tenants, which would violate tenant isolation
                    var companyIdProperty = entry.Property(nameof(ITenant.CompanyId));
                    if (companyIdProperty.IsModified)
                    {
                        var originalValue = companyIdProperty.OriginalValue;
                        var currentValue = companyIdProperty.CurrentValue;

                        if (!Equals(originalValue, currentValue))
                        {
                            throw new InvalidOperationException(
                                $"Cannot modify CompanyId for entity of type '{entry.Entity.GetType().Name}' (Id: {entry.Property("Id").CurrentValue}). " +
                                $"Attempted to change from '{originalValue}' to '{currentValue}'. " +
                                "Modifying CompanyId would violate tenant isolation and is strictly prohibited.");
                        }
                    }
                    break;
            }
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // modelBuilder.HasDefaultSchema(DEFAULT_SCHEMA);

        // Apply configurations from calling assembly (where DbContext is inherited)
        var callingAssembly = Assembly.GetCallingAssembly();
        modelBuilder.ApplyConfigurationsFromAssembly(callingAssembly);

        base.OnModelCreating(modelBuilder);

        // IMPORTANT: Apply global query filters LAST to prevent them from being overwritten
        // by entity configurations applied in derived DbContext classes
        ApplyGlobalQueryFilters(modelBuilder);
    }

    /// <summary>
    /// Applies global query filters for multi-tenant isolation and soft delete.
    /// Automatically filters queries to include only data belonging to the current tenant (CompanyId)
    /// and excludes soft-deleted records (IsDeleted = false).
    /// </summary>
    /// <param name="modelBuilder">The ModelBuilder instance</param>
    private void ApplyGlobalQueryFilters(ModelBuilder modelBuilder)
    {
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var isTenant = typeof(ITenant).IsAssignableFrom(entityType.ClrType);
            var isSoftDelete = typeof(ISoftDelete).IsAssignableFrom(entityType.ClrType);

            // Determine which filter method to call based on implemented interfaces
            if (isTenant && isSoftDelete)
            {
                // Entity implements both ITenant and ISoftDelete - combine filters
                var method = typeof(BaseDbContext)
                    .GetMethod(nameof(SetTenantAndSoftDeleteQueryFilter), BindingFlags.NonPublic | BindingFlags.Instance)
                    ?.MakeGenericMethod(entityType.ClrType);
                method?.Invoke(this, new object[] { modelBuilder });
            }
            else if (isTenant)
            {
                // Entity implements only ITenant
                var method = typeof(BaseDbContext)
                    .GetMethod(nameof(SetTenantQueryFilter), BindingFlags.NonPublic | BindingFlags.Instance)
                    ?.MakeGenericMethod(entityType.ClrType);
                method?.Invoke(this, new object[] { modelBuilder });
            }
            else if (isSoftDelete)
            {
                // Entity implements only ISoftDelete
                var method = typeof(BaseDbContext)
                    .GetMethod(nameof(SetSoftDeleteQueryFilter), BindingFlags.NonPublic | BindingFlags.Instance)
                    ?.MakeGenericMethod(entityType.ClrType);
                method?.Invoke(this, new object[] { modelBuilder });
            }
        }
    }

    /// <summary>
    /// Sets a query filter for tenant isolation on a specific entity type.
    /// Only includes records where CompanyId matches the current tenant's CompanyId.
    /// If no tenant context is available (factorProvider is null or CompanyId is empty), returns no records.
    /// IMPORTANT: This captures 'this' (BaseDbContext) so _factorProvider.CompanyId is evaluated at QUERY TIME, not model build time.
    /// </summary>
    private void SetTenantQueryFilter<TEntity>(ModelBuilder modelBuilder) where TEntity : class, ITenant
    {
        modelBuilder.Entity<TEntity>().HasQueryFilter(e =>
            _factorProvider != null &&
            _factorProvider.CompanyId != Guid.Empty &&
            e.CompanyId == _factorProvider.CompanyId
        );
    }

    /// <summary>
    /// Sets a query filter for soft delete on a specific entity type.
    /// Only includes records where IsDeleted = false.
    /// </summary>
    private void SetSoftDeleteQueryFilter<TEntity>(ModelBuilder modelBuilder) where TEntity : class, ISoftDelete
    {
        modelBuilder.Entity<TEntity>().HasQueryFilter(e => !e.IsDeleted);
    }

    /// <summary>
    /// Sets combined query filters for both tenant isolation and soft delete.
    /// Only includes records where CompanyId matches AND IsDeleted = false.
    /// </summary>
    private void SetTenantAndSoftDeleteQueryFilter<TEntity>(ModelBuilder modelBuilder)
        where TEntity : class, ITenant, ISoftDelete
    {
        modelBuilder.Entity<TEntity>().HasQueryFilter(e =>
            _factorProvider != null &&
            _factorProvider.CompanyId != Guid.Empty &&
            e.CompanyId == _factorProvider.CompanyId &&
            !e.IsDeleted
        );
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        ArgumentNullException.ThrowIfNull(configurationBuilder);
        
        // Configure DateTime properties to always use UTC
        configurationBuilder.Properties<DateTime>()
            .HaveConversion<DateTimeToUtcConverter>();
        configurationBuilder.Properties<DateTime?>()
            .HaveConversion<NullableDateTimeToUtcConverter>();
            
        base.ConfigureConventions(configurationBuilder);
    }

    private void OnEntityStateChanged(object? sender, EntityEntryEventArgs e) => ProcessAuditableEntity(e);
    private void OnEntityTracked(object? sender, EntityEntryEventArgs e) => ProcessAuditableEntity(e);

    private void ProcessAuditableEntity(EntityEntryEventArgs e)
    {
        switch (e.Entry.State)
        {
            case EntityState.Added:
                SetCreationAuditProperties(e.Entry);
                SetModificationAuditProperties(e.Entry);
                break;
            case EntityState.Modified:
                SetModificationAuditProperties(e.Entry);
                break;
            case EntityState.Deleted:
                SoftDeleteHandle(e);
                break;

        }
    }

    private void SoftDeleteHandle(EntityEntryEventArgs e)
    {
        if (e.Entry.Entity is not ISoftDelete entity)
            return;

        e.Entry.State = EntityState.Modified;

        // We just allow version and delete field can be updated
        foreach (var member in e.Entry.Members)
        {
            if (nameof(IConcurrencyControlled.Version) == member.Metadata.Name)
                continue;
            member.IsModified = false;
        }

        entity.IsDeleted = true;
        e.Entry.Member(nameof(entity.IsDeleted)).IsModified = true;
    }

    private void SetCreationAuditProperties(EntityEntry entry)
    {
        var now = DateTime.UtcNow;
        
        if (entry.Entity is ICreatedDateTracking createdDateEntity)
        {
            createdDateEntity.CreatedAt = now;
        }

        if (_factorProvider?.CurrentFactorId != default && 
            entry.Entity is ICreatedFactorTracking<Guid> createdFactorEntity)
        {
            createdFactorEntity.CreatedBy = _factorProvider.CurrentFactorId;
        }
    }

    private void SetModificationAuditProperties(EntityEntry entry)
    {
        var now = DateTime.UtcNow;
        
        if (entry.Entity is IUpdatedDateTracking updatedDateEntity)
        {
            updatedDateEntity.UpdatedAt = now;
            entry.Property(nameof(updatedDateEntity.UpdatedAt)).IsModified = true;
        }

        if (_factorProvider?.CurrentFactorId != default && 
            entry.Entity is IUpdatedFactorTracking<Guid> updatedFactorEntity)
        {
            updatedFactorEntity.UpdatedBy = _factorProvider.CurrentFactorId;
            entry.Property(nameof(updatedFactorEntity.UpdatedBy)).IsModified = true;
        }
    }

    private sealed class DateTimeToUtcConverter : ValueConverter<DateTime, DateTime>
    {
        public DateTimeToUtcConverter() : base(
            v => v.Kind == DateTimeKind.Utc ? v : v.ToUniversalTime(),
            v => DateTime.SpecifyKind(v, DateTimeKind.Utc))
        {
        }
    }

    private sealed class NullableDateTimeToUtcConverter : ValueConverter<DateTime?, DateTime?>
    {
        public NullableDateTimeToUtcConverter() : base(
            v => v.HasValue ? (v.Value.Kind == DateTimeKind.Utc ? v.Value : v.Value.ToUniversalTime()) : v,
            v => v.HasValue ? DateTime.SpecifyKind(v.Value, DateTimeKind.Utc) : v)
        {
        }
    }
}
