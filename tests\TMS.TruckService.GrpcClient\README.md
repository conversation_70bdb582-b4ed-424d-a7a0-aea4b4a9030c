# TMS.TruckService.GrpcClient

A .NET client library for calling the OrderService gRPC API with full dependency injection support.

## Features

- Clean wrapper around generated gRPC client
- Full dependency injection (DI) support
- Structured logging with Microsoft.Extensions.Logging
- Easy service registration with extension methods
- Interface-based design for testability
- Comprehensive test scenarios
- Interactive console test application

## Installation

### In your project

Add a project reference:

```bash
dotnet add reference path/to/TMS.TruckService.GrpcClient.csproj
```

Or add as a NuGet package (if published):

```bash
dotnet add package TMS.TruckService.GrpcClient
```

## Usage

### Option 1: Dependency Injection (Recommended)

This is the recommended approach for ASP.NET Core applications, console apps with hosting, and any application using Microsoft.Extensions.DependencyInjection.

#### In ASP.NET Core (Web API, MVC, etc.)

**Program.cs:**

```csharp
using TMS.TruckService.GrpcClient.Extensions;

var builder = WebApplication.CreateBuilder(args);

// Register the gRPC client with DI
builder.Services.AddOrderServiceClient(options =>
{
    options.ServerAddress = builder.Configuration["GrpcClients:OrderService:Address"]
        ?? "http://localhost:5000";
});

// Or use a simple string address
builder.Services.AddOrderServiceClient("https://orderservice.example.com");

var app = builder.Build();
app.Run();
```

**appsettings.json:**

```json
{
  "GrpcClients": {
    "OrderService": {
      "Address": "https://orderservice.example.com"
    }
  }
}
```

**Controller:**

```csharp
using Microsoft.AspNetCore.Mvc;
using TMS.TruckService.GrpcClient;

[ApiController]
[Route("api/[controller]")]
public class OrdersController : ControllerBase
{
    private readonly IOrderServiceClient _orderClient;
    private readonly ILogger<OrdersController> _logger;

    // Inject IOrderServiceClient
    public OrdersController(
        IOrderServiceClient orderClient,
        ILogger<OrdersController> logger)
    {
        _orderClient = orderClient;
        _logger = logger;
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetOrder(int id)
    {
        try
        {
            var reply = await _orderClient.GetOrderAsync(id);

            if (!reply.Success)
            {
                return NotFound(reply.ErrorMessage);
            }

            return Ok(reply.Order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order {OrderId}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto dto)
    {
        var reply = await _orderClient.CreateOrderAsync(
            dto.CustomerName,
            dto.Amount,
            dto.Description);

        if (!reply.Success)
        {
            return BadRequest(reply.ErrorMessage);
        }

        return CreatedAtAction(nameof(GetOrder),
            new { id = reply.Order.OrderId },
            reply.Order);
    }
}

public record CreateOrderDto(string CustomerName, double Amount, string? Description);
```

#### In Console Applications with Hosting

**Program.cs:**

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TMS.TruckService.GrpcClient;
using TMS.TruckService.GrpcClient.Extensions;

var host = Host.CreateDefaultBuilder(args)
    .ConfigureServices((context, services) =>
    {
        // Register gRPC client
        services.AddOrderServiceClient("http://localhost:5000");

        // Register your application services
        services.AddHostedService<MyBackgroundService>();
    })
    .Build();

await host.RunAsync();

// Background service example
public class MyBackgroundService : BackgroundService
{
    private readonly IOrderServiceClient _orderClient;
    private readonly ILogger<MyBackgroundService> _logger;

    public MyBackgroundService(
        IOrderServiceClient orderClient,
        ILogger<MyBackgroundService> logger)
    {
        _orderClient = orderClient;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var reply = await _orderClient.ListOrdersAsync(1, 10);
            _logger.LogInformation("Retrieved {Count} orders", reply.Orders.Count);

            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}
```

#### In Worker Services

**Program.cs:**

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TMS.TruckService.GrpcClient.Extensions;

var builder = Host.CreateApplicationBuilder(args);

// Register gRPC client
builder.Services.AddOrderServiceClient(options =>
{
    options.ServerAddress = builder.Configuration["OrderServiceAddress"]
        ?? "http://localhost:5000";
});

builder.Services.AddHostedService<Worker>();

var host = builder.Build();
host.Run();
```

**Worker.cs:**

```csharp
public class Worker : BackgroundService
{
    private readonly IOrderServiceClient _orderClient;
    private readonly ILogger<Worker> _logger;

    public Worker(IOrderServiceClient orderClient, ILogger<Worker> logger)
    {
        _orderClient = orderClient;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Use _orderClient here
    }
}
```

### Option 2: Manual Instantiation (Not Recommended)

For simple scenarios without DI (e.g., quick scripts), you can create instances manually:

```csharp
using TMS.TruckService.GrpcClient;
using Microsoft.Extensions.Logging;

// You'll need to create logger manually
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<OrderServiceClient>();

// Create gRPC channel and client
var channel = GrpcChannel.ForAddress("http://localhost:5000");
var grpcClient = new OrderService.OrderServiceClient(channel);

// Create wrapper
var orderClient = new OrderServiceClient(grpcClient, logger);

// Use the client
var reply = await orderClient.GetOrderAsync(1);
Console.WriteLine($"Order: {reply.Order.OrderNumber}");

// Cleanup
channel.Dispose();
```

**Note:** This approach loses many benefits of DI including:
- Automatic lifecycle management
- Easy mocking for tests
- Configuration integration
- Proper disposal

## API Reference

### IOrderServiceClient Interface

```csharp
public interface IOrderServiceClient
{
    Task<GetOrderReply> GetOrderAsync(int orderId, CallOptions? options = null);

    Task<CreateOrderReply> CreateOrderAsync(
        string customerName,
        double amount,
        string? description = null,
        CallOptions? options = null);

    Task<ListOrdersReply> ListOrdersAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? status = null,
        CallOptions? options = null);

    Task<UpdateOrderStatusReply> UpdateOrderStatusAsync(
        int orderId,
        string newStatus,
        string? notes = null,
        CallOptions? options = null);

    Task<DeleteOrderReply> DeleteOrderAsync(int orderId, CallOptions? options = null);

    Task<bool> TestConnectionAsync();
}
```

### Extension Methods

```csharp
// Simple registration with server address
services.AddOrderServiceClient("https://orderservice.example.com");

// Registration with options
services.AddOrderServiceClient(options =>
{
    options.ServerAddress = "https://orderservice.example.com";
    options.ConfigureChannel = channelOptions =>
    {
        channelOptions.MaxReceiveMessageSize = 32 * 1024 * 1024; // 32 MB
        channelOptions.MaxSendMessageSize = 32 * 1024 * 1024;
    };
});
```

## Configuration

### Channel Options

You can configure the gRPC channel:

```csharp
services.AddOrderServiceClient(options =>
{
    options.ServerAddress = "https://orderservice.example.com";
    options.ConfigureChannel = channelOptions =>
    {
        // Message size limits
        channelOptions.MaxReceiveMessageSize = 32 * 1024 * 1024; // 32 MB
        channelOptions.MaxSendMessageSize = 32 * 1024 * 1024;

        // HTTP client configuration
        channelOptions.HttpHandler = new SocketsHttpHandler
        {
            PooledConnectionIdleTimeout = TimeSpan.FromMinutes(5),
            KeepAlivePingDelay = TimeSpan.FromSeconds(60),
            KeepAlivePingTimeout = TimeSpan.FromSeconds(30),
            EnableMultipleHttp2Connections = true
        };

        // Credentials (if using HTTPS with client certificates)
        // channelOptions.Credentials = ...
    };
});
```

## Testing

The client is designed to be easily testable. Use the `IOrderServiceClient` interface to create mocks:

### With Moq

```csharp
using Moq;
using TMS.TruckService.GrpcClient;
using TMS.TruckService.Api.Grpc;

[Fact]
public async Task GetOrder_ReturnsOrder()
{
    // Arrange
    var mockClient = new Mock<IOrderServiceClient>();
    mockClient
        .Setup(x => x.GetOrderAsync(1, null))
        .ReturnsAsync(new GetOrderReply
        {
            Success = true,
            Order = new OrderDto { OrderId = 1, OrderNumber = "ORD-001" }
        });

    var controller = new OrdersController(mockClient.Object, logger);

    // Act
    var result = await controller.GetOrder(1);

    // Assert
    var okResult = Assert.IsType<OkObjectResult>(result);
    var order = Assert.IsType<OrderDto>(okResult.Value);
    Assert.Equal("ORD-001", order.OrderNumber);
}
```

### With NSubstitute

```csharp
using NSubstitute;
using TMS.TruckService.GrpcClient;

[Fact]
public async Task CreateOrder_CallsClient()
{
    // Arrange
    var client = Substitute.For<IOrderServiceClient>();
    client.CreateOrderAsync("John Doe", 100.0, null, null)
        .Returns(new CreateOrderReply { Success = true });

    var controller = new OrdersController(client, logger);

    // Act
    await controller.CreateOrder(new CreateOrderDto("John Doe", 100.0, null));

    // Assert
    await client.Received(1).CreateOrderAsync("John Doe", 100.0, Arg.Any<string>(), null);
}
```

## Example Console App

The project includes a fully functional console test application at `Program.cs`. Run it with:

```bash
cd tests/TMS.TruckService.GrpcClient
dotnet run

# Or specify server address
dotnet run -- https://orderservice.example.com
```

Features:
- Interactive menu
- 8 comprehensive test scenarios
- Custom test mode
- Connection testing
- Error handling demonstrations

## Architecture

```
┌─────────────────────────────────────┐
│   Your Application (Controller,    │
│   Service, Background Worker, etc) │
└────────────────┬────────────────────┘
                 │
                 │ Inject IOrderServiceClient
                 ▼
┌─────────────────────────────────────┐
│     OrderServiceClient              │
│  (Implements IOrderServiceClient)   │
│  - Logging                          │
│  - Error handling                   │
│  - Clean API                        │
└────────────────┬────────────────────┘
                 │
                 │ Uses
                 ▼
┌─────────────────────────────────────┐
│  OrderService.OrderServiceClient    │
│  (Proto-generated gRPC client)      │
└────────────────┬────────────────────┘
                 │
                 │ gRPC / HTTP/2
                 ▼
┌─────────────────────────────────────┐
│      OrderService gRPC Server       │
└─────────────────────────────────────┘
```

## Best Practices

1. **Always use DI**: Register the client in DI for proper lifecycle management, testability, and configuration

2. **Use the interface**: Depend on `IOrderServiceClient` not the concrete class for easy mocking

3. **Configure appropriately**: Set message size limits based on your needs

4. **Handle errors**: Always catch `RpcException` and handle gRPC status codes

5. **Use structured logging**: The client logs to `ILogger`, configure appropriately for your environment

6. **Connection pooling**: The DI approach automatically manages channel pooling

7. **Timeouts**: Use `CallOptions` to set deadlines for critical operations:
   ```csharp
   var deadline = DateTime.UtcNow.AddSeconds(5);
   var options = new CallOptions(deadline: deadline);
   var reply = await client.GetOrderAsync(1, options);
   ```

## Troubleshooting

### "AddGrpcClient not found"

Ensure you have the `Grpc.Net.ClientFactory` package:

```bash
dotnet add package Grpc.Net.ClientFactory
```

### Connection refused

- Verify the server is running
- Check the server address/port
- For HTTP/2, ensure the server supports it
- For HTTPS, check certificate validity

### Message size exceeded

Increase the message size limits:

```csharp
options.ConfigureChannel = channelOptions =>
{
    channelOptions.MaxReceiveMessageSize = 32 * 1024 * 1024;
};
```

## See Also

- [gRPC Server Implementation](../TMS.TruckService.Api/README.md)
- [TMS.SharedKernel.BaseGrpc](../../TMS.SharedKernel.BaseGrpc/README.md)
- [Proto File](Protos/order.proto)

## License

Internal use only - TMS System
