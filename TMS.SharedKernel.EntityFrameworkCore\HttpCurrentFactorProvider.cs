﻿using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using TMS.SharedKernel.Domain.Constants;
using TMS.SharedKernel.Domain.Enums;
using TMS.SharedKernel.Domain.Extentions;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernal.SmoothRedis;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// HttpCurrentFactorProvider
/// </summary>
public class HttpCurrentFactorProvider : ICurrentFactorProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ISmoothRedis? _redis;

    // Backing fields for lazy initialization
    private Guid? _currentFactorId;
    private string? _currentFactorName;
    private Guid? _companyId;
    private Guid? _departmentId;
    private string? _roles;
    private ClientType? _clientType;
    private string? _departmentCode;
    private bool _departmentCodeInitialized;

    /// <summary>
    /// HttpCurrentFactorProvider
    /// </summary>
    /// <param name="httpContextAccessor"></param>
    /// <param name="redis"></param>
    public HttpCurrentFactorProvider(IHttpContextAccessor httpContextAccessor, ISmoothRedis? redis)
    {
        _httpContextAccessor = httpContextAccessor;
        _redis = redis;
    }

    /// <summary>
    /// Gets the ClaimsPrincipal for the current HTTP context.
    /// </summary>
    public ClaimsPrincipal? CurrentUser => _httpContextAccessor.HttpContext?.User;

    /// <inheritdoc/>
    public Guid CurrentFactorId => _currentFactorId ??= ExtractFromHeaderToGuid(DomainConstants.UserId);

    /// <inheritdoc/>
    public string CurrentFactorName => _currentFactorName ??= HeaderEncodingHelper.DecodeBase64(ExtractFromHeaderToString(DomainConstants.UserName));

    /// <inheritdoc/>
    public Guid CompanyId => _companyId ??= ExtractFromHeaderToGuid(DomainConstants.CompanyId);

    /// <inheritdoc/>
    public Guid DepartmentId => _departmentId ??= ExtractFromHeaderToGuid(DomainConstants.DepartmentId);

    /// <inheritdoc/>
    public string Roles => _roles ??= ExtractFromHeaderToString(DomainConstants.Roles);

    private Guid ExtractFromHeaderToGuid(string headerName)
    {
        var idFromHeader = ExtractFromHeaderToString(headerName);
        if (!string.IsNullOrEmpty(idFromHeader) && Guid.TryParse(idFromHeader, out var parsedId))
        {
            return parsedId;
        }

        return Guid.Empty;
    }

    private string ExtractFromHeaderToString(string headerName)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null && httpContext.Request.Headers.TryGetValue(headerName, out var headerValue))
        {
            return headerValue.FirstOrDefault() ?? "";
        }

        return string.Empty;
    }

    /// <inheritdoc/>
    public ClientType ClientType
    {
        get
        {
            if (_clientType.HasValue)
                return _clientType.Value;

            var clientType = _httpContextAccessor.HttpContext?.Request.Headers["x-client-type"].FirstOrDefault() ?? "";
            _clientType = clientType.ToLowerInvariant() switch
            {
                "web" => ClientType.Web,
                "mobile" => ClientType.Mobile,
                "ios" => ClientType.Mobile,
                "android" => ClientType.Mobile,
                "desktop" => ClientType.Desktop,
                _ => ClientType.Unknown
            };
            return _clientType.Value;
        }
    }

    /// <inheritdoc/>
    public string DepartmentCode
    {
        get
        {
            if (_departmentCodeInitialized)
                return _departmentCode ?? String.Empty;

            if (_redis is null)
            {
                _departmentCode = String.Empty;
                _departmentCodeInitialized = true;
                return String.Empty;
            }

            // Skip if CompanyId or DepartmentId is empty
            if (CompanyId == Guid.Empty || DepartmentId == Guid.Empty)
            {
                _departmentCode = String.Empty;
                _departmentCodeInitialized = true;
                return String.Empty;
            }

            var cacheKey = $"metadata:{CompanyId}:{DepartmentId}";
            _departmentCode = _redis.Cache.GetAsync(cacheKey).ConfigureAwait(false).GetAwaiter().GetResult() ?? String.Empty;
            _departmentCodeInitialized = true;
            return _departmentCode;
        }
    }
}
