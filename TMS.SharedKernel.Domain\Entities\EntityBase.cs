﻿using System.Linq;
using System.Security.Cryptography;
using TMS.SharedKernel.Domain.Entities.Interfaces;
using TMS.SharedKernel.Domain.Events;

namespace TMS.SharedKernel.Domain.Entities;

public static class EntityIdGenerator<TKey>
{
    public static readonly Func<TKey> NewId = CreateGenerator();

    private static Func<TKey> CreateGenerator()
    {
        if (typeof(TKey) == typeof(Guid))
            return () => (TKey)(object)SequentialGuidGenerator.NewPostgreSqlGuid();

        return () => default!;
    }
}

/// <summary>
/// A base class for DDD Entities. Includes support for domain events dispatched post-persistence.
/// If you prefer GUID Ids, change it here.
/// If you need to support both GUID and int IDs, change to EntityBase&lt;TId&gt; and use TId as the type for Id.
/// </summary>
public abstract class EntityBase : HasDomainEventsBase, IEntity<Guid>
{
    /// <inheritdoc />
    public Guid Id { get; set; } = SequentialGuidGenerator.NewPostgreSqlGuid();
}

/// <inheritdoc cref="TMS.SharedKernel.Domain.Entities.Interfaces.IEntity{TKey}" />
public abstract class EntityBase<TKey> : HasDomainEventsBase, IEntity<TKey>
    where TKey : notnull, IComparable, IComparable<TKey>, IEquatable<TKey>
{
    /// <inheritdoc />
    public TKey Id { get; set; } = EntityIdGenerator<TKey>.NewId();
}

/// <summary>
/// 
/// </summary>
public enum SequentialGuidType
{
    SequentialAsString,
    SequentialAsBinary,
    SequentialAtEnd
}

public static class SequentialGuidGenerator
{
    private static readonly RandomNumberGenerator _rng = RandomNumberGenerator.Create();
    private static readonly object _lock = new object();

    /// <summary>
    /// Generates a sequential GUID based on the current timestamp
    /// </summary>
    /// <param name="guidType">The type of sequential GUID to generate</param>
    /// <returns>A sequential GUID</returns>
    public static Guid NewSequentialGuid(SequentialGuidType guidType = SequentialGuidType.SequentialAtEnd)
    {
        byte[] randomBytes = new byte[10];
        lock (_lock)
        {
            _rng.GetBytes(randomBytes);
        }

        long timestamp = DateTime.UtcNow.Ticks / 10000L; // Convert to milliseconds
        byte[] timestampBytes = BitConverter.GetBytes(timestamp);

        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(timestampBytes);
        }

        byte[] guidBytes = new byte[16];

        switch (guidType)
        {
            case SequentialGuidType.SequentialAsString:
            case SequentialGuidType.SequentialAsBinary:
                // Timestamp in first 6 bytes, random in last 10
                Buffer.BlockCopy(timestampBytes, 2, guidBytes, 0, 6);
                Buffer.BlockCopy(randomBytes, 0, guidBytes, 6, 10);

                if (guidType == SequentialGuidType.SequentialAsString)
                {
                    // Rearrange for string sorting (SQL Server UNIQUEIDENTIFIER as string)
                    // Swap bytes to match SQL Server's string representation ordering
                    SwapBytes(guidBytes, 0, 3);
                    SwapBytes(guidBytes, 1, 2);
                    SwapBytes(guidBytes, 4, 5);
                }
                break;

            case SequentialGuidType.SequentialAtEnd:
                // Random in first 10 bytes, timestamp in last 6
                Buffer.BlockCopy(randomBytes, 0, guidBytes, 0, 10);
                Buffer.BlockCopy(timestampBytes, 2, guidBytes, 10, 6);
                break;
        }

        return new Guid(guidBytes);
    }

    /// <summary>
    /// Simple method that generates a sequential GUID optimized for SQL Server
    /// </summary>
    /// <returns>A sequential GUID</returns>
    public static Guid NewGuid()
    {
        return NewSequentialGuid(SequentialGuidType.SequentialAtEnd);
    }

    /// <summary>
    /// Generates a sequential GUID optimized for PostgreSQL
    /// PostgreSQL sorts UUIDs lexicographically, so timestamp should be at the beginning
    /// </summary>
    /// <returns>A sequential GUID optimized for PostgreSQL</returns>
    public static Guid NewPostgreSqlGuid()
    {
        return NewSequentialGuid(SequentialGuidType.SequentialAsString);
    }

    private static void SwapBytes(byte[] guid, int left, int right)
    {
        byte temp = guid[left];
        guid[left] = guid[right];
        guid[right] = temp;
    }

    /// <summary>
    /// Creates a deterministic GUID from code, source, and type ID
    /// PostgreSQL-sortable format: SSSSTTTT-CCCCCCCCCCCCCCCCCCCCCCCC
    /// S = Source enum (4 hex), T = Type ID (4 hex), C = Code as hex string (24 hex = 12 ASCII chars)
    /// Codes sort alphabetically within the same source and type
    /// </summary>
    /// <param name="code">The post office code (e.g., "VLG", "ABH", "Q08")</param>
    /// <param name="source">The source enum value</param>
    /// <param name="typeId">The type ID</param>
    /// <returns>A deterministic, sortable GUID for PostgreSQL</returns>
    public static Guid CreateDeterministicGuid(string code, int source, int typeId)
    {
        if (string.IsNullOrWhiteSpace(code))
            code = "";

        // Normalize code: uppercase, remove non-alphanumeric
        code = new string(code.ToUpperInvariant()
            .Where(c => char.IsLetterOrDigit(c))
            .ToArray());

        // Convert code to hex string (each char = 2 hex digits)
        // Take up to 12 characters (24 hex digits) to fit in GUID
        string codeHex = "";
        for (int i = 0; i < Math.Min(12, code.Length); i++)
        {
            codeHex += ((int)code[i]).ToString("x2");
        }

        // Pad with zeros to 24 hex digits
        codeHex = codeHex.PadRight(24, '0');

        // Build GUID string: SSSSTTTT-CCCC-CCCC-CCCC-CCCCCCCCCCCC
        // This ensures PostgreSQL sorts by: Source -> Type -> Code (alphabetically)
        string guidString = string.Format(
            "{0:x4}{1:x4}-{2}-{3}-{4}-{5}",
            source & 0xFFFF,           // Source (4 hex)
            typeId & 0xFFFF,           // TypeId (4 hex)
            codeHex.Substring(0, 4),   // Code chars 0-1 (4 hex)
            codeHex.Substring(4, 4),   // Code chars 2-3 (4 hex)
            codeHex.Substring(8, 4),   // Code chars 4-5 (4 hex)
            codeHex.Substring(12, 12)  // Code chars 6-11 (12 hex)
        );

        return Guid.Parse(guidString);
    }
}

