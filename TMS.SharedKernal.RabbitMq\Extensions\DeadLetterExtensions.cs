﻿using Microsoft.Extensions.DependencyInjection;
using TMS.SharedKernal.RabbitMq.Configuration;

namespace TMS.SharedKernal.RabbitMq.Extensions; 

public static class DeadLetterExtensions
{
    public static IServiceCollection AddDeadLetterQueue(
        this IServiceCollection services,
        string queueName,
        string deadLetterQueueName)
    {
        services.PostConfigure<RabbitMqOptions>(options =>
        {
            if (options.Exchange.Queues.TryGetValue(queueName, out var queueConfig))
            {
                queueConfig.Arguments["x-dead-letter-exchange"] = options.Exchange.Name;
                queueConfig.Arguments["x-dead-letter-routing-key"] = $"dead.{queueConfig.RoutingKey}";
                queueConfig.Arguments["x-message-ttl"] = 300000; // 5 minutes
            }

            // Add dead letter queue
            options.Exchange.Queues[$"{queueName}_dlq"] = new QueueConfiguration
            {
                QueueName = deadLetterQueueName,
                RoutingKey = $"dead.{queueConfig?.RoutingKey}",
                Durable = true
            };
        });

        return services;
    }
}
