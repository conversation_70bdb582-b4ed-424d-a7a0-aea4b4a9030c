# TMS.SharedKernal.Caching

Generic metadata caching service for storing and retrieving lists of entities in Redis with type-safe convenience methods.

## Features

- ✅ **Generic caching** - Cache any entity type with `SetAsync<T>()` / `GetAsync<T>()`
- ✅ **Convenience methods** - Semantic methods for common entity types (ServiceType, ExtraService, OrderStatus, PriorityPlan, DailyExecutionPlan)
- ✅ **Type inference** - No need to specify generic type parameters for Set methods
- ✅ **Prefix support** - Optional prefixes (e.g., companyId) for multi-tenant scenarios
- ✅ **Configuration-based** - Register entity types and Redis key patterns via fluent API

## Installation

```bash
dotnet add package TMS.SharedKernal.Caching
```

## Quick Start

### 1. Configure in Startup/DependencyInjection

```csharp
using TMS.SharedKernal.Caching;

services.AddMetadataCaching(config =>
{
    // Simple entity types (no prefix required)
    config.RegisterEntityType<ServiceType>("metadata:servicetypes")
          .RegisterEntityType<ExtraService>("metadata:extraservices")
          .RegisterEntityType<OrderStatus>("metadata:orderstatuses");

    // Multi-tenant entity types (prefix required)
    config.RegisterEntityType<PriorityPlan>("metadata:{prefix}:priority-plans", requiresPrefix: true)
          .RegisterEntityType<DailyExecutionPlan>("metadata:{prefix}:daily-execution-plans", requiresPrefix: true);
});
```

### 2. Inject and Use

```csharp
public class MyService
{
    private readonly IMetadataCacheService _cache;

    public MyService(IMetadataCacheService cache)
    {
        _cache = cache;
    }

    public async Task CacheServiceTypes()
    {
        var serviceTypes = GetServiceTypesFromDatabase();

        // Type is inferred from the parameter - no need for <ServiceType>!
        await _cache.SetServiceTypeAsync(serviceTypes);
    }

    public async Task<List<ServiceType>> GetServiceTypes()
    {
        // Need to specify type for Get methods
        return await _cache.GetServiceTypeAsync<ServiceType>();
    }

    public async Task CachePriorityPlans(string companyId)
    {
        var plans = GetPriorityPlansFromDatabase(companyId);

        // Prefix-based caching for multi-tenant scenarios
        await _cache.SetPriorityPlanAsync(plans, companyId);
    }
}
```

## API Reference

### Generic Methods

```csharp
// Set any entity type
Task SetAsync<T>(List<T> items, string prefix = "") where T : class;

// Get any entity type
Task<List<T>> GetAsync<T>(string prefix = "") where T : class;
```

### Convenience Methods

#### ServiceType
```csharp
await _cache.SetServiceTypeAsync(serviceTypes);              // Type inferred!
var types = await _cache.GetServiceTypeAsync<ServiceType>();
```

#### ExtraService
```csharp
await _cache.SetExtraServiceAsync(extraServices);            // Type inferred!
var services = await _cache.GetExtraServiceAsync<ExtraService>();
```

#### OrderStatus
```csharp
await _cache.SetOrderStatusAsync(statuses);                  // Type inferred!
var statuses = await _cache.GetOrderStatusAsync<OrderStatus>();
```

#### PriorityPlan (with company prefix)
```csharp
await _cache.SetPriorityPlanAsync(plans, companyId);         // Type inferred!
var plans = await _cache.GetPriorityPlanAsync<PriorityPlan>(companyId);
```

#### DailyExecutionPlan (with company prefix)
```csharp
await _cache.SetDailyExecutionPlanAsync(plans, companyId);   // Type inferred!
var plans = await _cache.GetDailyExecutionPlanAsync<DailyExecutionPlan>(companyId);
```

## Configuration Options

### Redis Key Templates

When registering entity types, you can use placeholders in the Redis key template:

```csharp
config.RegisterEntityType<MyEntity>(
    redisKeyTemplate: "metadata:{prefix}:my-entities",
    requiresPrefix: true
);
```

- `{prefix}` - Will be replaced with the provided prefix value (e.g., companyId)
- `requiresPrefix` - If true, calling without prefix will throw `ArgumentNullException`

## Benefits

✅ **No duplication** - Define entity type mappings once, use everywhere
✅ **Type-safe** - Compile-time checking of entity types
✅ **Clean syntax** - Type inference removes boilerplate
✅ **Flexible** - Generic methods for custom scenarios
✅ **Easy to extend** - Add new convenience methods as needed

## Advanced Usage

### Adding Custom Entity Types

You can cache any entity type using the generic methods:

```csharp
// Configure
config.RegisterEntityType<MyCustomEntity>("metadata:custom-entities");

// Use
await _cache.SetAsync(customEntities);
var entities = await _cache.GetAsync<MyCustomEntity>();
```

### Multi-Tenant Scenarios

For multi-tenant applications, use prefixes to isolate data per tenant:

```csharp
// Configure with prefix placeholder
config.RegisterEntityType<TenantData>("tenant:{prefix}:data", requiresPrefix: true);

// Use with tenant ID
await _cache.SetAsync(data, tenantId);
var data = await _cache.GetAsync<TenantData>(tenantId);
```

## Version History

### v1.0.0 (Initial Release)
- Generic caching with configuration-based entity type registration
- Convenience methods for common entity types
- Type inference for Set methods
- Support for prefix-based multi-tenant scenarios

## Dependencies

- TMS.SharedKernal.SmoothRedis ^1.0.3
- .NET 9.0

## License

Internal use only - TMS Project
