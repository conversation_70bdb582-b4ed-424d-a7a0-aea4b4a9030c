using MapsterMapper;
using MediatR;
using TMS.SharedKernel.Domain;
using TMS.TruckService.Application.Features.Todos.Commands.CreateTodo;
using TMS.TruckService.Contracts.Todos;
using TMS.TruckService.Domain.Entities;

namespace TMS.TruckService.Application.Features.Todos.Queries.GetTodos;

public class GetTodosQueryHandler : IRequestHandler<GetTodosQuery, IEnumerable<TodoResponse>>
{
    private readonly IBaseRepository<Todo> _todoRepository;
    private readonly IMapper _mapper;

    public GetTodosQueryHandler(IBaseRepository<Todo> todoRepository, IMapper mapper)
    {
        _todoRepository = todoRepository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<TodoResponse>> Handle(GetTodosQuery request, CancellationToken cancellationToken)
    {
        var todos = await _todoRepository.GetAllAsync(cancellationToken);
        return _mapper.Map<IEnumerable<TodoResponse>>(todos);
    }
}

