﻿using TMS.SharedKernal.Kafka;
using TMS.SharedKernal.Kafka.Abstractions;

namespace TMS.SharedKernel.Domain.Events.AuditLog;

public class AuditLogMessage : BaseMessage
{
    public string EventType { get; set; } = null!;

    public string Source { get; set; } = "database-interceptor";
    public string Subject { get; set; } = null!;
    public EntityChangeData Data { get; set; } = null!;
    public AuditMetadata? Metadata { get; set; }
}

public class NonAuditLogEventHandler : IMessageHandler<AuditLogMessage>
{
    public Task HandleAsync(AuditLogMessage message, MessageContext context, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}

public class EntityChangeData
{
    public string TableName { get; set; } = null!;
    public string EntityId { get; set; } = null!;
    public ChangeType ChangeType { get; set; }

    /// <summary>
    /// Transaction identifier - groups all changes within a single SaveChanges call
    /// </summary>
    public string? TransactionId { get; set; }

    /// <summary>
    /// Order of this change within the transaction
    /// </summary>
    public int? TransactionSequence { get; set; }

    /// <summary>
    /// Parent/Master entity ID if this is a detail record
    /// </summary>
    public string? ParentEntityId { get; set; }

    /// <summary>
    /// Parent/Master table name if this is a detail record
    /// </summary>
    public string? ParentTableName { get; set; }

    /// <summary>
    /// Dictionary of related entities via foreign keys
    /// Key: Navigation property name, Value: Related entity identifier (TableName/EntityId)
    /// </summary>
    public Dictionary<string, string>? RelatedEntities { get; set; }

    public Dictionary<string, object?>? OldValues { get; set; }
    public Dictionary<string, object?>? NewValues { get; set; }
    public string[]? ChangedProperties { get; set; }
}

public class AuditMetadata
{
    public string? UserId { get; set; }
    public string? UserName { get; set; }
    public string? SessionId { get; set; }
    public string? CorrelationId { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? Application { get; set; }
    public string? Environment { get; set; }
}

public enum ChangeType
{
    Created,
    Updated,
    Deleted
}
