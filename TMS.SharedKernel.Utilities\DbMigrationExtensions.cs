﻿using System.Diagnostics.CodeAnalysis;
using System.IO.Compression;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Npgsql;
using Serilog;
using TMS.SharedKernel.Domain.Services.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for logging.
/// </summary>
[ExcludeFromCodeCoverage]
public static class DbMigrationExtensions
{
    /// <summary>
    /// EnsureDatabaseCreated
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="app"></param>
    /// <param name="recreateKey">If provided, checks if key matches in __DatabaseControl table. If key doesn't match or not exist, database will be dropped and recreated. Example: "RECREATE01"</param>
    public static void EnsureDatabaseCreated<T>(this WebApplication app, string? recreateKey = null) where T : Microsoft.EntityFrameworkCore.DbContext
    {
        const int maxRetries = 3;
        const int baseDelayMs = 500;

        using (var scope = app.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<T>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<T>>();

            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                try
                {
                    if (!string.IsNullOrEmpty(recreateKey))
                    {
                        HandleDatabaseWithControlTable(context, logger, recreateKey);
                    }
                    else
                    {
                        context.Database.EnsureCreated(); // Creates database if it doesn't exist
                        logger.LogInformation("Database created successfully or already exists");
                    }
                    return; // Success
                }
                catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "XX000" && pgEx.Message.Contains("tuple concurrently updated"))
                {
                    if (attempt < maxRetries - 1)
                    {
                        var delay = baseDelayMs * (int)Math.Pow(2, attempt);
                        logger.LogWarning("Concurrent database creation detected, retrying in {Delay}ms (attempt {Attempt}/{MaxRetries})", delay, attempt + 1, maxRetries);
                        Thread.Sleep(delay);
                    }
                    else
                    {
                        logger.LogWarning("Database creation failed after {MaxRetries} attempts due to concurrent updates. Assuming another instance completed the operation.", maxRetries);
                        // Don't throw - allow the application to continue
                    }
                }
                catch (Exception ex)
                {
                    if (attempt < maxRetries - 1)
                    {
                        var delay = baseDelayMs * (int)Math.Pow(2, attempt);
                        logger.LogWarning(ex, "Database creation failed, retrying in {Delay}ms (attempt {Attempt}/{MaxRetries})", delay, attempt + 1, maxRetries);
                        Thread.Sleep(delay);
                    }
                    else
                    {
                        logger.LogError(ex, "Database creation failed after {MaxRetries} attempts", maxRetries);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Handles database creation/deletion based on control table settings (multi-instance safe)
    /// </summary>
    private static void HandleDatabaseWithControlTable<T>(T context, Microsoft.Extensions.Logging.ILogger logger, string? recreateKey = null) where T : Microsoft.EntityFrameworkCore.DbContext
    {
        var connectionString = context.Database.GetConnectionString();

        // Check if database exists
        bool databaseExists = context.Database.CanConnect();

        if (databaseExists)
        {
            logger.LogInformation("Database exists, checking control table for recreate key");

            // Check if recreation is needed based on key
            var shouldRecreate = ShouldRecreateBasedOnKey(connectionString, recreateKey, logger);

            if (shouldRecreate)
            {
                logger.LogWarning("Control table key mismatch or not found. Dropping database...");

                // Drop and recreate database
                try
                {
                    context.Database.EnsureDeleted();
                    logger.LogInformation("Database dropped successfully");
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Error dropping database (might already be dropped by another instance)");
                }

                context.Database.EnsureCreated();
                logger.LogInformation("Database recreated successfully");

                // Update the key in control table
                CreateOrUpdateControlTable(connectionString, recreateKey, logger);
                logger.LogInformation("Control table key updated to: {RecreateKey}", recreateKey ?? "NULL");
            }
            else
            {
                logger.LogInformation("Control table key matches, proceeding with existing database");
                context.Database.EnsureCreated(); // Ensure tables are created
            }
        }
        else
        {
            logger.LogInformation("Database does not exist, creating new database");
            context.Database.EnsureCreated();

            // Create control table with the key
            CreateOrUpdateControlTable(connectionString, recreateKey, logger);
            logger.LogInformation("Database created successfully with control table");
        }
    }

    /// <summary>
    /// Checks if the database should be recreated based on key comparison
    /// </summary>
    private static bool ShouldRecreateBasedOnKey(string connectionString, string? expectedKey, Microsoft.Extensions.Logging.ILogger logger)
    {
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            connection.Open();

            // Check if control table exists
            var tableExistsCommand = new NpgsqlCommand(@"
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_name = '__databasecontrol'", connection);

            var tableExists = Convert.ToInt32(tableExistsCommand.ExecuteScalar()) > 0;

            if (!tableExists)
            {
                logger.LogInformation("Control table does not exist, database will be recreated");
                return true;
            }

            // Check if recreate_key column exists
            var columnExistsCommand = new NpgsqlCommand(@"
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = '__databasecontrol'
                AND column_name = 'recreate_key'", connection);

            var columnExists = Convert.ToInt32(columnExistsCommand.ExecuteScalar()) > 0;

            if (!columnExists)
            {
                logger.LogInformation("Control table recreate_key column does not exist, database will be recreated");
                return true;
            }

            // Read the key from control table
            var checkKeyCommand = new NpgsqlCommand(@"
                SELECT recreate_key
                FROM __databasecontrol
                WHERE id = 1", connection);

            var result = checkKeyCommand.ExecuteScalar();
            var storedKey = result != null && result != DBNull.Value ? result.ToString() : null;

            // Try to parse both keys as version numbers (numeric only)
            if (int.TryParse(storedKey, out int storedVersion) && int.TryParse(expectedKey, out int expectedVersion))
            {
                // Compare versions numerically
                if (expectedVersion > storedVersion)
                {
                    logger.LogInformation("Version upgrade detected. Stored version: {StoredVersion}, Expected version: {ExpectedVersion}. Database will be recreated", storedVersion, expectedVersion);
                    return true;
                }
                else if (expectedVersion == storedVersion)
                {
                    logger.LogInformation("Version matches. Current version: {Version}, database will NOT be recreated", storedVersion);
                    return false;
                }
                else
                {
                    logger.LogWarning("Version downgrade detected. Stored version: {StoredVersion}, Expected version: {ExpectedVersion}. Database will NOT be recreated", storedVersion, expectedVersion);
                    return false;
                }
            }
            else
            {
                // Fall back to string comparison if either key is not a valid number
                if (storedKey == expectedKey)
                {
                    logger.LogInformation("Control table key matches expected key: {Key}, database will NOT be recreated", expectedKey);
                    return false;
                }
                else
                {
                    logger.LogInformation("Control table key mismatch. Stored: '{StoredKey}', Expected: '{ExpectedKey}'. Database will be recreated", storedKey, expectedKey);
                    return true;
                }
            }
        }
        catch (Npgsql.NpgsqlException ex) when (ex.Message.Contains("does not exist") || ex.SqlState == "3D000")
        {
            // Database was deleted by another instance - this is expected in multi-instance scenarios
            logger.LogInformation("Database does not exist (possibly deleted by another instance), will recreate");
            return true;
        }
        catch (Exception ex)
        {
            // On any other error, log and assume recreation is needed for safety
            logger.LogWarning(ex, "Error checking control table, defaulting to recreate database");
            return true;
        }
    }

    /// <summary>
    /// Creates or updates the database control table
    /// </summary>
    private static void CreateOrUpdateControlTable(string connectionString, string? recreateKey, Microsoft.Extensions.Logging.ILogger logger)
    {
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            connection.Open();

            // Create control table with key-based schema
            var createTableCommand = new NpgsqlCommand(@"
                CREATE TABLE IF NOT EXISTS __databasecontrol (
                    id INT PRIMARY KEY DEFAULT 1,
                    recreate_key TEXT,
                    last_updated TIMESTAMP NOT NULL DEFAULT NOW(),
                    notes TEXT,
                    CONSTRAINT single_row CHECK (id = 1)
                )", connection);

            createTableCommand.ExecuteNonQuery();
            logger.LogInformation("Control table created or already exists");

            // Check if old columns exist and migrate if needed
            var columnCheckCommand = new NpgsqlCommand(@"
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = '__databasecontrol'
                AND column_name IN ('should_recreate', 'recreate_count')", connection);

            using var reader = columnCheckCommand.ExecuteReader();
            var oldColumns = new List<string>();
            while (reader.Read())
            {
                oldColumns.Add(reader.GetString(0));
            }
            reader.Close();

            if (oldColumns.Count > 0)
            {
                logger.LogInformation("Migrating from old schema to key-based schema");

                // Add new column if it doesn't exist
                var addColumnCommand = new NpgsqlCommand(@"
                    ALTER TABLE __databasecontrol
                    ADD COLUMN IF NOT EXISTS recreate_key TEXT", connection);
                addColumnCommand.ExecuteNonQuery();

                // Drop old columns
                foreach (var oldColumn in oldColumns)
                {
                    var dropColumnCommand = new NpgsqlCommand($@"
                        ALTER TABLE __databasecontrol
                        DROP COLUMN IF EXISTS {oldColumn}", connection);
                    dropColumnCommand.ExecuteNonQuery();
                }

                logger.LogInformation("Migration completed: old columns dropped, recreate_key column added");
            }

            // Insert or update the control key
            var upsertCommand = new NpgsqlCommand(@"
                INSERT INTO __databasecontrol (id, recreate_key, last_updated, notes)
                VALUES (1, @recreateKey, NOW(), 'Auto-managed by EnsureDatabaseCreated')
                ON CONFLICT (id)
                DO UPDATE SET
                    recreate_key = @recreateKey,
                    last_updated = NOW()", connection);

            upsertCommand.Parameters.AddWithValue("@recreateKey", (object?)recreateKey ?? DBNull.Value);
            upsertCommand.ExecuteNonQuery();

            logger.LogInformation("Control table updated: RecreateKey = {RecreateKey}", recreateKey ?? "NULL");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating/updating control table");
        }
    }


    /// <summary>
    /// EnsureSchemasCreated
    /// </summary>
    /// <param name="app"></param>
    /// <param name="connString"></param>
    /// <param name="tableName"></param>
    /// <param name="indicator"></param>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    public static void EnsureSchemasCreated(this WebApplication app, string connString, string tableName, string indicator = "Default", Dictionary<string, string>? parameters = null)
    {
        using (var scope = app.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<IDatabaseInitializer>();
            context.EnsureSchemasAsync(connString, tableName, indicator: indicator, parameters: parameters)
                .GetAwaiter()
                .GetResult();
        }
    }

    /// <summary>
    /// EnsureSchemasMigrated
    /// </summary>
    /// <param name="app"></param>
    /// <param name="connString"></param>
    /// <param name="indicatorName"></param>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    public static void EnsureSchemasMigrated(this WebApplication app, string connString, string indicatorName, Dictionary<string, string>? parameters = null)
    {
        using (var scope = app.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<IDatabaseInitializer>();
            context.EnsureSchemasAsync(connString, $"Migration{indicatorName}", indicatorName, parameters: parameters)
                .GetAwaiter()
                .GetResult();
        }
    }

    /// <summary>
    /// EnsureAllDatabaseSchemasCreated
    /// </summary>
    /// <param name="app"></param>
    /// <param name="connectionString"></param>
    /// <param name="schemaOperations"></param>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    public static void EnsureAllDatabaseSchemasCreated(this WebApplication app,
        string connectionString,
        List<(string TableName, string Indicator)> schemaOperations,
        Dictionary<string, string>? parameters = null)
    {
        Log.Information("🚛 Starting database schema initiation process...");

        using (var scope = app.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<IDatabaseInitializer>();
            context.EnsureAllDatabaseSchemasAsync(connectionString, schemaOperations, parameters: parameters)
                .GetAwaiter()
                .GetResult();
        }

        Log.Information("🚛 Database schema initiation process completed.");
        Log.Information("🚛 Welcome to TMS...");
    }

    /// <summary>
    /// Ensures database is initialized in a multi-instance safe way.
    /// Only one instance will perform drop/recreate, others will wait.
    /// </summary>
    /// <param name="app"></param>
    /// <param name="connectionString"></param>
    /// <param name="initializationAction">Action to perform database initialization (EnsureDatabaseCreated + EnsureAllDatabaseSchemasCreated)</param>
    /// <param name="maxWaitSeconds">Maximum time to wait for initialization (default: 120 seconds)</param>
    public static void EnsureDatabaseInitializedWithCoordination<T>(
        this WebApplication app,
        string connectionString,
        Action initializationAction,
        int maxWaitSeconds = 120) where T : Microsoft.EntityFrameworkCore.DbContext
    {
        using (var scope = app.Services.CreateScope())
        {
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DatabaseCoordinator>>();
            var coordinator = new DatabaseCoordinator(logger);

            Log.Information("Starting coordinated database initialization...");

            coordinator.CoordinateDatabaseInitializationAsync(
                connectionString,
                async () =>
                {
                    // Execute the initialization action synchronously
                    await Task.Run(initializationAction);
                },
                maxWaitSeconds
            ).GetAwaiter().GetResult();

            Log.Information("Coordinated database initialization completed");
        }
    }

}
