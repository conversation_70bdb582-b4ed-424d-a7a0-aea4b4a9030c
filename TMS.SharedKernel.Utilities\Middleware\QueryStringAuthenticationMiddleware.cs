﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using System.Threading.Tasks;

namespace TMS.SharedKernel.Utilities.Middleware;

/// <summary>
/// Middleware that converts access_token query parameter to Authorization Bearer header
/// This is useful for scenarios where tokens cannot be sent via headers (e.g., SignalR, OAuth callbacks)
/// </summary>
public class QueryStringAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private const string AccessTokenQueryParam = "access_token";
    private const string CodeQueryParam = "code";

    private const string RedactedValue = "REDACTED_QS_TOKEN";

    public QueryStringAuthenticationMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Check if access_token exists in query string
        if (context.Request.Query.TryGetValue(AccessTokenQueryParam, out var accessToken) &&
            !string.IsNullOrWhiteSpace(accessToken))
        {
            // dont need this one
            //// 2. Rebuild the QueryString without the sensitive parameter
            //var query = context.Request.Query.Where(q => !q.Key.Equals(AccessTokenQueryParam, StringComparison.OrdinalIgnoreCase))
            //                         .ToList();

            //// 3. Add a placeholder to show that redaction occurred (optional)
            //query.Add(new KeyValuePair<string, Microsoft.Extensions.Primitives.StringValues>(AccessTokenQueryParam, RedactedValue));

            //// 4. Create a new QueryBuilder with the redacted parameters
            //var queryBuilder = new QueryBuilder(query);

            //// 5. Replace the HttpContext's internal QueryString property
            //// This ensures all downstream middleware and logging components 
            //// will see the modified (redacted) URL.
            //context.Request.QueryString = queryBuilder.ToQueryString();

            // Only set the header if it doesn't already exist
            if (!context.Request.Headers.ContainsKey("Authorization"))
            {
                context.Request.Headers["Authorization"] = $"Bearer {accessToken}";
            }
        }
        // Also check for 'code' parameter (for OAuth flows)
        else if (context.Request.Query.TryGetValue(CodeQueryParam, out var code) &&
                 !string.IsNullOrWhiteSpace(code))
        {
            // Store code in context items for later use if needed
            context.Items["oauth_code"] = code.ToString();
        }

        await _next(context);
    }
}
