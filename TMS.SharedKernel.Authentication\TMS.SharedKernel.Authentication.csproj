﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Version>1.2.4</Version>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="ResourceBased\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization.Policy" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.14.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
    <PackageReference Include="System.Security.Claims" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.SharedKernel.Domain\TMS.SharedKernel.Domain.csproj" />
  </ItemGroup>

</Project>
