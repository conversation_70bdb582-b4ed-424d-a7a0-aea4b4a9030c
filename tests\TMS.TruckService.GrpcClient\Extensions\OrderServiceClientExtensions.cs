using Grpc.Net.Client;
using Microsoft.Extensions.DependencyInjection;
using TMS.TruckService.Api.Grpc;

namespace TMS.TruckService.GrpcClient.Extensions;

/// <summary>
/// Extension methods for registering OrderServiceClient in DI
/// </summary>
public static class OrderServiceClientExtensions
{
    /// <summary>
    /// Adds the OrderServiceClient to the service collection
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="serverAddress">The gRPC server address (e.g., "https://localhost:5001")</param>
    /// <param name="configureChannel">Optional channel configuration</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddOrderServiceClient(
        this IServiceCollection services,
        string serverAddress,
        Action<GrpcChannelOptions>? configureChannel = null)
    {
        // Register the gRPC client using AddGrpcClient
        services.AddGrpcClient<OrderService.OrderServiceClient>(options =>
        {
            options.Address = new Uri(serverAddress);
        })
        .ConfigureChannel(channelOptions =>
        {
            // Default configuration
            channelOptions.MaxReceiveMessageSize = 16 * 1024 * 1024; // 16 MB
            channelOptions.MaxSendMessageSize = 16 * 1024 * 1024; // 16 MB

            // Apply custom configuration if provided
            configureChannel?.Invoke(channelOptions);
        });

        // Register our wrapper client
        services.AddScoped<IOrderServiceClient, OrderServiceClient>();

        return services;
    }

    /// <summary>
    /// Adds the OrderServiceClient to the service collection with configuration callback
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configure">Configuration callback</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddOrderServiceClient(
        this IServiceCollection services,
        Action<OrderServiceClientOptions> configure)
    {
        var options = new OrderServiceClientOptions();
        configure(options);

        return services.AddOrderServiceClient(options.ServerAddress, options.ConfigureChannel);
    }
}

/// <summary>
/// Configuration options for OrderServiceClient
/// </summary>
public class OrderServiceClientOptions
{
    /// <summary>
    /// The gRPC server address (default: http://localhost:5000)
    /// </summary>
    public string ServerAddress { get; set; } = "http://localhost:5000";

    /// <summary>
    /// Optional channel configuration
    /// </summary>
    public Action<GrpcChannelOptions>? ConfigureChannel { get; set; }
}
