using Microsoft.Extensions.Logging;
using Npgsql;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Coordinates database initialization across multiple instances to prevent race conditions
/// </summary>
public class DatabaseCoordinator
{
    private readonly ILogger<DatabaseCoordinator> _logger;

    public DatabaseCoordinator(ILogger<DatabaseCoordinator> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Ensures database is initialized in a multi-instance safe way.
    /// Only one instance will perform initialization, others will wait.
    /// </summary>
    /// <param name="connectionString">Database connection string</param>
    /// <param name="initializationAction">Action to perform database initialization</param>
    /// <param name="maxWaitSeconds">Maximum time to wait for initialization (default: 120 seconds)</param>
    public async Task CoordinateDatabaseInitializationAsync(
        string connectionString,
        Func<Task> initializationAction,
        int maxWaitSeconds = 120)
    {
        const int lockId = 999888777; // Unique ID for database initialization lock
        const int pollIntervalMs = 500;
        var maxAttempts = (maxWaitSeconds * 1000) / pollIntervalMs;

        var (acquiredLock, isLeader) = await TryAcquireInitializationLockAsync(connectionString, lockId);

        if (isLeader)
        {
            _logger.LogInformation("This instance acquired the initialization lock - will perform database initialization");

            try
            {
                // This instance is responsible for initialization
                await initializationAction();

                _logger.LogInformation("Database initialization completed successfully by this instance");
            }
            finally
            {
                // Release the lock
                await ReleaseInitializationLockAsync(connectionString, lockId);
                _logger.LogInformation("Released initialization lock");
            }
        }
        else
        {
            _logger.LogInformation("Another instance is initializing the database - waiting for completion...");

            // Wait for the database to be ready
            for (int attempt = 0; attempt < maxAttempts; attempt++)
            {
                if (await IsDatabaseReadyAsync(connectionString))
                {
                    _logger.LogInformation("Database is ready - proceeding with startup");
                    return;
                }

                if (attempt % 10 == 0) // Log every 5 seconds
                {
                    _logger.LogInformation("Still waiting for database initialization... ({Elapsed}s elapsed)",
                        (attempt * pollIntervalMs) / 1000);
                }

                await Task.Delay(pollIntervalMs);
            }

            _logger.LogWarning("Timeout waiting for database initialization after {MaxWait}s - proceeding anyway",
                maxWaitSeconds);
        }
    }

    /// <summary>
    /// Tries to acquire the initialization lock using PostgreSQL advisory locks
    /// </summary>
    private async Task<(bool acquired, bool isLeader)> TryAcquireInitializationLockAsync(
        string connectionString, int lockId)
    {
        try
        {
            // Try to connect to the database
            await using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            // Try to acquire an advisory lock (non-blocking)
            var command = new NpgsqlCommand("SELECT pg_try_advisory_lock(@lockId)", connection);
            command.Parameters.AddWithValue("@lockId", (long)lockId);

            var acquired = (bool)await command.ExecuteScalarAsync();

            if (acquired)
            {
                _logger.LogDebug("Successfully acquired advisory lock {LockId}", lockId);
            }
            else
            {
                _logger.LogDebug("Advisory lock {LockId} is held by another instance", lockId);
            }

            return (true, acquired);
        }
        catch (PostgresException pgEx) when (pgEx.SqlState == "3D000")
        {
            // Database doesn't exist - this is the first instance
            _logger.LogInformation("Database does not exist - this instance will create it");
            return (true, true);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error acquiring initialization lock - assuming leader role for safety");
            return (false, true); // If we can't check, assume leader to avoid deadlock
        }
    }

    /// <summary>
    /// Releases the initialization lock
    /// </summary>
    private async Task ReleaseInitializationLockAsync(string connectionString, int lockId)
    {
        try
        {
            await using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            var command = new NpgsqlCommand("SELECT pg_advisory_unlock(@lockId)", connection);
            command.Parameters.AddWithValue("@lockId", (long)lockId);

            await command.ExecuteScalarAsync();
            _logger.LogDebug("Released advisory lock {LockId}", lockId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error releasing initialization lock (may have already been released)");
        }
    }

    /// <summary>
    /// Checks if the database is ready (exists and has basic tables)
    /// </summary>
    private async Task<bool> IsDatabaseReadyAsync(string connectionString)
    {
        try
        {
            await using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            // Check if __databasecontrol table exists (created by EnsureDatabaseCreated)
            var command = new NpgsqlCommand(@"
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = '__databasecontrol'
                )", connection);

            var exists = (bool)await command.ExecuteScalarAsync();

            if (!exists)
            {
                _logger.LogDebug("Database control table does not exist yet");
                return false;
            }

            // Verify we can read from the control table
            var readCommand = new NpgsqlCommand("SELECT COUNT(*) FROM __databasecontrol", connection);
            await readCommand.ExecuteScalarAsync();

            _logger.LogDebug("Database is ready and accessible");
            return true;
        }
        catch (PostgresException pgEx) when (pgEx.SqlState == "3D000")
        {
            _logger.LogDebug("Database does not exist yet");
            return false;
        }
        catch (PostgresException pgEx) when (pgEx.SqlState == "42P01")
        {
            _logger.LogDebug("Database exists but tables not yet created");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error checking database readiness");
            return false;
        }
    }

}
