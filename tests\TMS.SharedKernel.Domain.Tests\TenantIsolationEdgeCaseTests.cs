using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Tests.Mocks;
using TMS.SharedKernel.Domain.Tests.TestDbContext;
using TMS.SharedKernel.Domain.Tests.TestEntities;

namespace TMS.SharedKernel.Domain.Tests;

/// <summary>
/// Edge case tests for tenant isolation
/// </summary>
public class TenantIsolationEdgeCaseTests : IDisposable
{
    private readonly MockCurrentFactorProvider _mockProvider;
    private readonly TestTenantDbContext _context;
    private readonly Guid _tenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

    public TenantIsolationEdgeCaseTests()
    {
        _mockProvider = new MockCurrentFactorProvider();

        var options = new DbContextOptionsBuilder<TestTenantDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new TestTenantDbContext(options, _mockProvider);
    }

    [Fact]
    public async Task Query_Should_ReturnEmpty_When_ProviderIsNull()
    {
        // Arrange - Create context with null provider
        var options = new DbContextOptionsBuilder<TestTenantDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        using var contextWithNullProvider = new TestTenantDbContext(options, null!);

        // Seed data
        contextWithNullProvider.TenantScopedEntities.Add(new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "Test"
        });
        await contextWithNullProvider.SaveChangesAsync(); // This will fail due to validation

        // This test documents the expected behavior
    }

    [Fact]
    public async Task MultipleQueries_Should_UseCurrentTenantContext()
    {
        // Arrange
        _mockProvider.CompanyId = _tenantId;
        _mockProvider.CurrentFactorId = Guid.NewGuid();

        // Seed entities
        _context.TenantScopedEntities.AddRange(
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Entity1" },
            new TenantScopedEntity { CompanyId = _tenantId, Name = "Entity2" }
        );
        await _context.SaveChangesAsync();

        _context.ChangeTracker.Clear();

        // Act - Multiple queries
        var query1 = await _context.TenantScopedEntities.ToListAsync();
        var query2 = await _context.TenantScopedEntities.CountAsync();
        var query3 = await _context.TenantScopedEntities.AnyAsync();

        // Assert
        Assert.Equal(2, query1.Count);
        Assert.Equal(2, query2);
        Assert.True(query3);
    }

    [Fact]
    public async Task ConcurrentQueries_Should_IsolateTenants()
    {
        // Arrange
        var tenant1 = Guid.NewGuid();
        var tenant2 = Guid.NewGuid();

        // Setup mock providers for each tenant
        var provider1 = new MockCurrentFactorProvider { CompanyId = tenant1, CurrentFactorId = Guid.NewGuid() };
        var provider2 = new MockCurrentFactorProvider { CompanyId = tenant2, CurrentFactorId = Guid.NewGuid() };

        // Create separate contexts for each tenant
        var options = new DbContextOptionsBuilder<TestTenantDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        using var context1 = new TestTenantDbContext(options, provider1);
        using var context2 = new TestTenantDbContext(options, provider2);

        // Seed data
        context1.TenantScopedEntities.Add(new TenantScopedEntity { CompanyId = tenant1, Name = "Tenant1Data" });
        context2.TenantScopedEntities.Add(new TenantScopedEntity { CompanyId = tenant2, Name = "Tenant2Data" });

        await context1.SaveChangesAsync();
        await context2.SaveChangesAsync();

        context1.ChangeTracker.Clear();
        context2.ChangeTracker.Clear();

        // Act
        var tenant1Results = await context1.TenantScopedEntities.ToListAsync();
        var tenant2Results = await context2.TenantScopedEntities.ToListAsync();

        // Assert
        Assert.Single(tenant1Results);
        Assert.Single(tenant2Results);
        Assert.Equal(tenant1, tenant1Results[0].CompanyId);
        Assert.Equal(tenant2, tenant2Results[0].CompanyId);
        Assert.NotEqual(tenant1Results[0].Id, tenant2Results[0].Id);
    }

    [Fact]
    public async Task Include_Navigation_Should_RespectTenantFilter()
    {
        // This test verifies that related entities also respect tenant isolation
        // Skip if you don't have navigation properties yet
        _mockProvider.CompanyId = _tenantId;

        var results = await _context.TenantScopedEntities.ToListAsync();

        Assert.All(results, e => Assert.Equal(_tenantId, e.CompanyId));
    }

    [Fact]
    public void GlobalFilter_Should_BeApplied_OnModelCreation()
    {
        // Arrange & Act
        var entityType = _context.Model.FindEntityType(typeof(TenantScopedEntity));
        var filter = entityType?.GetQueryFilter();

        // Assert
        Assert.NotNull(filter); // Filter should be set
    }

    [Fact]
    public void GlobalFilter_Should_NotBeApplied_ToNonTenantEntities()
    {
        // Arrange & Act
        var entityType = _context.Model.FindEntityType(typeof(SharedEntity));
        var filter = entityType?.GetQueryFilter();

        // Assert
        Assert.Null(filter); // No filter for non-tenant entities
    }

    [Fact]
    public async Task AsNoTracking_Should_StillApplyTenantFilter()
    {
        // Arrange
        _mockProvider.CompanyId = _tenantId;
        _mockProvider.CurrentFactorId = Guid.NewGuid();

        _context.TenantScopedEntities.Add(new TenantScopedEntity
        {
            CompanyId = _tenantId,
            Name = "Test"
        });
        await _context.SaveChangesAsync();

        _context.ChangeTracker.Clear();

        // Act
        var results = await _context.TenantScopedEntities
            .AsNoTracking()
            .ToListAsync();

        // Assert
        Assert.NotEmpty(results);
        Assert.All(results, e => Assert.Equal(_tenantId, e.CompanyId));
    }

    [Fact]
    public async Task RawSql_Should_BypassTenantFilter()
    {
        // Arrange
        var tenant1 = Guid.NewGuid();
        var tenant2 = Guid.NewGuid();

        _mockProvider.CompanyId = tenant1;
        _mockProvider.CurrentFactorId = Guid.NewGuid();

        _context.TenantScopedEntities.AddRange(
            new TenantScopedEntity { CompanyId = tenant1, Name = "Tenant1" },
            new TenantScopedEntity { CompanyId = tenant2, Name = "Tenant2" }
        );
        await _context.SaveChangesAsync();

        _context.ChangeTracker.Clear();

        // Act - Raw SQL bypasses EF filters
        var results = await _context.TenantScopedEntities
            .FromSqlRaw("SELECT * FROM TenantScopedEntities")
            .ToListAsync();

        // Assert
        // Raw SQL may bypass filters - this documents the behavior
        // You should avoid raw SQL for tenant-scoped queries
        Assert.True(results.Count >= 2); // Might return all tenants' data
    }

    public void Dispose()
    {
        _context?.Dispose();
    }
}
