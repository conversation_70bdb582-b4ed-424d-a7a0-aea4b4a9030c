﻿using MapsterMapper;
using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.TruckService.Contracts.Todos;
using TMS.TruckService.Domain.Entities;

namespace TMS.TruckService.Application.Features.Todos.Commands.UpdateTodo;

public class UpdateTodoCommandHandler : IRequestHandler<UpdateTodoCommand, TodoResponse>
{
    private readonly IBaseRepository<Todo> _todoRepository;
    private readonly IMapper _mapper;

    public UpdateTodoCommandHandler(IBaseRepository<Todo> todoRepository, IMapper mapper)
    {
        _todoRepository = todoRepository;
        _mapper = mapper;
    }

    public async Task<TodoResponse> Handle(UpdateTodoCommand request, CancellationToken cancellationToken)
    {
        var existingTodo = await _todoRepository.GetByIdAsync(request.Id, cancellationToken, asNoTracking: false);

        if (existingTodo == null)
            //throw new KeyNotFoundException(MessageFormatter.FormatNotFound(nameof(Todo), request.Id));
            throw new BusinessRuleValidationException(nameof(Todo.Id), MessageFormatter.FormatNotFound(nameof(Todo), request.Id));

        bool wasCompleted = existingTodo.IsCompleted;

        existingTodo.Title = request.Title;
        existingTodo.Description = request.Description;
        existingTodo.IsCompleted = request.IsCompleted;

        if (request.IsCompleted && !wasCompleted)
            existingTodo.CompletedAt = DateTime.UtcNow;
        else if (!request.IsCompleted)
            existingTodo.CompletedAt = null;

        _todoRepository.Update(existingTodo);

        // Return the mapped TodoResponse
        return _mapper.Map<TodoResponse>(existingTodo);
    }
}

