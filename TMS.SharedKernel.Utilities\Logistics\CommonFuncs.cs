﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace TMS.SharedKernel.Utilities.Logistics;

/// <summary>
/// CommonFuncs (logistics)
/// </summary>
public static class CommonFuncs
{
    #region Distance Calculations

    /// <summary>
    /// Calculates the distance between two geographic coordinates using the Haversine formula
    /// </summary>
    /// <param name="lat1">Latitude of first point</param>
    /// <param name="lon1">Longitude of first point</param>
    /// <param name="lat2">Latitude of second point</param>
    /// <param name="lon2">Longitude of second point</param>
    /// <returns>Distance in kilometers</returns>
    public static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double earthRadiusKm = 6371.0;

        var dLat = DegreesToRadians(lat2 - lat1);
        var dLon = DegreesToRadians(lon2 - lon1);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return earthRadiusKm * c;
    }

    /// <summary>
    /// Calculates total route distance from a list of coordinates
    /// </summary>
    public static double CalculateRouteDistance(List<(double Latitude, double Longitude)> waypoints)
    {
        if (waypoints == null || waypoints.Count < 2)
            return 0;

        double totalDistance = 0;
        for (int i = 0; i < waypoints.Count - 1; i++)
        {
            totalDistance += CalculateDistance(
                waypoints[i].Latitude, waypoints[i].Longitude,
                waypoints[i + 1].Latitude, waypoints[i + 1].Longitude);
        }

        return totalDistance;
    }

    private static double DegreesToRadians(double degrees) => degrees * Math.PI / 180.0;

    #endregion

    #region Weight & Volume Calculations

    /// <summary>
    /// Calculates volumetric weight (dimensional weight) for air freight
    /// </summary>
    /// <param name="length">Length in cm</param>
    /// <param name="width">Width in cm</param>
    /// <param name="height">Height in cm</param>
    /// <param name="divisor">Standard divisor (default: 5000 for air freight)</param>
    /// <returns>Volumetric weight in kg</returns>
    public static double CalculateVolumetricWeight(double length, double width, double height, double divisor = 5000)
    {
        return (length * width * height) / divisor;
    }

    /// <summary>
    /// Calculates chargeable weight (max of actual weight and volumetric weight)
    /// </summary>
    public static double CalculateChargeableWeight(double actualWeight, double length, double width, double height, double divisor = 5000)
    {
        var volumetricWeight = CalculateVolumetricWeight(length, width, height, divisor);
        return Math.Max(actualWeight, volumetricWeight);
    }

    /// <summary>
    /// Calculates total volume in cubic meters
    /// </summary>
    public static double CalculateVolume(double length, double width, double height)
    {
        return (length * width * height) / 1_000_000; // Convert cm³ to m³
    }

    #endregion

    #region Load & Capacity Calculations

    /// <summary>
    /// Calculates the utilization percentage
    /// </summary>
    public static double CalculateUtilizationPercentage(double usedCapacity, double totalCapacity)
    {
        if (totalCapacity <= 0) return 0;
        return Math.Round((usedCapacity / totalCapacity) * 100, 2);
    }

    /// <summary>
    /// Checks if load exceeds vehicle capacity
    /// </summary>
    public static bool IsOverloaded(double currentLoad, double maxCapacity)
    {
        return currentLoad > maxCapacity;
    }

    /// <summary>
    /// Calculates remaining capacity
    /// </summary>
    public static double CalculateRemainingCapacity(double totalCapacity, double usedCapacity)
    {
        return Math.Max(0, totalCapacity - usedCapacity);
    }

    #endregion

    #region Time & Speed Calculations

    /// <summary>
    /// Calculates estimated time of arrival (ETA) based on distance and average speed
    /// </summary>
    /// <param name="distanceKm">Distance in kilometers</param>
    /// <param name="averageSpeedKmh">Average speed in km/h</param>
    /// <param name="startTime">Start time</param>
    /// <returns>Estimated arrival time</returns>
    public static DateTime CalculateETA(double distanceKm, double averageSpeedKmh, DateTime startTime)
    {
        if (averageSpeedKmh <= 0) return startTime;
        var travelTimeHours = distanceKm / averageSpeedKmh;
        return startTime.AddHours(travelTimeHours);
    }

    /// <summary>
    /// Calculates estimated travel time in hours
    /// </summary>
    public static double CalculateTravelTime(double distanceKm, double averageSpeedKmh)
    {
        if (averageSpeedKmh <= 0) return 0;
        return distanceKm / averageSpeedKmh;
    }

    /// <summary>
    /// Calculates average speed based on distance and time
    /// </summary>
    public static double CalculateAverageSpeed(double distanceKm, TimeSpan travelTime)
    {
        if (travelTime.TotalHours <= 0) return 0;
        return distanceKm / travelTime.TotalHours;
    }

    #endregion

    #region Cost Calculations

    /// <summary>
    /// Calculates cost per kilometer
    /// </summary>
    public static decimal CalculateCostPerKm(decimal totalCost, double distanceKm)
    {
        if (distanceKm <= 0) return 0;
        return Math.Round(totalCost / (decimal)distanceKm, 2);
    }

    /// <summary>
    /// Calculates cost per ton
    /// </summary>
    public static decimal CalculateCostPerTon(decimal totalCost, double weightTons)
    {
        if (weightTons <= 0) return 0;
        return Math.Round(totalCost / (decimal)weightTons, 2);
    }

    /// <summary>
    /// Calculates fuel cost based on distance, consumption rate, and fuel price
    /// </summary>
    /// <param name="distanceKm">Distance in kilometers</param>
    /// <param name="fuelConsumptionPer100Km">Fuel consumption in liters per 100 km</param>
    /// <param name="fuelPricePerLiter">Fuel price per liter</param>
    public static decimal CalculateFuelCost(double distanceKm, double fuelConsumptionPer100Km, decimal fuelPricePerLiter)
    {
        var fuelUsed = (distanceKm / 100) * fuelConsumptionPer100Km;
        return Math.Round((decimal)fuelUsed * fuelPricePerLiter, 2);
    }

    #endregion

    #region Delivery & Route Optimization

    /// <summary>
    /// Calculates delivery priority score based on urgency and SLA
    /// </summary>
    public static int CalculateDeliveryPriority(DateTime deadline, DateTime currentTime, bool isExpress = false)
    {
        var hoursRemaining = (deadline - currentTime).TotalHours;
        var priority = hoursRemaining switch
        {
            <= 4 => 1,    // Critical
            <= 12 => 2,   // High
            <= 24 => 3,   // Medium
            _ => 4        // Low
        };

        return isExpress ? Math.Max(1, priority - 1) : priority;
    }

    /// <summary>
    /// Checks if delivery is delayed based on SLA
    /// </summary>
    public static bool IsDeliveryDelayed(DateTime expectedDelivery, DateTime? actualDelivery)
    {
        if (!actualDelivery.HasValue) return DateTime.Now > expectedDelivery;
        return actualDelivery.Value > expectedDelivery;
    }

    /// <summary>
    /// Calculates delay time in hours
    /// </summary>
    public static double CalculateDelayHours(DateTime expectedTime, DateTime actualTime)
    {
        var delay = actualTime - expectedTime;
        return delay.TotalHours > 0 ? delay.TotalHours : 0;
    }

    #endregion

    #region Pallet & Container Calculations

    /// <summary>
    /// Calculates number of pallets needed
    /// </summary>
    public static int CalculatePalletCount(double totalWeight, double maxWeightPerPallet)
    {
        if (maxWeightPerPallet <= 0) return 0;
        return (int)Math.Ceiling(totalWeight / maxWeightPerPallet);
    }

    /// <summary>
    /// Checks if packages fit in a container (simplified calculation)
    /// </summary>
    public static bool CanFitInContainer(double totalVolume, double containerVolume)
    {
        return totalVolume <= containerVolume;
    }

    /// <summary>
    /// Calculates number of containers needed
    /// </summary>
    public static int CalculateContainerCount(double totalVolume, double containerVolume)
    {
        if (containerVolume <= 0) return 0;
        return (int)Math.Ceiling(totalVolume / containerVolume);
    }

    #endregion

    #region Fuel & Emission Calculations

    /// <summary>
    /// Calculates CO2 emissions based on fuel consumption
    /// </summary>
    /// <param name="fuelLiters">Fuel consumed in liters</param>
    /// <param name="co2PerLiter">CO2 emission factor (default: 2.68 kg/liter for diesel)</param>
    public static double CalculateCO2Emissions(double fuelLiters, double co2PerLiter = 2.68)
    {
        return fuelLiters * co2PerLiter;
    }

    #endregion

    #region Zone & Service Area

    /// <summary>
    /// Checks if a coordinate is within a circular service area
    /// </summary>
    public static bool IsWithinServiceArea(double centerLat, double centerLon, double radiusKm, double pointLat, double pointLon)
    {
        var distance = CalculateDistance(centerLat, centerLon, pointLat, pointLon);
        return distance <= radiusKm;
    }

    #endregion

    #region Sorting & Grouping Helpers

    /// <summary>
    /// Generates a route code based on origin and destination
    /// </summary>
    public static string GenerateRouteCode(string origin, string destination)
    {
        return $"{origin?.ToUpperInvariant()}-{destination?.ToUpperInvariant()}";
    }

    /// <summary>
    /// Calculates batch/wave number based on time window
    /// </summary>
    public static int CalculateBatchNumber(DateTime orderTime, TimeSpan batchInterval)
    {
        if (batchInterval.TotalMinutes <= 0) return 0;
        return (int)(orderTime.TimeOfDay.TotalMinutes / batchInterval.TotalMinutes);
    }

    #endregion
}
