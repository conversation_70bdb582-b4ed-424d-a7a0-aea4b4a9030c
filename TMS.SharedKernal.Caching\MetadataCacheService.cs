﻿using StackExchange.Redis;
using TMS.SharedKernal.SmoothRedis;

namespace TMS.SharedKernal.Caching;

/// <summary>
/// Generic metadata caching service for storing and retrieving lists of entities in Redis
/// Uses a configuration-based approach to map entity types to Redis keys
/// </summary>
public class MetadataCacheService : IMetadataCacheService
{
    private readonly ISmoothRedis _redis;
    private readonly Dictionary<Type, MetadataCacheKeyConfiguration> _configurations;

    public MetadataCacheService(
        ISmoothRedis redis,
        IEnumerable<MetadataCacheKeyConfiguration> configurations)
    {
        _redis = redis;
        _configurations = configurations.ToDictionary(c => c.EntityType);
    }

    #region Generic Methods

    public async Task SetAsync<T>(List<T> items, string prefix = "") where T : class
    {
        string redisKey = GetRedisKey<T>(prefix);
        // Delete existing key to ensure clean state before adding new items
        await _redis.Cache.RemoveAsync(redisKey);

        if (items == null || items.Count == 0)
            return;
        
        var redisCollection = _redis.Collections.List<T>(redisKey);
        
        await redisCollection.PushAsync(items, toLeft: false);
    }

    public async Task<List<T>> GetAsync<T>(string prefix = "") where T : class
    {
        string redisKey = GetRedisKey<T>(prefix);
        var redisCollection = _redis.Collections.List<T>(redisKey);
        return await redisCollection.RangeAsync();
    }

    public async Task SetAsync<T>(Dictionary<string, T> items, string prefix = "") where T : class
    {
        string redisKey = GetRedisKey<T>(prefix);

        if (items == null || items.Count == 0)
        {
            await _redis.Cache.RemoveAsync(redisKey);
            return;
        }

        // Store entire dictionary as a single JSON blob for better performance
        // Single serialization operation instead of serializing each item separately
        await _redis.Cache.SetAsync(redisKey, items);
    }

    public async Task<Dictionary<string, T>> GetDictionaryAsync<T>(string prefix = "") where T : class
    {
        string redisKey = GetRedisKey<T>(prefix);

        // Retrieve entire dictionary with single deserialization operation
        var result = await _redis.Cache.GetAsync<Dictionary<string, T>>(redisKey);

        return result ?? new Dictionary<string, T>();
    }

    #endregion

    #region Convenience Methods - ServiceType

    public async Task SetServiceTypesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetServiceTypesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    #endregion

    #region Convenience Methods - ExtraService

    public async Task SetExtraServicesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetExtraServicesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    #endregion

    #region Convenience Methods - OrderStatus

    public async Task SetOrderStatusesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetOrderStatusesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    #endregion

    #region Convenience Methods - PriorityPlan

    public async Task SetPriorityPlansAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetPriorityPlansAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    #endregion

    public async Task SetWardsAsync<T>(List<T> items, string provinceId) where T : class
    {
        await SetAsync(items, provinceId);
    }

    public async Task<List<T>> GetWardsAsync<T>(string provinceId) where T : class
    {
        return await GetAsync<T>(provinceId);
    }

    public async Task SetProvincesAsync<T>(List<T> items, string countryId) where T : class
    {
        await SetAsync(items, countryId);
    }

    public async Task<List<T>> GetProvincesAsync<T>(string countryId) where T : class
    {
        return await GetAsync<T>(countryId);
    }

    public async Task SetPostOfficesAsync<T>(List<T> items, TimeSpan? expiry = null) where T : class
    {
        if (items == null || items.Count == 0)
            return;

        // Use reflection to get PostOfficeCode property
        var type = typeof(T);
        var codeProperty = type.GetProperty("PostOfficeCode");

        if (codeProperty == null)
        {
            throw new ArgumentException($"Type {type.Name} must have a PostOfficeCode property for post office caching.");
        }

        // Build dictionary with individual keys for each office
        var keyValuePairs = new Dictionary<string, T>();

        foreach (var item in items)
        {
            var code = codeProperty.GetValue(item)?.ToString();
            if (!string.IsNullOrWhiteSpace(code))
            {
                var redisKey = $"metadata:postoffices:{code}";
                keyValuePairs[redisKey] = item;
            }
        }

        // Use batch operation to set all offices at once
        await _redis.Batch.Cache.SetManyAsync(keyValuePairs, expiry);
    }

    public async Task<Dictionary<string, T>> GetPostOfficesAsync<T>(List<string> codes) where T : class
    {
        if (codes == null || codes.Count == 0)
            return new Dictionary<string, T>();

        // Build Redis keys for all requested codes
        var redisKeys = codes.Select(code => $"metadata:postoffices:{code}").ToArray();

        // Use batch operation to get all offices at once
        var results = await _redis.Batch.Cache.GetManyAsync<T>(redisKeys);

        // Transform results back to use office code as key (not full Redis key)
        var output = new Dictionary<string, T>();
        foreach (var kvp in results)
        {
            if (kvp.Value != null)
            {
                // Extract office code from Redis key: "metadata:postoffices:{code}" -> "{code}"
                var code = kvp.Key.Replace("metadata:postoffices:", "");
                output[code] = kvp.Value;
            }
        }

        return output;
    }

    private string GetRedisKey<T>(string prefix) where T : class
    {
        var entityType = typeof(T);

        if (!_configurations.TryGetValue(entityType, out var config))
        {
            throw new ArgumentException(
                $"No Redis cache key configuration found for type {entityType.Name}. " +
                $"Please register a MetadataCacheKeyConfiguration for this type.");
        }

        // Check if prefix is required but not provided
        if (config.RequiresPrefix && string.IsNullOrWhiteSpace(prefix))
        {
            throw new ArgumentNullException(
                nameof(prefix),
                $"Prefix is required for caching type {entityType.Name}.");
        }

        // Replace {prefix} placeholder in the template
        var redisKey = config.RedisKeyTemplate;
        if (config.RequiresPrefix)
        {
            redisKey = redisKey.Replace("{prefix}", prefix);
        }

        return redisKey;
    }

    public async Task SetZonesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetZonesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    public async Task SetLeadTimeTypesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetLeadTimeTypesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    public async Task SetTransportMethodTypesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetTransportMethodTypesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }

    public async Task SetTransportVehicleTypesAsync<T>(List<T> items) where T : class
    {
        await SetAsync(items);
    }

    public async Task<List<T>> GetTransportVehicleTypesAsync<T>() where T : class
    {
        return await GetAsync<T>();
    }
}
