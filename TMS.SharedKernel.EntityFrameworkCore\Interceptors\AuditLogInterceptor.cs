﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernel.Domain.Entities.Interfaces;
using TMS.SharedKernel.Domain.Events.AuditLog;
using TMS.SharedKernel.EntityFrameworkCore.Configuration;
using TMS.SharedKernel.EntityFrameworkCore.Services;

namespace TMS.SharedKernel.EntityFrameworkCore.Interceptors;

/// <summary>
/// AuditLogInterceptor
/// </summary>
public class AuditLogInterceptor : SaveChangesInterceptor
{
    private readonly IMessageProducer _eventPublisher;
    private readonly AuditContextProvider _auditContextProvider;
    private readonly ILogger<AuditLogInterceptor> _logger;
    private readonly InterceptorConfiguration _config;

    /// <summary>
    /// AuditLogInterceptor
    /// </summary>
    /// <param name="serviceProvider"></param>
    /// <param name="auditContextProvider"></param>
    /// <param name="logger"></param>
    /// <param name="config"></param>
    public AuditLogInterceptor(
        IServiceProvider serviceProvider,
        AuditContextProvider auditContextProvider,
        ILogger<AuditLogInterceptor> logger,
        IOptions<InterceptorConfiguration> config)
    {
        _config = config.Value;

        // Resolve the IMessageProducer using the configured service key
        _eventPublisher = serviceProvider.GetRequiredKeyedService<IMessageProducer>(_config.MessageProducerServiceKey);

        _auditContextProvider = auditContextProvider;
        _logger = logger;
    }

    /// <summary>
    /// SavingChangesAsync
    /// </summary>
    /// <param name="eventData"></param>
    /// <param name="result"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        if (eventData.Context != null && _config.EnableEventPublishing)
        {
            await PublishDatabaseChangeEventsAsync(eventData.Context, cancellationToken);
        }

        return await base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private async Task PublishDatabaseChangeEventsAsync(DbContext context, CancellationToken cancellationToken)
    {
        var changeEvents = new List<AuditLogMessage>();
        var auditMetadata = _auditContextProvider.GetCurrentAuditMetadata();

        // Generate transaction ID if grouping is enabled
        string? transactionId = _config.GroupByTransaction ? Guid.NewGuid().ToString() : null;
        int sequence = 0;

        // Collect all entries first to ensure consistent ordering
        var entries = context.ChangeTracker.Entries().ToList();

        // Check for excessive changes
        if (entries.Count > _config.MaxChangesPerTransaction)
        {
            _logger.LogWarning(
                "Transaction contains {Count} changes, exceeding configured maximum of {Max}",
                entries.Count,
                _config.MaxChangesPerTransaction);
        }

        foreach (var entry in entries)
        {
            if (!ShouldPublishEvent(entry)) continue;

            var changeEvent = CreateDatabaseChangeEvent(entry, auditMetadata, transactionId, sequence++);
            if (changeEvent != null)
            {
                changeEvents.Add(changeEvent);
            }
        }

        if (changeEvents.Count > 0)
        {
            if (_config.PublishAsAtomic)
            {
                // Publish all events together as an atomic batch
                var tasks = changeEvents.Select(evt => _eventPublisher.ProduceAsync<AuditLogMessage>(evt, cancellationToken: cancellationToken));
                await Task.WhenAll(tasks);
            }
            else
            {
                // Publish events individually but with same TransactionId
                var tasks = changeEvents.Select(evt => _eventPublisher.ProduceAsync<AuditLogMessage>(evt, cancellationToken: cancellationToken));
                await Task.WhenAll(tasks);
            }

            _logger.LogInformation(
                "Published {Count} database change events with TransactionId: {TransactionId}",
                changeEvents.Count,
                transactionId ?? "none");
        }
    }

    private bool ShouldPublishEvent(EntityEntry entry)
    {
        // Skip unchanged entities
        if (entry.State == EntityState.Unchanged || entry.State == EntityState.Detached)
            return false;

        // Skip entities marked with INoAudit
        if (entry.Entity is INoAudit)
            return false;

        // Skip excluded entity types
        var entityType = entry.Entity.GetType();
        if (_config.ExcludedEntityTypes.Contains(entityType))
            return false;

        // Skip excluded tables
        var tableName = GetTableName(entry);
        if (_config.ExcludedTables.Contains(tableName))
            return false;

        return true;
    }

    private AuditLogMessage? CreateDatabaseChangeEvent(EntityEntry entry, AuditMetadata auditMetadata, string? transactionId, int sequence)
    {
        var tableName = GetTableName(entry);
        var entityId = GetEntityId(entry);

        if (string.IsNullOrEmpty(entityId)) return null;

        var changeType = entry.State switch
        {
            EntityState.Added => ChangeType.Created,
            EntityState.Modified => ChangeType.Updated,
            EntityState.Deleted => ChangeType.Deleted,
            _ => throw new InvalidOperationException($"Unexpected entity state: {entry.State}")
        };

        var changeEvent = new AuditLogMessage
        {
            EventType = $"entity.{changeType.ToString().ToLowerInvariant()}",
            Metadata = auditMetadata,
            Data = new EntityChangeData
            {
                TableName = tableName,
                EntityId = entityId,
                ChangeType = changeType,
                TransactionId = transactionId,
                TransactionSequence = _config.GroupByTransaction ? sequence : null
            }
        };

        // Capture values based on configuration and change type
        CaptureEntityValues(entry, changeEvent.Data);

        // Capture relationships if enabled
        if (_config.CaptureRelationships)
        {
            CaptureEntityRelationships(entry, changeEvent.Data);
        }

        // Build subject with master-detail hierarchy for easy MongoDB querying
        // Format: {masterTable}/{masterId}/details/{detailTable}/{detailId} for details
        //     or: {tableName}/{entityId} for masters
        changeEvent.Subject = BuildSubject(tableName, entityId, changeEvent.Data);

        return changeEvent;
    }

    private string BuildSubject(string tableName, string entityId, EntityChangeData data)
    {
        // If this entity has a parent (master), include it in the subject for easy querying
        if (!string.IsNullOrEmpty(data.ParentTableName) && !string.IsNullOrEmpty(data.ParentEntityId))
        {
            // Detail record: {masterTable}/{masterId}/details/{detailTable}/{detailId}
            return $"{data.ParentTableName.ToLowerInvariant()}/{data.ParentEntityId}/details/{tableName.ToLowerInvariant()}/{entityId}";
        }

        // Master record: {tableName}/{entityId}
        return $"{tableName.ToLowerInvariant()}/{entityId}";
    }

    private void CaptureEntityValues(EntityEntry entry, EntityChangeData data)
    {
        switch (entry.State)
        {
            case EntityState.Added:
                if (_config.CaptureNewValues)
                {
                    data.NewValues = GetEntityValues(entry, PropertyValues.Current);
                }
                break;

            case EntityState.Modified:
                if (_config.CaptureOldValues)
                {
                    data.OldValues = GetEntityValues(entry, PropertyValues.Original);
                }
                if (_config.CaptureNewValues)
                {
                    data.NewValues = GetEntityValues(entry, PropertyValues.Current);
                }
                if (_config.CaptureChangedPropertiesOnly)
                {
                    data.ChangedProperties = GetChangedPropertyNames(entry);
                }
                break;

            case EntityState.Deleted:
                if (_config.CaptureOldValues)
                {
                    data.OldValues = GetEntityValues(entry, PropertyValues.Original);
                }
                break;
        }
    }

    private Dictionary<string, object?> GetEntityValues(EntityEntry entry, PropertyValues valueType)
    {
        var values = new Dictionary<string, object?>();

        foreach (var property in entry.Properties)
        {
            var propertyName = property.Metadata.Name;

            if (_config.ExcludedProperties.Contains(propertyName))
                continue;

            object? value = null;
            try
            {
                value = valueType switch
                {
                    PropertyValues.Original => property.OriginalValue,
                    PropertyValues.Current => property.CurrentValue,
                    _ => null
                };
            }
            catch (InvalidOperationException)
            {
                // OriginalValue might not be available (e.g., with AsNoTracking)
                // In this case, use CurrentValue as fallback for Original, or skip if Current
                if (valueType == PropertyValues.Original)
                {
                    _logger.LogDebug(
                        "OriginalValue not available for property {PropertyName}, using CurrentValue",
                        propertyName);
                    value = property.CurrentValue;
                }
            }

            values[propertyName] = SerializeValueIfNeeded(value);
        }

        return values;
    }

    private string[] GetChangedPropertyNames(EntityEntry entry)
    {
        var changedProperties = new List<string>();

        // Check if we need to load original values from database
        // This happens when entity was attached without tracking (AsNoTracking scenario)
        bool needsDatabaseValues = false;
        if (entry.State == EntityState.Modified)
        {
            // Check if we have real original values by testing if OriginalValue != CurrentValue for any property
            var modifiedProperties = entry.Properties.Where(p => p.IsModified).ToList();

            if (modifiedProperties.Any())
            {
                // If all modified properties have OriginalValue == CurrentValue, we likely don't have real original values
                var allEqual = modifiedProperties.All(p =>
                {
                    try
                    {
                        var orig = p.OriginalValue;
                        var curr = p.CurrentValue;
                        return (orig == null && curr == null) || (orig != null && orig.Equals(curr));
                    }
                    catch
                    {
                        return false;
                    }
                });

                needsDatabaseValues = allEqual;
            }
        }

        // If we need database values, try to get them
        Dictionary<string, object?>? databaseValues = null;
        if (needsDatabaseValues)
        {
            try
            {
                var dbValues = entry.GetDatabaseValues();
                if (dbValues != null)
                {
                    databaseValues = new Dictionary<string, object?>();
                    foreach (var prop in entry.Properties)
                    {
                        databaseValues[prop.Metadata.Name] = dbValues[prop.Metadata.Name];
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load database values for entity {EntityType}", entry.Entity.GetType().Name);
            }
        }

        foreach (var property in entry.Properties)
        {
            // Skip excluded properties
            if (_config.ExcludedProperties.Contains(property.Metadata.Name))
                continue;

            // Skip if not modified
            if (!property.IsModified)
                continue;

            try
            {
                object? originalValue;
                object? currentValue = property.CurrentValue;

                // Use database values if available, otherwise use tracked original value
                if (databaseValues != null && databaseValues.ContainsKey(property.Metadata.Name))
                {
                    originalValue = databaseValues[property.Metadata.Name];
                }
                else
                {
                    originalValue = property.OriginalValue;
                }

                // If both are null, no change
                if (originalValue == null && currentValue == null)
                    continue;

                // If one is null and the other isn't, it's a change
                if (originalValue == null || currentValue == null)
                {
                    changedProperties.Add(property.Metadata.Name);
                    continue;
                }

                // Compare values - if they're different, it's a change
                if (!originalValue.Equals(currentValue))
                {
                    changedProperties.Add(property.Metadata.Name);
                }
            }
            catch (InvalidOperationException)
            {
                // This can happen when OriginalValue is not available
                // If the property is marked as modified, include it as changed
                changedProperties.Add(property.Metadata.Name);
            }
        }

        return changedProperties.ToArray();
    }

    private object? SerializeValueIfNeeded(object? value)
    {
        if (value == null) return null;

        var type = value.GetType();
        if (IsSimpleType(type)) return value;

        try
        {
            return JsonConvert.SerializeObject(value);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to serialize value of type {Type}", type.Name);
            return value.ToString();
        }
    }

    private string GetTableName(EntityEntry entry) =>
        entry.Metadata.GetTableName() ?? entry.Entity.GetType().Name;

    private string GetEntityId(EntityEntry entry)
    {
        var key = entry.Metadata.FindPrimaryKey();
        if (key == null) return string.Empty;

        var keyValues = key.Properties
            .Select(p => entry.Property(p.Name).CurrentValue?.ToString())
            .Where(v => !string.IsNullOrEmpty(v));

        return string.Join("|", keyValues);
    }

    private static bool IsSimpleType(Type type) =>
        type.IsPrimitive || type.IsEnum || type == typeof(string) ||
        type == typeof(decimal) || type == typeof(DateTime) ||
        type == typeof(DateTimeOffset) || type == typeof(TimeSpan) ||
        type == typeof(Guid) || Nullable.GetUnderlyingType(type) != null;

    /// <summary>
    /// Captures foreign key relationships and navigation properties
    /// </summary>
    private void CaptureEntityRelationships(EntityEntry entry, EntityChangeData data)
    {
        try
        {
            var foreignKeys = entry.Metadata.GetForeignKeys();

            if (!foreignKeys.Any()) return;

            data.RelatedEntities = new Dictionary<string, string>();

            // Determine which FK is the master-detail relationship based on strategy
            IForeignKey? masterForeignKey = null;

            switch (_config.MasterDetailStrategy)
            {
                case MasterDetailStrategy.DeleteBehavior:
                    // Use cascade delete as indicator of master-detail
                    masterForeignKey = foreignKeys.FirstOrDefault(fk =>
                        fk.DeleteBehavior == DeleteBehavior.Cascade);
                    break;

                case MasterDetailStrategy.ExplicitConfiguration:
                    // Use explicit configuration
                    var entityType = entry.Entity.GetType();
                    if (_config.MasterDetailRelationships.TryGetValue(entityType, out var masterType))
                    {
                        masterForeignKey = foreignKeys.FirstOrDefault(fk =>
                            fk.PrincipalEntityType.ClrType == masterType);
                    }
                    break;

                case MasterDetailStrategy.FirstForeignKey:
                    // Original behavior - first FK is master
                    masterForeignKey = foreignKeys.FirstOrDefault();
                    break;

                case MasterDetailStrategy.None:
                    // Don't set master-detail relationship
                    masterForeignKey = null;
                    break;
            }

            foreach (var foreignKey in foreignKeys)
            {
                // Get the navigation property name (e.g., "Order" for OrderId FK)
                var navigationName = foreignKey.DependentToPrincipal?.Name
                                   ?? foreignKey.PrincipalToDependent?.Name
                                   ?? foreignKey.PrincipalEntityType.ClrType.Name;

                // Get the principal (parent) table name
                var principalTableName = foreignKey.PrincipalEntityType.GetTableName()
                                       ?? foreignKey.PrincipalEntityType.ClrType.Name;

                // Get the foreign key value(s)
                var fkValues = foreignKey.Properties
                    .Select(p => entry.Property(p.Name).CurrentValue?.ToString())
                    .Where(v => !string.IsNullOrEmpty(v))
                    .ToList();

                if (fkValues.Any())
                {
                    var relatedEntityId = string.Join("|", fkValues);
                    var relatedEntityIdentifier = $"{principalTableName}/{relatedEntityId}";

                    data.RelatedEntities[navigationName] = relatedEntityIdentifier;

                    // Set as parent only if this is the identified master FK
                    if (masterForeignKey != null &&
                        foreignKey == masterForeignKey &&
                        string.IsNullOrEmpty(data.ParentTableName) &&
                        string.IsNullOrEmpty(data.ParentEntityId))
                    {
                        data.ParentTableName = principalTableName;
                        data.ParentEntityId = relatedEntityId;
                    }
                }
            }

            // If no related entities found, clear the dictionary
            if (data.RelatedEntities.Count == 0)
            {
                data.RelatedEntities = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to capture relationships for entity {EntityType}",
                entry.Entity.GetType().Name);
            // Don't fail the entire audit if relationship capture fails
        }
    }

    private enum PropertyValues
    {
        Original,
        Current
    }
}
