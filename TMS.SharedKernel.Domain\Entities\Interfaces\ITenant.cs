namespace TMS.SharedKernel.Domain.Entities.Interfaces;

/// <summary>
/// Marker interface for entities that belong to a specific tenant (company).
/// Entities implementing this interface will have automatic tenant isolation applied via global query filters.
/// CompanyId is automatically populated and protected from modification.
///
/// <para><strong>Usage:</strong></para>
/// <para>Implement this interface on entities that need multi-tenant data isolation.</para>
///
/// <example>
/// <code>
/// // Tenant-scoped entity (customer data per company)
/// public class Vehicle : AuditableSoftDeleteEntity, ITenant
/// {
///     public Guid CompanyId { get; set; }  // Required by ITenant
///     public string LicensePlate { get; set; }
///     // ... other properties
/// }
///
/// // System-wide entity (shared across all tenants)
/// public class VehicleType : AuditableEntity
/// {
///     // No ITenant - shared by all companies
///     public string Name { get; set; }
///     public string Description { get; set; }
/// }
/// </code>
/// </example>
///
/// <para><strong>Automatic Features:</strong></para>
/// <list type="bullet">
/// <item>Global query filters automatically add WHERE CompanyId = @currentTenant</item>
/// <item>CompanyId is auto-populated on entity creation from current user context</item>
/// <item>CompanyId modification is prevented after creation (security)</item>
/// <item>Validation ensures CompanyId is never empty</item>
/// </list>
/// </summary>
public interface ITenant
{
    /// <summary>
    /// Gets or sets the Company ID (Tenant identifier) that owns this entity.
    /// This property is:
    /// - Automatically populated from the current tenant context when creating entities
    /// - Protected from modification after entity creation (enforced by BaseDbContext)
    /// - Used for automatic tenant isolation via global query filters
    /// </summary>
    Guid CompanyId { get; set; }
}
