﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Repositories\TodoRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\TMS.SharedKernel.Domain\TMS.SharedKernel.Domain.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernel.EntityFrameworkCore\TMS.SharedKernel.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\TMS.TruckService.Domain\TMS.TruckService.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Repositories\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DatabaseInitializer\Schemas01.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\SchemasDefault.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\SchemasQuartz.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
