{
  "ConnectionStrings": {
    "SharedKernalConnection": "Host=localhost;Database=SharedKernal;Username=********;Password=********"
  },
  "SharedKernelAuditLog": {
    "EnableEventPublishing": true,
    "CaptureOldValues": true,
    "CaptureNewValues": true,
    "CaptureChangedPropertiesOnly": true,
    "ExcludedTables": [
      "__EFMigrationsHistory",
      "code_sequence"
    ],
    "ExcludedProperties": [
      "CreatedAt",
      "CreatedBy",
      "UpdatedAt",
      "UpdatedBy",
      "RowVersion"
    ]
  },
  "KafkaAuditLog": {
    "BootstrapServers": "localhost:9092" // ************:9092
  },
  "Cors": {
    "AllowedOrigins": [
      "*"
    ],
    "AllowedMethods": [
      "GET",
      "POST",
      "PUT",
      "DELETE",
      "PATCH",
      "OPTIONS"
    ],
    "AllowedHeaders": [
      "*"
    ],
    "AllowCredentials": true
  },
  "DefaultKey": "<set>",
  "PrivacyHash": "c5700b2ff775a612cc8a0a8efff0e9cc",
  "Webhook": {
    "Key": "<set>"
  },
  "DriverService": {
    "Url": "https://localhost:7073"
  },
  "Identity": {
    "Issuer": "247-Eco-System",
    "Audience": "tms-api",
    "Key": "<set>"
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Information",
        "System": "Information",
        "Microsoft.EntityFrameworkCore.Database.Command": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Console",
              "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
            }
          ]
        }
      }
    ]
  },
  "environment": "development",
  "OpenTelemetry": {
    "OtlpEndpoint": "http://localhost:4317",
    "Key": "<set>"
  },
  "KafkaFlow": {
    "BootstrapServers": "localhost:9092",
    //"Security": {
    //  "EnableSsl": false,
    //  "Sasl": {
    //    "Enabled": false,
    //    "Mechanism": "PLAIN",
    //    "Username": "your-kafka-username",
    //    "Password": "your-kafka-password"
    //  },
    //  "Ssl": {
    //    "Protocol": "TLSv1.2",
    //    "CaLocation": "/path/to/ca-cert.pem",
    //    "CertificateLocation": "/path/to/client-cert.pem",
    //    "KeyLocation": "/path/to/client-key.pem",
    //    "KeyPassword": "client-key-password",
    //    "VerifyHostname": true
    //  }
    //},
    "Producer": {
      "MessageSendMaxRetries": 3,
      "MessageTimeoutMs": 5000,
      "EnableIdempotence": true,
      "CompressionType": "snappy",
      "BatchSize": 16384,
      "LingerMs": 5,
      "RequestTimeoutMs": 30000,
      "DeliveryTimeoutMs": 120000
    },
    "Consumer": {
      "AutoOffsetReset": "earliest",
      "EnableAutoCommit": false,
      "SessionTimeoutMs": 6000,
      "MaxPollIntervalMs": 300000,
      "MaxRetries": 3,
      "RetryDelayMs": 1000
    },
    "DeadLetter": {
      "TopicName": "dead-letter-queue",
      "ConsumerGroup": "dead-letter-group",
      "Enabled": true,
      "MaxRetries": 3
    }
  },
  "KafkaFlow02": {
    "BootstrapServers": "localhost:9092" // ************:9092
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "Password": "your_secure_password",
    "Database": 2,
    "ConnectTimeout": 10000,
    "SyncTimeout": 5000,
    "AsyncTimeout": 5000,
    "ConnectRetry": 5,
    "AbortOnConnectFail": false
  },
  "RabbitMq": {
    "HostName": "localhost",
    "Port": 5672,
    "UserName": "guest",
    "Password": "guest",
    "Exchanges": [
      {
        "Name": "company_events",
        "Type": "Topic",
        "Queues": {
          "orders": {
            "QueueName": "orders_queue",
            "RoutingKey": "event.order.*",
            "PrefetchCount": 5
          },
          "payments": {
            "QueueName": "payments_queue",
            "RoutingKey": "event.payment.*",
            "PrefetchCount": 10
          },
          "notifications": {
            "QueueName": "notifications_queue",
            "RoutingKey": "event.email.*",
            "PrefetchCount": 20
          },
          "audit": {
            "QueueName": "audit_queue",
            "RoutingKey": "event.*",
            "PrefetchCount": 1
          }
        }
      }
    ]
  }
}