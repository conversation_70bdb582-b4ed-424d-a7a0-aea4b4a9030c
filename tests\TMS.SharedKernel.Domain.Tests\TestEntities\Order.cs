using TMS.SharedKernel.Domain.Entities;

namespace TMS.SharedKernel.Domain.Tests.TestEntities;

/// <summary>
/// Test entity representing an Order (Master entity)
/// </summary>
public class Order : AuditableEntity
{
    public string OrderNumber { get; set; } = null!;
    public string CustomerName { get; set; } = null!;
    public decimal TotalAmount { get; set; }
    public string? Description { get; set; }

    // Navigation property
    public ICollection<OrderLine> OrderLines { get; set; } = new List<OrderLine>();
}
