﻿namespace TMS.TruckService.Application;

public class PermissionDefinition
{
    public const string TodoRead   = "Todo.Read";
    public const string TodoWrite  = "Todo.Write";
    public const string TodoDelete = "Todo.Delete";
    public const string TodoCreate = "pms.pricing.requestcalculation";
    public const string TodoUpdate = "Todo.Update";

    public const string NotificationPolicy = "NotificationPolicy";

   public static IEnumerable<string> AllPermissions => new List<string>
    {
        TodoRead,
        TodoWrite,
        TodoDelete,
        TodoCreate,
        TodoUpdate,
        NotificationPolicy
    };
}
