﻿using System.Text.Json;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.SharedKernal.RabbitMq.Configuration;

namespace TMS.SharedKernal.RabbitMq.Events;


public class RabbitMqHealthCheck : IHealthCheck
{
    private readonly RabbitMqOptions _options;

    public RabbitMqHealthCheck(IOptions<RabbitMqOptions> options)
    {
        _options = options.Value;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var factory = new ConnectionFactory()
            {
                HostName = _options.HostName,
                Port = _options.Port,
                UserName = _options.UserName,
                Password = _options.Password,
                VirtualHost = _options.VirtualHost
            };

            using var connection = factory.CreateConnection();
            using var channel = connection.CreateModel();

            var queueCount = _options.Exchange.Queues.Count;
            var data = new Dictionary<string, object>
            {
                ["server"] = $"{_options.HostName}:{_options.Port}",
                ["queues_configured"] = queueCount
            };

            return HealthCheckResult.Healthy("RabbitMQ is healthy", data);
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("RabbitMQ is unhealthy", ex);
        }
    }
}
