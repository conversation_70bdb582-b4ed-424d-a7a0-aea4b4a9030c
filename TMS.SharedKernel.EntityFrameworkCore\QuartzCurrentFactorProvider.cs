﻿using System.Security.Claims;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Constants;
using TMS.SharedKernel.Domain.Enums;
using TMS.SharedKernel.Domain.Extentions;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernal.SmoothRedis;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// QuartzCurrentFactorProvider
/// </summary>
public class QuartzCurrentFactorProvider : ICurrentFactorProvider
{
    private readonly IQuartzJobContextAccessor _contextAccessor;
    private readonly ISmoothRedis? _redis;

    // Backing fields for lazy initialization
    private IQuartzJobContext? _jobContext;
    private bool _jobContextInitialized;
    private string? _departmentCode;
    private bool _departmentCodeInitialized;

    /// <summary>
    /// QuartzCurrentFactorProvider
    /// </summary>
    /// <param name="contextAccessor"></param>
    /// <param name="redis"></param>
    public QuartzCurrentFactorProvider(IQuartzJobContextAccessor contextAccessor, ISmoothRedis? redis)
    {
        _contextAccessor = contextAccessor;
        _redis = redis;
    }

    private IQuartzJobContext? JobContext
    {
        get
        {
            if (_jobContextInitialized)
                return _jobContext;

            _jobContext = _contextAccessor.JobContext;
            _jobContextInitialized = true;
            return _jobContext;
        }
    }

    /// <inheritdoc/>
    public ClaimsPrincipal? CurrentUser => JobContext?.CurrentUser;

    /// <inheritdoc/>
    public Guid CurrentFactorId => JobContext?.CurrentFactorId ?? Guid.Empty;

    /// <inheritdoc/>
    public string CurrentFactorName => JobContext?.CurrentFactorName ?? string.Empty;

    /// <inheritdoc/>
    public Guid CompanyId => JobContext?.CompanyId ?? Guid.Empty;

    /// <inheritdoc/>
    public Guid DepartmentId => JobContext?.DepartmentId ?? Guid.Empty;

    /// <inheritdoc/>
    public string Roles => JobContext?.Roles ?? string.Empty;

    /// <inheritdoc/>
    public ClientType ClientType => JobContext?.ClientType ?? ClientType.Unknown;

    /// <inheritdoc/>
    public string DepartmentCode
    {
        get
        {
            if (_departmentCodeInitialized)
                return _departmentCode ?? String.Empty;

            if (_redis is null)
            {
                _departmentCode = String.Empty;
                _departmentCodeInitialized = true;
                return String.Empty;
            }

            // Skip if CompanyId or DepartmentId is empty
            if (CompanyId == Guid.Empty || DepartmentId == Guid.Empty)
            {
                _departmentCode = String.Empty;
                _departmentCodeInitialized = true;
                return String.Empty;
            }

            var cacheKey = $"metadata:{CompanyId}:{DepartmentId}";
            _departmentCode = _redis.Cache.GetAsync(cacheKey).ConfigureAwait(false).GetAwaiter().GetResult() ?? String.Empty;
            _departmentCodeInitialized = true;
            return _departmentCode;
        }
    }
}
