﻿namespace TMS.SharedKernel.Constants;

/// <summary>
/// Common messages shared across all bounded contexts and microservices
/// </summary>
public static class ValidationMessages
{
    // Basic validation
    public const string Required = "{0} is required.";
    public const string InvalidEmail = "Please enter a valid email address for {0}.";
    public const string InvalidPhoneNumber = "Please enter a valid phone number for {0}.";
    public const string InvalidUrl = "Please enter a valid URL for {0}.";

    // General validation
    public const string InvalidValue = "Invalid value for {0}: {1}";
    public const string InvalidFormat = "Invalid format for {0}.";
    public const string InvalidType = "{0} must be of type {1}.";

    // Length validation
    public const string MinLength = "{0} must be at least {1} characters long.";
    public const string MaxLength = "{0} must not exceed {1} characters.";
    public const string ExactLength = "{0} must be exactly {1} characters long.";

    // Range validation
    public const string Range = "{0} must be between {1} and {2}.";
    public const string MinValue = "{0} must be at least {1}.";
    public const string MaxValue = "{0} must not exceed {1}.";

    // Date validation
    public const string InvalidDate = "Please enter a valid date for {0}.";
    public const string FutureDate = "{0} must be a future date.";
    public const string PastDate = "{0} must be a past date.";
    public const string DateRange = "{0} must be between {1} and {2}.";

    // Collection validation
    public const string EmptyCollection = "{0} cannot be empty.";
    public const string MaxItems = "{0} cannot contain more than {1} items.";
    public const string MinItems = "{0} must contain at least {1} items.";

    // Business validation
    public const string MustBeUnique = "{0} must be unique.";
    public const string InvalidState = "{0} is in an invalid state.";
    public const string DependencyExists = "Cannot delete {0} because it has associated {1}.";
}
