﻿namespace TMS.SharedKernel.Domain.Provider.Interfaces;

/// <summary>
/// Interface overriding the <see cref="ICurrentFactorProvider"/>,
/// typically used in non-HTTP contexts where user information is not available
/// (e.g., background jobs, notification handlers, etc.).
/// </summary>
public interface IOverridableCurrentFactorProvider : ICurrentFactorProvider
{
    /// <summary>
    /// Sets the current factor ID for the given user.
    /// </summary>
    /// <param name="userId">The identifier of the user to associate with the current factor.</param>
    void SetFactorId(string userId);

    /// <summary>
    /// Clears the currently set factor ID.
    /// </summary>
    void Clear();
}
