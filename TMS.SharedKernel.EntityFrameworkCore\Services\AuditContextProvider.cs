﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using TMS.SharedKernel.Domain.Events.AuditLog;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.SharedKernel.EntityFrameworkCore.Services;

/// <summary>
/// AuditContextProvider
/// </summary>
public class AuditContextProvider
{
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IWebHostEnvironment _env;

    /// <summary>
    /// AuditContextProvider
    /// </summary>
    /// <param name="currentUserService"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="env"></param>
    public AuditContextProvider(
        ICurrentFactorProvider currentUserService,
        IHttpContextAccessor httpContextAccessor,
        IWebHostEnvironment env)
    {
        _currentFactorProvider = currentUserService;
        _httpContextAccessor = httpContextAccessor;
        _env = env;
    }

    /// <summary>
    /// GetCurrentAuditMetadata
    /// </summary>
    /// <returns></returns>
    public AuditMetadata GetCurrentAuditMetadata()
    {
        var httpContext = _httpContextAccessor.HttpContext;

        return new AuditMetadata
        {
            UserId = $"{_currentFactorProvider.CurrentFactorId}",
            UserName = _currentFactorProvider.CurrentFactorName,
            //SessionId = httpContext?.Session?.Id ?? "Not configuration.",
            CorrelationId = httpContext?.TraceIdentifier,
            IpAddress = httpContext?.Connection?.RemoteIpAddress?.ToString(),
            UserAgent = httpContext?.Request?.Headers["User-Agent"].FirstOrDefault(),
            Application = _env.ApplicationName,
            Environment = _env.EnvironmentName
        };
    }
}
