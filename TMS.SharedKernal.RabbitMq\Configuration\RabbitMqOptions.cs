﻿using System.ComponentModel.DataAnnotations;

namespace TMS.SharedKernal.RabbitMq.Configuration;

// === appsettings.json ===
/*
{
  "RabbitMq": {
    "HostName": "localhost",
    "Port": 5672,
    "UserName": "guest",
    "Password": "guest",
    "ExchangeName": "company_events",
    "ExchangeType": "Topic",
    "Queues": {
      "orders": {
        "QueueName": "orders_queue",
        "RoutingKey": "event.order.*",
        "PrefetchCount": 5
      },
      "payments": {
        "QueueName": "payments_queue",
        "RoutingKey": "event.payment.*",
        "PrefetchCount": 10
      },
      "notifications": {
        "QueueName": "notifications_queue",
        "RoutingKey": "event.email.*",
        "PrefetchCount": 20
      },
      "audit": {
        "QueueName": "audit_queue",
        "RoutingKey": "event.*",
        "PrefetchCount": 1
      }
    }
  }
}
*/
public class RabbitMQConfiguration
{
    public string HostName { get; set; } = "localhost";
    public int Port { get; set; } = 5672;
    public string UserName { get; set; } = "guest";
    public string Password { get; set; } = "guest";
    public string VirtualHost { get; set; } = "/";

    public int ConnectionRetryCount { get; set; } = 3;
    public TimeSpan ConnectionRetryDelay { get; set; } = TimeSpan.FromSeconds(5);

    public List<ExchangeConfiguration> Exchanges { get; set; } = new();
}

public class ExchangeConfiguration
{
    public string Name { get; set; } = "events_exchange";
    public ExchangeType Type { get; set; } = ExchangeType.Topic;
    public bool Durable { get; set; } = true;
    public bool AutoDelete { get; set; } = false;

    public Dictionary<string, QueueConfiguration> Queues { get; set; } = new();
}

public class RabbitMqOptions
{
    public string HostName { get; set; } = "localhost";
    public int Port { get; set; } = 5672;
    public string UserName { get; set; } = "guest";
    public string Password { get; set; } = "guest";
    public string VirtualHost { get; set; } = "/";
    
    public int ConnectionRetryCount { get; set; } = 3;
    public TimeSpan ConnectionRetryDelay { get; set; } = TimeSpan.FromSeconds(5);

    public ExchangeConfiguration Exchange { get; set; }
}

public enum ExchangeType
{
    Direct,
    Topic,
    Fanout,
    Headers
}

public class QueueConfiguration
{
    public string QueueName { get; set; } = string.Empty;
    public string RoutingKey { get; set; } = string.Empty;
    public bool Durable { get; set; } = true;
    public bool Exclusive { get; set; } = false;
    public bool AutoDelete { get; set; } = false;
    public int PrefetchCount { get; set; } = 1;
    public Dictionary<string, object> Arguments { get; set; } = new();
}
