﻿namespace TMS.SharedKernel.Constants;

/// <summary>
/// Common messages shared across all bounded contexts and microservices
/// </summary>
public static class LogMessages
{
    // Operation logging
    public const string OperationStarted = "Starting {OperationType} for {EntityType} {EntityId}";
    public const string OperationCompleted = "Completed {OperationType} for {EntityType} {EntityId} in {Duration}ms";
    public const string OperationFailed = "Failed {OperationType} for {EntityType} {EntityId}: {Error}";

    // Performance logging
    public const string SlowOperation = "Slow operation detected: {OperationType} took {Duration}ms (threshold: {Threshold}ms)";
    public const string HighMemoryUsage = "High memory usage detected: {MemoryUsage}MB (threshold: {Threshold}MB)";

    // Security logging
    public const string UnauthorizedAccess = "Unauthorized access attempt to {Resource} by {UserId}";
    public const string LoginAttempt = "Login attempt for {Email} from {IpAddress}";
    public const string LoginSuccess = "Successful login for {UserId} from {IpAddress}";
    public const string LoginFailure = "Failed login attempt for {Email} from {IpAddress}: {Reason}";

    // System logging
    public const string ServiceStarted = "{ServiceName} service started";
    public const string ServiceStopped = "{ServiceName} service stopped";
    public const string ConfigurationLoaded = "Configuration loaded for {ServiceName}";
    public const string HealthCheckFailed = "Health check failed for {ServiceName}: {Error}";
}
