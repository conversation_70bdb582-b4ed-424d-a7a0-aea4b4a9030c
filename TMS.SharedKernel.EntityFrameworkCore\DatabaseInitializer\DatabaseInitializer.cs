using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;
using Npgsql;
using TMS.SharedKernel.Domain.Services.Interfaces;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// DatabaseInitializer
/// </summary>
public class DatabaseInitializer : IDatabaseInitializer
{
    private readonly ILogger<DatabaseInitializer> _logger;

    /// <summary>
    /// DatabaseInitializer
    /// </summary>
    /// <param name="logger"></param>
    public DatabaseInitializer(ILogger<DatabaseInitializer> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get SQL script content
    /// </summary>
    public string GetScript(string folder = "DatabaseInitializer", string p = "Default")
    {
        return File.ReadAllText(Path.Combine(AppContext.BaseDirectory, folder, $"Schemas{p}.sql"));
    }

    /// <summary>
    /// Original method - creates individual transactions (use for backward compatibility only)
    /// </summary>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    public async Task EnsureSchemasAsync(string connectionString,
        string tableName,
        string indicator = "Default",
        Dictionary<string, string> parameters = null)
    {
        var scripts = this.GetScript(p: indicator);
        if (string.IsNullOrEmpty(scripts))
        {
            _logger.LogWarning("EnsureSchemasAsync[{TableName}] no scripts found for indicator {Indicator}, skipping", tableName, indicator);
              return;
        }

        // Replace placeholders with actual values
        scripts = ReplacePlaceholders(scripts, parameters);

        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync();

        using var transaction = await connection.BeginTransactionAsync();

        try
        {
            var lockId = tableName.GetHashCode();
            var lockCommand = new NpgsqlCommand(
                "SELECT pg_advisory_xact_lock(@lockId)", connection, transaction);
            lockCommand.Parameters.AddWithValue("@lockId", (long)lockId);
            await lockCommand.ExecuteNonQueryAsync();

            _logger.LogInformation("EnsureSchemasAsync[{TableName}] acquired lock, checking tables", tableName);

            var tableCount = await CheckTableExistsAsync(connection, transaction, tableName);
            if (tableCount > 0)
            {
                _logger.LogInformation("EnsureSchemasAsync[{TableName}] tables already exist, skipping", tableName);
                await transaction.CommitAsync();
                return;
            }

            _logger.LogInformation("EnsureSchemasAsync[{TableName}] creating tables...", tableName);

            var createCommand = new NpgsqlCommand(scripts, connection, transaction);
            await createCommand.ExecuteNonQueryAsync();

            await transaction.CommitAsync();
            _logger.LogInformation("EnsureSchemasAsync[{TableName}] tables created successfully", tableName);
        }
        catch (Exception ex)
        {
            _logger.LogWarning("EnsureSchemasAsync[{TableName}] failed: {Error}", tableName, ex.Message);
            await transaction.RollbackAsync();
        }
    }

    /// <summary>
    /// Execute all schema operations in a single atomic transaction
    /// </summary>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    public async Task EnsureAllDatabaseSchemasAsync(string connectionString,
        List<(string TableName, string Indicator)> schemaOperations,
        Dictionary<string, string> parameters = null,
        params Action[] additionalOperations)
    {
        const int maxRetries = 5;
        const int baseDelayMs = 500;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                using var connection = new NpgsqlConnection(connectionString);
                await connection.OpenAsync();

                using var transaction = await connection.BeginTransactionAsync();

                try
                {
                    // Single lock for ALL schema operations
                    var lockId = "schema_initialization".GetHashCode();
                    var lockCommand = new NpgsqlCommand(
                        "SELECT pg_advisory_xact_lock(@lockId)", connection, transaction);
                    lockCommand.Parameters.AddWithValue("@lockId", (long)lockId);
                    await lockCommand.ExecuteNonQueryAsync();

                    _logger.LogInformation("Acquired unified schema initialization lock (attempt {Attempt}/{MaxRetries})", attempt + 1, maxRetries);

                    // Execute additional operations first (like EnsureDatabaseCreated)
                    foreach (var operation in additionalOperations)
                    {
                        _logger.LogInformation("Executing additional database operation...");
                        operation();
                    }

                    // Execute schema operations
                    foreach (var (tableName, indicator) in schemaOperations)
                    {
                        var tabName = string.IsNullOrEmpty(tableName) ? $"Migration-{indicator}" : tableName;
                        await EnsureSchemaInTransactionAsync(connection, transaction, tabName, indicator, parameters);
                    }

                    await transaction.CommitAsync();
                    _logger.LogInformation("All database schema initialization completed successfully");
                    return; // Success - exit the retry loop
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Database schema initialization failed: {Error}", ex.Message);
                    try
                    {
                        await transaction.RollbackAsync();
                    }
                    catch (Exception rollbackEx)
                    {
                        _logger.LogWarning("Rollback failed (transaction may already be aborted): {Error}", rollbackEx.Message);
                    }
                    throw; // Re-throw to trigger retry
                }
            }
            catch (PostgresException pgEx) when (pgEx.SqlState == "P0001")
            {
                _logger.LogInformation("Migration already applied, database initialization complete");
                return; // Success - migration was already applied
            }
            catch (PostgresException pgEx) when (pgEx.SqlState == "XX000" && pgEx.Message.Contains("tuple concurrently updated"))
            {
                if (attempt < maxRetries - 1)
                {
                    var delay = baseDelayMs * (int)Math.Pow(2, attempt) + Random.Shared.Next(0, 200);
                    _logger.LogInformation("Concurrent update detected, retrying in {Delay}ms (attempt {Attempt}/{MaxRetries})", delay, attempt + 1, maxRetries);
                    await Task.Delay(delay);
                }
                else
                {
                    _logger.LogWarning("Database schema initialization failed after {MaxRetries} attempts due to concurrent updates. This is usually harmless if another instance completed the initialization.", maxRetries);
                    // Don't throw - allow the application to continue as another instance likely succeeded
                }
            }
            catch (Exception ex) when (attempt < maxRetries - 1)
            {
                var delay = baseDelayMs * (int)Math.Pow(2, attempt) + Random.Shared.Next(0, 200);
                _logger.LogWarning("Database operation failed: {Error}. Retrying in {Delay}ms (attempt {Attempt}/{MaxRetries})", ex.Message, delay, attempt + 1, maxRetries);
                await Task.Delay(delay);
            }
        }
    }

    /// <summary>
    /// Execute schema creation within existing transaction
    /// </summary>
    private async Task EnsureSchemaInTransactionAsync(NpgsqlConnection connection, NpgsqlTransaction transaction,
        string tableName, string indicator, Dictionary<string, string> parameters = null)
    {
        _logger.LogInformation("Processing schema for table: {TableName} with indicator: {Indicator}", tableName, indicator);

        var scripts = this.GetScript(p: indicator);
        if (string.IsNullOrEmpty(scripts))
        {
            _logger.LogWarning("No scripts found for indicator {Indicator}, skipping {TableName}", indicator, tableName);
            return;
        }

        // Replace placeholders with actual values
        scripts = ReplacePlaceholders(scripts, parameters);

        // Check if table exists
        var tableCount = await CheckTableExistsAsync(connection, transaction, tableName);
        if (tableCount > 0)
        {
            _logger.LogInformation("Table {TableName} already exists, skipping", tableName);
            return;
        }

        _logger.LogInformation("Creating tables for {TableName}...", tableName);

        try
        {
            var createCommand = new NpgsqlCommand(scripts, connection, transaction);
            await createCommand.ExecuteNonQueryAsync();
            _logger.LogInformation("Successfully created tables for {TableName}", tableName);
        }
        catch (PostgresException pgEx) when (pgEx.SqlState == "P0001")
        {
            // P0001 = raise_exception from SQL script (expected for already applied migrations)
            _logger.LogInformation("Migration for {TableName} already applied: {Message}", tableName, pgEx.MessageText);
        }
        catch (PostgresException pgEx) when (pgEx.SqlState == "42710")
        {
            // 42710 = duplicate_object (type, table, function already exists)
            _logger.LogInformation("Object for {TableName} already exists: {Message}", tableName, pgEx.MessageText);
        }
        catch (PostgresException pgEx) when (pgEx.SqlState == "42P07")
        {
            // 42P07 = duplicate_table
            _logger.LogInformation("Table {TableName} already exists: {Message}", tableName, pgEx.MessageText);
        }
        catch (PostgresException pgEx)
        {
            _logger.LogError("Failed to create tables for {TableName}: {Error}", tableName, pgEx.Message);
            throw; // Re-throw to abort transaction
        }
    }

    /// <summary>
    /// Replace placeholders in SQL script with actual values
    /// </summary>
    /// <param name="script">SQL script content</param>
    /// <param name="parameters">Dictionary of key-value pairs to replace</param>
    /// <returns>Script with replaced placeholders</returns>
    private string ReplacePlaceholders(string script, Dictionary<string, string> parameters)
    {
        if (parameters == null || parameters.Count == 0)
        {
            return script;
        }

        foreach (var param in parameters)
        {
            var placeholder = $"${{{param.Key}}}";
            script = script.Replace(placeholder, param.Value);
            _logger.LogInformation("Replaced placeholder {Placeholder} with value {Value}", placeholder, param.Value);
        }

        return script;
    }

    /// <summary>
    /// Check if table exists in database
    /// </summary>
    private static async Task<int> CheckTableExistsAsync(NpgsqlConnection connection, NpgsqlTransaction
transaction, string tableName)
    {
        var checkCommand = new NpgsqlCommand(@"
              SELECT COUNT(*)
              FROM information_schema.tables
              WHERE table_name = @tableName", connection, transaction);
        checkCommand.Parameters.AddWithValue("@tableName", tableName.ToLower());

        return Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
    }
}
