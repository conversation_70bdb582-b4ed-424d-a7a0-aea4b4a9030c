﻿using System.Linq.Expressions;
using TMS.SharedKernel.Domain;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// Factory class for creating fluent sorting chains.
/// </summary>
/// <typeparam name="T">The entity type to sort</typeparam>
/// <example>
/// var sorts = SortBuilder&lt;Product&gt;.Create()
///     .ThenBy(x => x.Category)
///     .ThenByDescending(x => x.Price)
///     .ThenBy(x => x.Name);
/// var results = query.ApplySorting(sorts);
/// </example>
public static class SortBuilder<T>
{
    /// <summary>
    /// Creates a new empty list of sort options for building fluent sorting chains.
    /// </summary>
    /// <returns>An empty list ready for fluent chaining with ThenBy/ThenByDescending</returns>
    public static List<ISortOption<T>> Create() => new();
}

/// <summary>
/// Extension methods for building fluent sorting chains.
/// Note: Despite the "Then" prefix, these methods can be used for both initial and subsequent sorts.
/// The actual OrderBy/ThenBy logic is handled by ApplySorting extension method.
/// </summary>
public static class SortBuilderExtensions
{
    /// <summary>
    /// Adds an ascending sort operation.
    /// Can be used as the first sort or subsequent sort - the ApplySorting method handles the distinction.
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <typeparam name="TKey">The property type to sort by</typeparam>
    /// <param name="sorts">The list of sort options</param>
    /// <param name="keySelector">Expression selecting the property to sort by</param>
    /// <returns>The sort list for fluent chaining</returns>
    public static List<ISortOption<T>> ThenBy<T, TKey>(
        this List<ISortOption<T>> sorts,
        Expression<Func<T, TKey>> keySelector)
    {
        sorts.Add(new SortOption<T, TKey>(keySelector, descending: false));
        return sorts;
    }

    /// <summary>
    /// Adds a descending sort operation.
    /// Can be used as the first sort or subsequent sort - the ApplySorting method handles the distinction.
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    /// <typeparam name="TKey">The property type to sort by</typeparam>
    /// <param name="sorts">The list of sort options</param>
    /// <param name="keySelector">Expression selecting the property to sort by</param>
    /// <returns>The sort list for fluent chaining</returns>
    public static List<ISortOption<T>> ThenByDescending<T, TKey>(
        this List<ISortOption<T>> sorts,
        Expression<Func<T, TKey>> keySelector)
    {
        sorts.Add(new SortOption<T, TKey>(keySelector, descending: true));
        return sorts;
    }
}
