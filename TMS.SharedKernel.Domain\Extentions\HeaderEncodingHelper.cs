﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json;

namespace TMS.SharedKernel.Domain.Extentions;

public static class HeaderEncodingHelper
{
    /// <summary>
    /// Base64 encode a string for HTTP headers (most common)
    /// </summary>
    public static string EncodeBase64(string input)
    {
        if (string.IsNullOrEmpty(input)) return string.Empty;

        var bytes = Encoding.UTF8.GetBytes(input);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Base64 decode a header value
    /// </summary>
    public static string DecodeBase64(string encodedInput)
    {
        if (string.IsNullOrEmpty(encodedInput)) return string.Empty;

        try
        {
            var bytes = Convert.FromBase64String(encodedInput);
            return Encoding.UTF8.GetString(bytes);
        }
        catch (FormatException)
        {
            throw new ArgumentException("Invalid Base64 string");
        }
    }

    /// <summary>
    /// URL encode for headers
    /// </summary>
    public static string EncodeUrl(string input)
    {
        if (string.IsNullOrEmpty(input)) return string.Empty;
        return HttpUtility.UrlEncode(input);
    }

    /// <summary>
    /// URL decode from headers
    /// </summary>
    public static string DecodeUrl(string encodedInput)
    {
        if (string.IsNullOrEmpty(encodedInput)) return string.Empty;
        return HttpUtility.UrlDecode(encodedInput);
    }

    /// <summary>
    /// Hex encode for headers
    /// </summary>
    public static string EncodeHex(string input)
    {
        if (string.IsNullOrEmpty(input)) return string.Empty;

        var bytes = Encoding.UTF8.GetBytes(input);
        return Convert.ToHexString(bytes);
    }

    /// <summary>
    /// Hex decode from headers
    /// </summary>
    public static string DecodeHex(string hexInput)
    {
        if (string.IsNullOrEmpty(hexInput)) return string.Empty;

        try
        {
            var bytes = Convert.FromHexString(hexInput);
            return Encoding.UTF8.GetString(bytes);
        }
        catch (FormatException)
        {
            throw new ArgumentException("Invalid hex string");
        }
    }

    /// <summary>
    /// JSON encode then Base64 (for complex objects)
    /// </summary>
    public static string EncodeJsonBase64<T>(T obj)
    {
        var json = JsonConvert.SerializeObject(obj);
        return EncodeBase64(json);
    }

    /// <summary>
    /// Base64 decode then JSON parse
    /// </summary>
    public static T? DecodeJsonBase64<T>(string encodedJson)
    {
        var json = DecodeBase64(encodedJson);
        return JsonConvert.DeserializeObject<T>(json);
    }
}
