﻿using Newtonsoft.Json;

namespace TMS.SharedKernal.RabbitMq.Serialization;

public interface IEventSerializer
{
    string Serialize<T>(T obj);
    T? Deserialize<T>(string json);
    object? Deserialize(string json, Type type);
}

public class JsonEventSerializer : IEventSerializer
{
    private readonly JsonSerializerSettings _options;

    public JsonEventSerializer()
    {
        _options = new JsonSerializerSettings
        {
            Formatting = Formatting.Indented,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };
    }

    public string Serialize<T>(T obj) => JsonConvert.SerializeObject(obj, _options);

    public T? Deserialize<T>(string json) => JsonConvert.DeserializeObject<T>(json, _options);

    public object? Deserialize(string json, Type type) => JsonConvert.DeserializeObject(json, type, _options);
}
