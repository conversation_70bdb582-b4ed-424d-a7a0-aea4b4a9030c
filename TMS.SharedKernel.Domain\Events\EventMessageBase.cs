﻿using TMS.SharedKernel.Domain.Events.Interfaces;

namespace TMS.SharedKernel.Domain.Events;

/// <inheritdoc />
[Obsolete("Use new EventMessageBase class in TMS.SharedKernel.MessageService package instead.")]
public abstract class EventMessageBase : IEventMessage<string>
{
    /// <inheritdoc />
    public string ItemId { get; set; } = string.Empty;

    /// <inheritdoc />
    public string Action { get; set; } = string.Empty;

    /// <inheritdoc />
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <inheritdoc />
    public string Version { get; set; } = "1.0";
}
