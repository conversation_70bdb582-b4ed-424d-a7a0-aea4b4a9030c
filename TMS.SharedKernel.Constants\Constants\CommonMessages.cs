﻿namespace TMS.SharedKernel.Constants;

/// <summary>
/// Common messages shared across all bounded contexts and microservices
/// </summary>
public static class CommonMessages
{
    // Generic CRUD messages - used across all domains
    public const string EntityNotFound = "{0} with {1} not found.";
    public const string EntityAlreadyExists = "{0} with {1} already exists.";
    public const string EntityCreated = "{0} created successfully.";
    public const string EntityUpdated = "{0} updated successfully.";
    public const string EntityDeleted = "{0} deleted successfully.";
    public const string EntityRetrieved = "{0} retrieved successfully.";

    // Common validation messages
    public const string Required = "{0} is required.";
    public const string InvalidFormat = "Invalid format for {0}.";
    public const string InvalidValue = "Invalid value for {0}: {1}";
    public const string MustBeUnique = "{0} must be unique.";
    public const string OutOfRange = "{0} must be between {1} and {2}.";

    // Operation results
    public const string OperationSuccessful = "{0} operation completed successfully.";
    public const string OperationFailed = "{0} operation failed: {1}";
    public const string ProcessingCompleted = "{0} processing completed.";
    public const string UnexpectedError = "An unexpected error occurred: {0}";
}

/// <summary>
/// Common error codes shared across all bounded contexts and microservices
/// </summary>
public static class CommonErrorCodes
{
    // Generic CRUD error codes
    public const string ENTITY_NOT_FOUND = "ENTITY_NOT_FOUND";
    public const string ENTITY_ALREADY_EXISTS = "ENTITY_ALREADY_EXISTS";
    public const string ENTITY_CREATION_FAILED = "ENTITY_CREATION_FAILED";
    public const string ENTITY_UPDATE_FAILED = "ENTITY_UPDATE_FAILED";
    public const string ENTITY_DELETION_FAILED = "ENTITY_DELETION_FAILED";

    // Common validation error codes
    public const string REQUIRED_FIELD_MISSING = "REQUIRED_FIELD_MISSING";
    public const string INVALID_FORMAT = "INVALID_FORMAT";
    public const string INVALID_VALUE = "INVALID_VALUE";
    public const string DUPLICATE_VALUE = "DUPLICATE_VALUE";
    public const string VALUE_OUT_OF_RANGE = "VALUE_OUT_OF_RANGE";

    // Business rule error codes
    public const string BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION";
    public const string INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS";
    public const string OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED";

    // Authentication and authorization error codes
    public const string UNAUTHORIZED = "UNAUTHORIZED";
    public const string FORBIDDEN = "FORBIDDEN";
    public const string TOKEN_EXPIRED = "TOKEN_EXPIRED";
    public const string TOKEN_INVALID = "TOKEN_INVALID";

    // System error codes
    public const string INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR";
    public const string EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR";
    public const string DATABASE_ERROR = "DATABASE_ERROR";
    public const string TIMEOUT_ERROR = "TIMEOUT_ERROR";

    // Domain-specific error codes (can be extended by specific domains)
    public const string DOMAIN_VALIDATION_FAILED = "DOMAIN_VALIDATION_FAILED";
    public const string ENTITY_VALIDATION_FAILED = "ENTITY_VALIDATION_FAILED";
    public const string AGGREGATE_VALIDATION_FAILED = "AGGREGATE_VALIDATION_FAILED";

    /// <summary>
    /// Thông tin bắt buộc
    /// </summary>
    public const string MS001 = "MS001";

    /// <summary>
    /// Thông tin đã tồn tại trong hệ thống
    /// </summary>
    public const string MS002 = "MS002";

    /// <summary>
    /// Phương tiện đã được sử dụng
    /// </summary>
    public const string MS003 = "MS003";

    /// <summary>
    /// Thông tin đăng nhập không đúng
    /// </summary>
    public const string MS004 = "MS004";

    /// <summary>
    /// Biển số xe này không tồn tại trong hệ thống
    /// </summary>
    public const string MS005 = "MS005";

    /// <summary>
    /// Bạn đang ghép với xe này
    /// </summary>
    public const string MS006 = "MS006";

    /// <summary>
    /// Vui lòng nhập đầy đủ thông tin
    /// </summary>
    public const string MS007 = "MS007";

    /// <summary>
    /// Ghép xe thất bại
    /// </summary>
    public const string MS008 = "MS008";

    /// <summary>
    /// Xe đang trong trạng thái không sẵn sàng. Không thể ghép
    /// </summary>
    public const string MS009 = "MS009";

    /// <summary>
    /// Bạn đã ghép xe khác. Vui lòng hủy ghép xe hiện tại trước khi ghép xe mới.
    /// </summary>
    public const string MS010 = "MS010";

    /// <summary>
    /// Hủy ghép xe thất bại vì lý do không đạt
    /// </summary>
    public const string MS011 = "MS011";

    /// <summary>
    /// Hủy ghép xe thất bại
    /// </summary>
    public const string MS012 = "MS012";

    /// <summary>
    /// Ghép xe thất bại vì lý do không đạt
    /// </summary>
    public const string MS013 = "MS013";

    /// <summary>
    /// Đổi tài thất bại
    /// </summary>
    public const string MS014 = "MS014";

    /// <summary>
    /// Xóa thành công
    /// </summary>
    public const string MS015 = "MS015";

    /// <summary>
    /// Thêm mới thành công
    /// </summary>
    public const string MS016 = "MS016";

    /// <summary>
    /// Chỉnh sửa thành công
    /// </summary>
    public const string MS017 = "MS017";

    /// <summary>
    /// Đã có {số lượng} đối tượng gán với loại danh mục này
    /// </summary>
    public const string MS018 = "MS018";

    /// <summary>
    /// Tệp tải lên chưa đúng định dạng
    /// </summary>
    public const string MS019 = "MS019";

    /// <summary>
    /// Import thành công
    /// </summary>
    public const string MS020 = "MS020";

    /// <summary>
    /// Ghép xe thành công
    /// </summary>
    public const string MS021 = "MS021";

    /// <summary>
    /// Hủy ghép xe thành công
    /// </summary>
    public const string MS022 = "MS022";

    /// <summary>
    /// Bạn đã chuyển sang trạng thái “Tài chờ”
    /// </summary>
    public const string MS023 = "MS023";

    /// <summary>
    /// Bạn đã chuyển sang trạng thái “Tài lái”
    /// </summary>
    public const string MS024 = "MS024";

    /// <summary>
    /// Những thay đổi do bạn thực hiện cho đến hiện tại sẽ không được lưu vào hệ thống
    /// </summary>
    public const string MS025 = "MS025";

    /// <summary>
    /// Những thay đổi do bạn thực hiện cho đến hiện tại sẽ không được lưu vào hệ thống
    /// </summary>
    public const string MS026 = "MS026";

    /// <summary>
    /// Bạn có chắc chắn muốn xóa danh mục này không? Hành động này không thể hoàn tác.
    /// </summary>
    public const string MS027 = "MS027";

    /// <summary>
    /// Khoảng cách vượt quá 500m so với xe {biển số xe}
    /// </summary>
    public const string MS028 = "MS028";

    /// <summary>
    /// Bạn hiện chưa vào ca. Vui lòng kiểm tra lại lịch làm việc
    /// </summary>
    public const string MS029 = "MS029";

    /// <summary>
    /// Bằng lái của bạn không phù hợp với loại xe này. Vui lòng kiểm tra lại.
    /// </summary>
    public const string MS030 = "MS030";

    /// <summary>
    /// Bằng lái của bạn đã hết hạn. Vui lòng gia hạn để tiếp tục chạy xe.
    /// </summary>
    public const string MS031 = "MS031";

    /// <summary>
    /// Hôm nay bạn đã lái quá {số tiếng}. Vui lòng nghỉ ngơi trước khi tiếp tục.
    /// </summary>
    public const string MS032 = "MS032";

    /// <summary>
    /// Tuần này anh/chị đã lái quá {số tiếng}. Hãy dành thời gian nghỉ ngơi để đảm bảo sức khoẻ.
    /// </summary>
    public const string MS033 = "MS033";

    /// <summary>
    /// Bạn cần đăng nhập hộp đen mới có thể tiến hành thao tác ghép xe
    /// </summary>
    public const string MS034 = "MS034";

    /// <summary>
    /// Bạn cần đăng xuất hộp đen mới có thể tiến hành thao tác đổi tài
    /// </summary>
    public const string MS035 = "MS035";

    /// <summary>
    /// Biển số xe đang thuộc đơn vị {tên đơn vị}. Vui lòng ghép xe với lý do bên dưới:
    /// Mượn tài xế
    /// Mượn xe
    /// </summary>
    public const string MS036 = "MS036";

    /// <summary>
    /// Tài xế {tên tài xế} đang ghép với xe này. Bạn sẽ ghép xe với vai trò:
    /// Tài lái
    /// Tài chờ
    /// </summary>
    public const string MS037 = "MS037";

    /// <summary>
    /// Tài xế {tên tài xế} muốn ghép vào xe. Bạn có muốn chuyển sang tài chờ.
    /// </summary>
    public const string MS038 = "MS038";

    /// <summary>
    /// Thay thế Tài xế {tên tài xế} 
    /// Thực hiện kế hoạch {mã+tên kế hoạch}
    /// </summary>
    public const string MS039 = "MS039";

    /// <summary>
    /// Bạn chưa kết thúc kế hoạch {mã+tên kế hoạch}. Vui lòng thực hiện trước khi hủy ghép xe.
    /// </summary>
    public const string MS040 = "MS040";

    /// <summary>
    /// Bạn chưa kết thúc kế hoạch {mã+tên kế hoạch}. Bạn có chắc chắn muốn hủy ghép xe
    /// </summary>
    public const string MS041 = "MS041";

    /// <summary>
    /// Ghép với xe {biển số xe}
    /// Tự động hủy ghép xe biển số {biển số xe}
    /// Tiếp tục kế hoạch {mã+tên kế hoạch}
    /// </summary>
    public const string MS042 = "MS042";

    /// <summary>
    /// Chọn tài chờ mà bạn muốn đổi:
    /// Tài chờ 1
    /// Tài chờ 2
    /// </summary>
    public const string MS043 = "MS043";

    /// <summary>
    /// Điền số công tơ mét hiện tại trước khi đổi tài: 
    /// Nhập ảnh
    /// Nhập số
    /// </summary>
    public const string MS044 = "MS044";

    /// <summary>
    /// Tài xế {tên tài xế} muốn đổi vị trí tài lái với bạn
    /// </summary>
    public const string MS045 = "MS045";

    /// <summary>
    /// Bạn có chắc chắn muốn đổi vị trí tài chờ với tài xế {tên tài xế}
    /// </summary>
    public const string MS046 = "MS046";

    /// <summary>
    /// Tài xế {tên tài xế} muốn đổi vị trí tài chờ với bạn
    /// </summary>
    public const string MS047 = "MS047";

    /// <summary>
    /// Gửi request thành công
    /// </summary>
    public const string MS048 = "MS048";

    /// <summary>
    /// Hủy yêu cầu thành công
    /// </summary>
    public const string MS049 = "MS049";

    /// <summary>
    /// Lý do ghép/hủy ghép xe không tìm thấy
    /// </summary>
    public const string MS050 = "MS050";

    /// <summary>
    /// Phương tiện chưa được ghép xe
    /// </summary>
    public const string MS051 = "MS051";

    /// <summary>
    /// Tải xuống thành công
    /// </summary>
    public const string MS052 = "MS052";

    /// <summary>
    /// Tải xuống thất bại
    /// </summary>
    public const string MS053 = "MS053";

    /// <summary>
    /// Danh mục chi phí không tồn tại hoặc đã vô hiệu, vui lòng kiểm tra lại
    /// </summary>
    public const string MS054 = "MS054";

    /// <summary>
    /// Ngày thực hiện giao dịch đã quá {số ngày} ngày so với ngày hiện tại, vui lòng kiểm tra lại
    /// </summary>
    public const string MS055 = "MS055";

    /// <summary>
    /// VAT không hợp lại, vui lòng kiểm tra lại.
    /// </summary>
    public const string MS056 = "MS056";

    /// <summary>
    /// Đối tác không tồn tại hoặc đã ngừng hoạt động, vui lòng kiểm tra lại
    /// </summary>
    public const string MS057 = "MS057";

    /// <summary>
    /// Ngày thực hiện giao dịch không đúng format, vui lòng kiểm tra lại
    /// </summary>
    public const string MS058 = "MS058";

    /// <summary>
    /// Cột thông tin không đúng format, vui lòng kiểm ta lại
    /// </summary>
    public const string MS059 = "MS059";

    /// <summary>
    /// Lỗi hệ thống, vui lòng liên hệ bộ phận hỗ trợ
    /// </summary>
    public const string MS060 = "MS060";

    /// <summary>
    /// Tổng tiền không hợp lệ, vui lòng kiểm tra lại
    /// </summary>
    public const string MS061 = "MS061";

    /// <summary>
    /// Biển số xe không có trong hệ thống, vui lòng kiểm tra lại
    /// </summary>
    public const string MS062 = "MS062";
}
