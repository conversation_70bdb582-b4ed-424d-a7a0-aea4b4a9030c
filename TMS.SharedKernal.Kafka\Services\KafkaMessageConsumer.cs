
using System.Diagnostics;
using Confluent.Kafka;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;
using TMS.SharedKernal.Kafka.Abstractions;  // MessageContext is here
using TMS.SharedKernal.Kafka.Configuration;

namespace TMS.SharedKernal.Kafka.Services;

public class KafkaMessageConsumer : BackgroundService, IMessageConsumer
{
    private static readonly ActivitySource ActivitySource = new("TMS.Kafka.Consumer");
    private static readonly TextMapPropagator Propagator = Propagators.DefaultTextMapPropagator;

    private readonly IServiceProvider _serviceProvider;
    private readonly IMessageSerializer _serializer;
    private readonly ILogger<KafkaMessageConsumer> _logger;
    private readonly KafkaFlowOptions _options;
    private readonly ITopicManager? _topicManager;
    private readonly Dictionary<string, IConsumer<string, string>> _consumers;
    private readonly Dictionary<string, TopicConfiguration> _topicConfigurations;
    private readonly string _serviceKey;

    public KafkaMessageConsumer(
        IServiceProvider serviceProvider,
        KafkaFlowOptions options,
        IMessageSerializer serializer,
        ILogger<KafkaMessageConsumer> logger,
        string serviceKey,
        ITopicManager? topicManager = null)
    {
        _serviceProvider = serviceProvider;
        _serializer = serializer;
        _options = options;
        _logger = logger;
        _topicManager = topicManager;
        _consumers = new Dictionary<string, IConsumer<string, string>>();
        _topicConfigurations = _options.Topics.ToDictionary(t => t.TopicName, t => t);
        _serviceKey = serviceKey;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Add startup delay to ensure web API starts first
        _logger.LogInformation($"{_serviceKey} consumer starting in 3 seconds...");
        await Task.Delay(3000, stoppingToken);

        if (_options.Topics == null || !_options.Topics.Any())
        {
            _logger.LogWarning("No Kafka topics configured. Consumer will not start.");
            return;
        }

        // Auto-create topics if topic manager is available and auto-creation is enabled
        if (_topicManager != null && _options.Topics.Any(t => t.AutoCreateTopic))
        {
            try
            {
                _logger.LogInformation("Auto-creating Kafka topics...");
                await _topicManager.CreateAllTopicsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to auto-create topics. Consumer will continue but may fail if topics don't exist.");
            }
        }

        _logger.LogInformation($"Starting {_serviceKey} consumers for {_options.Topics.Count} topics...");

        var tasks = _options.Topics.Select(topic =>
            StartConsumerForTopic(topic, stoppingToken)).ToArray();

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in one or more consumer tasks");
        }
    }

    private async Task StartConsumerForTopic(TopicConfiguration topicConfig, CancellationToken stoppingToken)
    {
        var consumerConfig = new ConsumerConfig
        {
            BootstrapServers = _options.BootstrapServers,
            GroupId = topicConfig.ConsumerGroup,
            AutoOffsetReset = Enum.Parse<AutoOffsetReset>(_options.Consumer.AutoOffsetReset, true),
            EnableAutoCommit = _options.Consumer.EnableAutoCommit,
            SessionTimeoutMs = _options.Consumer.SessionTimeoutMs,
            MaxPollIntervalMs = _options.Consumer.MaxPollIntervalMs,
            FetchMinBytes = _options.Consumer.FetchMinBytes,
            //FetchMaxWaitMs = _options.Consumer.FetchMaxWaitMs,
            // Add these critical timeout settings
            MetadataMaxAgeMs = 30000,
            SocketTimeoutMs = 10000,
            ApiVersionRequestTimeoutMs = 10000,
            //BrokerAddressTimeoutMs = 5000
        };

        // Apply security configuration
        ApplySecurityConfig(consumerConfig, _options.Security);

        var consumer = new ConsumerBuilder<string, string>(consumerConfig)
            .SetErrorHandler((_, e) => _logger.LogError($"Consumer error for {topicConfig.TopicName}: {e.Reason}"))
            .SetPartitionsAssignedHandler((c, partitions) =>
            {
                _logger.LogInformation($"[{topicConfig.TopicName}] Assigned partitions: [{string.Join(", ", partitions)}]");
            })
            .Build();

        _consumers[topicConfig.TopicName] = consumer;

        try
        {
            _logger.LogInformation($"Attempting to subscribe to topic: {topicConfig.TopicName}");
            consumer.Subscribe(topicConfig.TopicName);
            _logger.LogInformation($"Successfully subscribed to topic: {topicConfig.TopicName}");

            while (!stoppingToken.IsCancellationRequested)
            {
                ConsumeResult<string, string>? result = null;
                try
                {
                    // Add timeout to prevent indefinite blocking
                    result = consumer.Consume(TimeSpan.FromMilliseconds(1000));

                    // Check if result is null (timeout occurred)
                    if (result == null)
                    {
                        // No message received within timeout, continue loop
                        continue;
                    }

                    if (result.IsPartitionEOF)
                    {
                        _logger.LogInformation($"[{topicConfig.TopicName}] Reached end of partition {result.Partition}");
                        continue;
                    }

                    // ProcessMessage returns true if we should commit (success or sent to DLQ/retry)
                    var shouldCommit = await ProcessMessage(topicConfig, result, consumer, stoppingToken);

                    if (shouldCommit)
                    {
                        // Commit the offset - message was either processed successfully,
                        // sent to retry topic, or sent to DLQ
                        consumer.Commit(result);
                    }
                }
                catch (ConsumeException ex)
                {
                    _logger.LogError($"Consume error for {topicConfig.TopicName}: {ex.Error.Reason}");

                    // Add delay on consume errors to prevent tight error loops
                    await Task.Delay(1000, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation($"Consumer for {topicConfig.TopicName} was cancelled");
                    break;
                }
                catch (Exception ex)
                {
                    // This should only happen when DLQ is disabled and processing fails
                    _logger.LogError(ex, $"Error processing message from {topicConfig.TopicName}");

                    if (result != null)
                    {
                        _logger.LogCritical(
                            $"CRITICAL: Message processing failed and DLQ is disabled. " +
                            $"Consumer will be blocked at Topic: {topicConfig.TopicName}, " +
                            $"Partition: {result.Partition}, Offset: {result.Offset}. " +
                            $"Enable DeadLetter.Enabled=true to prevent this.");

                        // Don't commit - let Kafka redeliver
                        // This will cause the consumer to retry indefinitely until fixed
                    }

                    // Add delay to prevent tight error loops
                    await Task.Delay(5000, stoppingToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Fatal error in consumer for {topicConfig.TopicName}");
        }
        finally
        {
            try
            {
                consumer.Close();
                _logger.LogInformation($"Consumer for {topicConfig.TopicName} closed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error closing consumer for {topicConfig.TopicName}");
            }
        }
    }

    private static void ApplySecurityConfig(ClientConfig config, SecurityOptions security)
    {
        // Configure SASL Authentication
        if (security.Sasl.Enabled)
        {
            config.SecurityProtocol = security.EnableSsl ? SecurityProtocol.SaslSsl : SecurityProtocol.SaslPlaintext;
            config.SaslMechanism = Enum.Parse<SaslMechanism>(security.Sasl.Mechanism, true);
            config.SaslUsername = security.Sasl.Username;
            config.SaslPassword = security.Sasl.Password;
        }
        // Configure SSL only
        else if (security.EnableSsl)
        {
            config.SecurityProtocol = SecurityProtocol.Ssl;
        }

        // Configure SSL settings
        if (security.EnableSsl)
        {
            if (!string.IsNullOrEmpty(security.Ssl.CaLocation))
                config.SslCaLocation = security.Ssl.CaLocation;

            if (!string.IsNullOrEmpty(security.Ssl.CertificateLocation))
                config.SslCertificateLocation = security.Ssl.CertificateLocation;

            if (!string.IsNullOrEmpty(security.Ssl.KeyLocation))
                config.SslKeyLocation = security.Ssl.KeyLocation;

            if (!string.IsNullOrEmpty(security.Ssl.KeyPassword))
                config.SslKeyPassword = security.Ssl.KeyPassword;

            config.EnableSslCertificateVerification = security.Ssl.VerifyHostname;
        }
    }

    private async Task<bool> ProcessMessage(TopicConfiguration topicConfig, ConsumeResult<string, string> result, IConsumer<string, string> consumer, CancellationToken cancellationToken)
    {
        // Extract trace context from message headers
        var parentContext = Propagator.Extract(default, result.Message.Headers, ExtractTraceContext);
        Baggage.Current = parentContext.Baggage;

        // Start a new activity as a child of the extracted context with proper naming convention
        // Use extracted parent context, fallback to current activity, or create root span
        var activityContext = parentContext.ActivityContext != default
            ? parentContext.ActivityContext
            : Activity.Current?.Context ?? default;

        using var activity = activityContext != default
            ? ActivitySource.StartActivity($"{topicConfig.TopicName} process", ActivityKind.Consumer, activityContext)
            : ActivitySource.StartActivity($"{topicConfig.TopicName} process", ActivityKind.Consumer);

        // Log if activity is null (helps with debugging)
        if (activity == null)
        {
            _logger.LogWarning($"Failed to create activity for topic {topicConfig.TopicName}. ActivitySource might not be configured.");
        }

        using var scope = _serviceProvider.CreateScope();

        try
        {
            // Add OpenTelemetry messaging semantic convention tags (v1.21.0)
            // Reference: https://opentelemetry.io/docs/specs/semconv/messaging/kafka/
            activity?.SetTag("messaging.system", "kafka");
            activity?.SetTag("messaging.destination.name", topicConfig.TopicName);
            activity?.SetTag("messaging.destination.kind", "topic");
            activity?.SetTag("messaging.destination.partition.id", result.Partition.Value);
            activity?.SetTag("messaging.operation", "process");
            activity?.SetTag("messaging.message.id", result.Message.Headers?.FirstOrDefault(h => h.Key == "MessageId")?.GetValueBytes() != null
                ? System.Text.Encoding.UTF8.GetString(result.Message.Headers.First(h => h.Key == "MessageId").GetValueBytes())
                : result.Message.Key);
            activity?.SetTag("messaging.conversation_id", result.Message.Key);
            activity?.SetTag("messaging.message.payload_size_bytes", result.Message.Value?.Length ?? 0);
            activity?.SetTag("messaging.client_id", consumer.Name);
            activity?.SetTag("messaging.kafka.message.key", result.Message.Key);
            activity?.SetTag("messaging.kafka.source.partition", result.Partition.Value);
            activity?.SetTag("messaging.kafka.message.offset", result.Offset.Value);
            activity?.SetTag("messaging.kafka.consumer.group", topicConfig.ConsumerGroup);
            activity?.SetTag("net.peer.name", _options.BootstrapServers);

            // Add peer.service for service map visualization in Signoz
            activity?.SetTag("peer.service", "kafka");

            // Custom tags
            var messageType = result.Message.Headers?.FirstOrDefault(h => h.Key == "MessageType")?.GetValueBytes();
            if (messageType != null)
            {
                activity?.SetTag("messaging.message_type", System.Text.Encoding.UTF8.GetString(messageType));
            }

            var context = new MessageContext
            {
                Topic = result.Topic,
                Key = result.Message.Key,
                Partition = result.Partition.Value,
                Offset = result.Offset.Value,
                Timestamp = result.Message.Timestamp.UtcDateTime,
                Headers = result.Message.Headers?.ToDictionary(h => h.Key, h => System.Text.Encoding.UTF8.GetString(h.GetValueBytes())) ?? new()
            };

            var message = _serializer.Deserialize(result.Message.Value, topicConfig.MessageType);
            var handler = scope.ServiceProvider.GetRequiredService(topicConfig.HandlerType);

            var handleMethod = topicConfig.HandlerType.GetMethod("HandleAsync");
            await (Task)handleMethod.Invoke(handler, new[] { message, context, cancellationToken });

            _logger.LogInformation($"Successfully processed message from {topicConfig.TopicName} at offset {result.Offset}");
            return true; // Success
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("exception.type", ex.GetType().FullName);
            activity?.SetTag("exception.message", ex.Message);
            activity?.SetTag("exception.stacktrace", ex.StackTrace);

            var retryCount = GetRetryCount(result);

            _logger.LogError(ex,
                $"Failed to process message from {topicConfig.TopicName}. " +
                $"Partition: {result.Partition}, Offset: {result.Offset}, " +
                $"Key: {result.Message.Key}, RetryCount: {retryCount}/{_options.DeadLetter.MaxRetries}");

            if (_options.DeadLetter.Enabled)
            {
                if (retryCount >= _options.DeadLetter.MaxRetries)
                {
                    // Max retries exceeded - send to DLQ and acknowledge
                    await SendToDeadLetter(topicConfig.TopicName, result, ex, retryCount, scope);
                    _logger.LogWarning(
                        $"Message sent to DLQ after {retryCount} failed attempts. " +
                        $"Topic: {topicConfig.TopicName}, Offset: {result.Offset}");
                    return true; // Treated as success - commit the offset
                }
                else
                {
                    // Send to retry topic for reprocessing
                    await SendToRetryTopic(topicConfig.TopicName, result, retryCount, scope);
                    _logger.LogInformation(
                        $"Message sent to retry topic (attempt {retryCount + 1}/{_options.DeadLetter.MaxRetries}). " +
                        $"Topic: {topicConfig.TopicName}, Offset: {result.Offset}");
                    return true; // Commit the original message
                }
            }

            // DLQ not enabled - re-throw to prevent commit
            throw;
        }
    }

    private async Task SendToRetryTopic(
        string originalTopic,
        ConsumeResult<string, string> result,
        int currentRetryCount,
        IServiceScope scope)
    {
        try
        {
            var producer = scope.ServiceProvider.GetKeyedService<IMessageProducer>(_serviceKey);
            var retryTopicName = $"{originalTopic}.retry";

            // Create a new message with incremented retry count in headers
            var message = new Message<string, string>
            {
                Key = result.Message.Key,
                Value = result.Message.Value,
                Timestamp = result.Message.Timestamp,
                Headers = new Headers()
            };

            // Copy existing headers
            if (result.Message.Headers != null)
            {
                foreach (var header in result.Message.Headers)
                {
                    if (header.Key != "x-retry-count") // Skip old retry count
                    {
                        message.Headers.Add(header.Key, header.GetValueBytes());
                    }
                }
            }

            // Add incremented retry count
            message.Headers.Add("x-retry-count", BitConverter.GetBytes(currentRetryCount + 1));
            message.Headers.Add("x-original-topic", System.Text.Encoding.UTF8.GetBytes(originalTopic));
            message.Headers.Add("x-failed-at", System.Text.Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString("O")));

            // Calculate delay for exponential backoff
            var delayMs = CalculateRetryDelay(currentRetryCount);

            // For simplicity, we'll send immediately to retry topic
            // In production, you might want to use a delayed queue or scheduled consumer
            var producerConfig = new ProducerConfig { BootstrapServers = _options.BootstrapServers };
            using var retryProducer = new ProducerBuilder<string, string>(producerConfig).Build();
            await retryProducer.ProduceAsync(retryTopicName, message);

            _logger.LogInformation(
                $"Message sent to retry topic: {retryTopicName}, " +
                $"RetryCount: {currentRetryCount + 1}, NextDelay: {delayMs}ms");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                $"CRITICAL: Failed to send message to retry topic. " +
                $"Falling back to DLQ. Topic: {originalTopic}, Offset: {result.Offset}");

            // Fallback: send directly to DLQ
            await SendToDeadLetter(originalTopic, result, ex, currentRetryCount, scope);
        }
    }

    private async Task SendToDeadLetter(
        string originalTopic,
        ConsumeResult<string, string> result,
        Exception exception,
        int retryCount,
        IServiceScope scope)
    {
        try
        {
            var producer = scope.ServiceProvider.GetKeyedService<IMessageProducer>(_serviceKey);
            var deadLetterMessage = new DeadLetterMessage
            {
                OriginalTopic = originalTopic,
                OriginalMessage = result.Message.Value,
                Error = exception.ToString(), // Include full exception details
                RetryCount = retryCount,
                OriginalTimestamp = result.Message.Timestamp.UtcDateTime
            };

            await producer.ProduceAsync(_options.DeadLetter.TopicName, deadLetterMessage);

            _logger.LogInformation(
                $"Message sent to DLQ: Topic={originalTopic}, " +
                $"Partition={result.Partition}, Offset={result.Offset}, " +
                $"Key={result.Message.Key}, RetryCount={retryCount}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                $"CRITICAL: Failed to send message to dead letter queue. " +
                $"Original message may be lost. Topic: {originalTopic}, Offset: {result.Offset}");
            throw; // Re-throw to prevent offset commit
        }
    }

    private int GetRetryCount(ConsumeResult<string, string> result)
    {
        if (result.Message.Headers == null)
            return 0;

        var retryHeader = result.Message.Headers.FirstOrDefault(h => h.Key == "x-retry-count");
        if (retryHeader == null)
            return 0;

        var bytes = retryHeader.GetValueBytes();
        if (bytes == null || bytes.Length != 4)
            return 0;

        return BitConverter.ToInt32(bytes, 0);
    }

    private static IEnumerable<string> ExtractTraceContext(Headers? headers, string key)
    {
        if (headers == null)
        {
            return Enumerable.Empty<string>();
        }

        var header = headers.FirstOrDefault(h => h.Key == key);
        if (header == null)
        {
            return Enumerable.Empty<string>();
        }

        var bytes = header.GetValueBytes();
        if (bytes == null || bytes.Length == 0)
        {
            return Enumerable.Empty<string>();
        }

        return new[] { System.Text.Encoding.UTF8.GetString(bytes) };
    }

    private int CalculateRetryDelay(int retryCount)
    {
        // Exponential backoff: 1s, 2s, 4s, 8s, etc. with max of 30s
        var delayMs = Math.Min(1000 * Math.Pow(2, retryCount), 30000);
        return (int)delayMs;
    }

    public override void Dispose()
    {
        foreach (var consumer in _consumers.Values)
        {
            consumer?.Dispose();
        }
        base.Dispose();
    }
}
