﻿
using TMS.SharedKernal.Kafka.Abstractions;

namespace TMS.SharedKernal.Kafka.Configuration
{
    public class KafkaFlowOptions
    {
        public string BootstrapServers { get; set; } = "localhost:9092";
        public SecurityOptions Security { get; set; } = new();
        public ProducerOptions Producer { get; set; } = new();
        public ConsumerOptions Consumer { get; set; } = new();
        public List<TopicConfiguration> Topics { get; set; } = new();
        public DeadLetterOptions DeadLetter { get; set; } = new();
        public bool EnableMetrics { get; set; } = true;
    }

    public class SecurityOptions
    {
        public bool EnableSsl { get; set; } = false;
        public SaslOptions Sasl { get; set; } = new();
        public SslOptions Ssl { get; set; } = new();
    }

    public class SaslOptions
    {
        public bool Enabled { get; set; } = false;
        public string Mechanism { get; set; } = "PLAIN"; // PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI
        public string Username { get; set; }
        public string Password { get; set; }
    }

    public class SslOptions
    {
        public string Protocol { get; set; } = "TLSv1.2";
        public string CaLocation { get; set; }           // Path to CA certificate file
        public string CertificateLocation { get; set; }  // Path to client certificate file
        public string KeyLocation { get; set; }          // Path to client private key file
        public string KeyPassword { get; set; }          // Private key password
        public bool VerifyHostname { get; set; } = true;
    }

    public class ProducerOptions
    {
        public int MessageSendMaxRetries { get; set; } = 3;
        public int MessageTimeoutMs { get; set; } = 5000;
        public bool EnableIdempotence { get; set; } = true;
        public string CompressionType { get; set; } = "snappy";
        public int BatchSize { get; set; } = 16384;
        public int LingerMs { get; set; } = 5;
        public int RequestTimeoutMs { get; set; } = 30000;
        //public int DeliveryTimeoutMs { get; set; } = 120000;
    }

    public class ConsumerOptions
    {
        public string AutoOffsetReset { get; set; } = "earliest";
        public bool EnableAutoCommit { get; set; } = false;
        public int SessionTimeoutMs { get; set; } = 6000;
        public int MaxPollIntervalMs { get; set; } = 300000;
        public int FetchMinBytes { get; set; } = 1;
        //public int FetchMaxWaitMs { get; set; } = 500;
        public int MaxRetries { get; set; } = 3;
        public int RetryDelayMs { get; set; } = 1000;
    }

    public class TopicConfiguration : ITopicConfiguration
    {
        public string TopicName { get; set; }
        public string ConsumerGroup { get; set; }
        public Type MessageType { get; set; }
        public Type HandlerType { get; set; }
        public int Partitions { get; set; } = 1;
        public short ReplicationFactor { get; set; } = 1;
        public bool AutoCreateTopic { get; set; } = false;
    }

    public class DeadLetterOptions
    {
        public string TopicName { get; set; } = "dead-letter-queue";
        public string ConsumerGroup { get; set; } = "dead-letter-group";
        public bool Enabled { get; set; } = true;
        public int MaxRetries { get; set; } = 3;
    }
}
