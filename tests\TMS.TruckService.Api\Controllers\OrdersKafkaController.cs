﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.SharedKernal.SmoothRedis;
using TMS.TruckService.Application;
using TMS.TruckService.Contracts.Orders;
using TMS.TruckService.Domain.EventsKafka;

namespace TMS.TruckService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public class OrdersKafkaController : ControllerBase
{
    private readonly IEventPublisher _eventPublisher;
    private readonly IServiceProvider _services;
    private readonly ISmoothRedis _redis;
    private readonly IMessageProducer _kafkaMsgProducer;

    public OrdersKafkaController([FromKeyedServices(Constants.RabbitMqCompanyEvents)] IEventPublisher eventPublisher, 
        IServiceProvider services,
        ISmoothRedis redis,
        [FromKeyedServices(Constants.KafkaFlow)] IMessageProducer messageProducer)
    {
        _eventPublisher = eventPublisher;
        _services = services;
        _redis = redis;
        _kafkaMsgProducer = messageProducer;
    }

    [AllowAnonymous]
    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderRequest request)
    {
        await _redis.Cache.SetAsync("pairing:truckId:driverId", request, TimeSpan.FromMinutes(5));

        // Send order created event
        var orderEvent = new OrderCreatedEventKafka
        
        {
            OrderId = "order456",
            CustomerId = "user123",
            TotalAmount = 99.99m,
        };
        await _kafkaMsgProducer.ProduceAsync(orderEvent);

        return Ok(new { OrderId = orderEvent.OrderId });
    }
}

