﻿using TMS.SharedKernel.Domain;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// Extension methods for IQueryable to support advanced sorting operations.
/// </summary>
public static class QueryableExtensions
{
    /// <summary>
    /// Applies multiple sorting operations to a query in sequence.
    /// The first sort option uses OrderBy/OrderByDescending, subsequent sorts use ThenBy/ThenByDescending.
    /// </summary>
    /// <typeparam name="T">The entity type being queried</typeparam>
    /// <param name="query">The query to apply sorting to</param>
    /// <param name="sortOptions">Collection of sort options to apply in order. Build using SortBuilder with ThenBy/ThenByDescending methods.</param>
    /// <returns>The query with all sorting operations applied, or the original query if no sort options provided</returns>
    /// <example>
    /// var sorts = SortBuilder&lt;Product&gt;.Create()
    ///     .ThenBy(x => x.Category)
    ///     .ThenByDescending(x => x.Price)
    ///     .ThenBy(x => x.Name);
    /// var sortedQuery = query.ApplySorting(sorts);
    /// </example>
    public static IQueryable<T> ApplySorting<T>(this IQueryable<T> query, IEnumerable<ISortOption<T>> sortOptions)
    {
        if (sortOptions == null || !sortOptions.Any())
            return query;

        return sortOptions.Aggregate(
            (query: (IQueryable<T>)query, isFirst: true),
            (acc, sortOption) => (
                sortOption.Apply(acc.query, acc.isFirst),
                false
            )
        ).query;
    }
}
