﻿namespace TMS.SharedKernel.Constants.Constants;

public record SystemInfo(Guid Id, string Code, string Name);

public static class SystemConstants
{
    [Obsolete("Using System for new one.")]
    public static readonly Guid CreatedBySystem = Guid.Parse("00000000-0000-0000-0000-000000000001");

    // System Definitions
    public static readonly SystemInfo System = new(Guid.Parse("00000000-0000-0000-0000-000000000001"), "S01", "System");
    public static readonly SystemInfo Pms = new(Guid.Parse("00000000-0000-0000-0000-000000000002"), "S02", "PMS");
    public static readonly SystemInfo Hrm = new(Guid.Parse("00000000-0000-0000-0000-000000000003"), "S03", "HRM");
    public static readonly SystemInfo Crm = new(Guid.Parse("00000000-0000-0000-0000-000000000004"), "S04", "CRM");

    /// <summary>
    /// First tenant supported company at version v0
    /// for default data seeding
    /// </summary>
    public static readonly SystemInfo DefaultCompany = new(Guid.Parse("01000000-**************-000000000001")
        , "", "");

    // All Systems Collection
    public static readonly IReadOnlyList<SystemInfo> All = new[] { System, Pms, Hrm, Crm };
}
