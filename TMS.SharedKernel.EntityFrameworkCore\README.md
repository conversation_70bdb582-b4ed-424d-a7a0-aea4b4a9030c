# TMS.SharedKernel.EntityFrameworkCore

Entity Framework Core infrastructure components for TMS SharedKernel, providing a complete data access layer with audit tracking, domain events, and repository patterns.

## Features

- **BaseDbContext**: Custom DbContext with audit tracking and domain event dispatching
- **Generic Repository Pattern**: Complete CRUD operations with async support
- **Unit of Work Pattern**: Transaction management with execution strategies
- **Paged Results**: Built-in pagination support
- **Specification Pattern**: Query specification support
- **Audit Tracking**: Automatic created/updated date and user tracking
- **Domain Events**: Automatic domain event dispatching on SaveChanges
- **UTC DateTime Conversion**: Automatic UTC conversion for all DateTime properties

## Core Components

### BaseDbContext

```csharp
public class YourDbContext : BaseDbContext
{
    public YourDbContext(DbContextOptions<YourDbContext> options, ICurrentFactorProvider factorProvider) 
        : base(options, factorProvider)
    {
    }

    public DbSet<Product> Products { get; set; }
    public DbSet<Order> Orders { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Your entity configurations
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(FieldLengths.MediumText);
        });

        base.OnModelCreating(modelBuilder);
    }
}
```

### Repository Pattern

```csharp
// Basic repository usage
public class ProductService
{
    private readonly IBaseRepository<Product> _productRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ProductService(IBaseRepository<Product> productRepository, IUnitOfWork unitOfWork)
    {
        _productRepository = productRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Product?> GetProductAsync(int id)
    {
        return await _productRepository.GetByIdAsync(id);
    }

    public async Task<PagedResult<Product>> GetProductsAsync(int page, int pageSize)
    {
        return await _productRepository.GetPagedAsync(
            page, 
            pageSize, 
            predicate: p => p.IsActive,
            orderBy: p => p.Name);
    }

    public async Task CreateProductAsync(Product product)
    {
        await _productRepository.AddAsync(product);
        await _unitOfWork.SaveChangesAsync();
    }

    public async Task UpdateProductAsync(Product product)
    {
        _productRepository.Update(product);
        await _unitOfWork.SaveChangesAsync();
    }
}
```

### Unit of Work with Transactions

```csharp
public class OrderService
{
    private readonly IBaseRepository<Order> _orderRepository;
    private readonly IBaseRepository<Product> _productRepository;
    private readonly IUnitOfWork _unitOfWork;

    public async Task ProcessOrderAsync(CreateOrderRequest request)
    {
        await _unitOfWork.ExecuteTransactionAsync(async () =>
        {
            // Create order
            var order = new Order(request.CustomerId, request.Items);
            await _orderRepository.AddAsync(order);

            // Update inventory
            foreach (var item in request.Items)
            {
                var product = await _productRepository.GetByIdAsync(item.ProductId);
                product.ReduceInventory(item.Quantity);
                _productRepository.Update(product);
            }

            // SaveChanges is called automatically within the transaction
        });
    }
}
```

### Custom Repository

```csharp
public interface IProductRepository : IBaseRepository<Product>
{
    Task<IReadOnlyList<Product>> GetActiveProductsAsync(CancellationToken cancellationToken = default);
    Task<Product?> GetBySkuAsync(string sku, CancellationToken cancellationToken = default);
}

public class ProductRepository : BaseRepository<Product>, IProductRepository
{
    public ProductRepository(BaseDbContext context) : base(context)
    {
    }

    public async Task<IReadOnlyList<Product>> GetActiveProductsAsync(CancellationToken cancellationToken = default)
    {
        return await FindAsync(p => p.IsActive, cancellationToken);
    }

    public async Task<Product?> GetBySkuAsync(string sku, CancellationToken cancellationToken = default)
    {
        return await FirstOrDefaultAsync(p => p.Sku == sku, cancellationToken);
    }
}
```

## Repository Interface Methods

### Read Operations
- `GetByIdAsync<TKey>(TKey id)` - Get entity by ID
- `GetAllAsync()` - Get all entities
- `FindAsync(Expression<Func<T, bool>> predicate)` - Find entities by predicate
- `FirstOrDefaultAsync(Expression<Func<T, bool>> predicate)` - Get first matching entity
- `ExistsAsync(Expression<Func<T, bool>> predicate)` - Check if entity exists
- `CountAsync(Expression<Func<T, bool>>? predicate)` - Count entities

### Paging
- `GetPagedAsync(int page, int pageSize, predicate, orderBy, ascending)` - Get paged results

### Include Operations
- `FindWithIncludeAsync(predicate, params includes[])` - Find with related data

### Write Operations
- `AddAsync(TEntity entity)` - Add single entity
- `AddRangeAsync(IEnumerable<TEntity> entities)` - Add multiple entities
- `Update(TEntity entity)` - Update entity
- `UpdateRange(IEnumerable<TEntity> entities)` - Update multiple entities
- `Remove(TEntity entity)` - Remove entity
- `RemoveRange(IEnumerable<TEntity> entities)` - Remove multiple entities
- `RemoveByIdAsync(object id)` - Remove by ID

## Unit of Work Interface Methods

### Transaction Management
- `BeginTransactionAsync()` - Begin new transaction
- `BeginTransactionWithContextAsync()` - Begin transaction with context
- `CommitTransactionAsync()` - Commit current transaction
- `RollbackTransactionAsync()` - Rollback current transaction

### Execution Strategies
- `ExecuteTransactionAsync(Action operation)` - Execute with retry strategy
- `ExecuteTransactionAsync<TOutput>(Func<Task<TOutput>> operation)` - Execute with result
- `ExecuteTransactionAsync(Func<Task> operation)` - Execute async operation

### Save Changes
- `SaveChangesAsync()` - Save all changes

## Setup and Configuration

### 1. Install Package
```bash
dotnet add package TMS.SharedKernel.EntityFrameworkCore
```

### 2. Register Services
```csharp
// In Program.cs or Startup.cs
builder.Services.AddDbContext<YourDbContext>(options =>
    options.UseSqlServer(connectionString));

// Register repositories and unit of work
builder.Services.AddRepositories(); // From TMS.SharedKernel.Utilities

// Register current factor provider
builder.Services.AddScoped<ICurrentFactorProvider, HttpCurrentFactorProvider>();
```

### 3. Configure Entity Mappings
```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    // Entity configurations are automatically applied from:
    // - Current assembly (where DbContext is defined)
    // - Calling assembly
    
    base.OnModelCreating(modelBuilder);
}
```

## Automatic Features

### Audit Tracking
Entities implementing audit interfaces get automatic tracking:
```csharp
public class Product : AuditableEntity
{
    public string Name { get; set; }
    // CreatedDate, UpdatedDate, CreatedBy, UpdatedBy are set automatically
}
```

### Domain Events
Domain events are automatically dispatched on `SaveChanges`:
```csharp
public class Order : AuditableEntity
{
    public void CompleteOrder()
    {
        Status = OrderStatus.Completed;
        RegisterDomainEvent(new OrderCompletedEvent(Id)); // Dispatched automatically
    }
}
```

### UTC DateTime Conversion
All `DateTime` properties are automatically converted to UTC:
```csharp
// No manual UTC conversion needed
entity.CreatedDate = DateTime.Now; // Automatically converted to UTC
```

## Paged Results

```csharp
var pagedProducts = await _productRepository.GetPagedAsync(
    page: 1,
    pageSize: 10,
    predicate: p => p.Category == "Electronics",
    orderBy: p => p.Name,
    ascending: true);

Console.WriteLine($"Total: {pagedProducts.TotalCount}");
Console.WriteLine($"Pages: {pagedProducts.PageCount}");
Console.WriteLine($"Current Page: {pagedProducts.CurrentPage}");

foreach (var product in pagedProducts.Items)
{
    Console.WriteLine(product.Name);
}
```

## Best Practices

1. **Repository Pattern**: Use repositories for data access abstraction
2. **Unit of Work**: Use for transaction management and consistency
3. **Async Operations**: Always use async methods for database operations
4. **Paging**: Use built-in paging for large result sets
5. **Specifications**: Use specification pattern for complex queries
6. **Domain Events**: Leverage automatic domain event dispatching
7. **Audit Tracking**: Use audit interfaces for compliance requirements

## Dependencies

- **Microsoft.EntityFrameworkCore** - Core EF functionality
- **Microsoft.EntityFrameworkCore.Relational** - Relational database support
- **Microsoft.Data.SqlClient** - SQL Server provider
- **AutoMapper** - Object mapping
- **TMS.SharedKernel.Domain** - Core contracts and interfaces
- **MediatR** - Domain event handling

## Schema Configuration

The default schema is set to `TMSA01`. Override in your DbContext if needed:
```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    modelBuilder.HasDefaultSchema("YourSchema");
    base.OnModelCreating(modelBuilder);
}
```