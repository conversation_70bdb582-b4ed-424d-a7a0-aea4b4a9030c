# SharedKernel Publishing Options

You now have **two publishing options** for each SharedKernel library:

## Option 1: Simple Publish (Original)

**Files:** `Publish_TMS.SharedKer*.bat`

**What it does:**
- Clean the project
- Build in Release mode
- Pack NuGet package
- **NO obfuscation** - code remains readable

**Use when:**
- ✅ Publishing internally
- ✅ Debugging issues
- ✅ Development/testing
- ✅ Quick publish without extra processing

**Example:**
```batch
Publish_TMS.SharedKernal.Caching.bat
```

## Option 2: Obfuscated Publish

**Files:** `Publish_TMS.SharedKer*.obfuscar.bat`

**What it does:**
- Clean and build the project
- **Obfuscate method bodies** (strings encrypted, control flow scrambled)
- Pack NuGet package with obfuscated DLL
- Backup original DLL with `.original` extension

**Use when:**
- ✅ Publishing to production
- ✅ Publishing externally
- ✅ Protecting intellectual property
- ✅ Hiding implementation details

**Example:**
```batch
Publish_TMS.SharedKernal.Caching.obfuscar.bat
```

## Available Libraries

| Library | Simple | Obfuscated |
|---------|--------|------------|
| **Caching** | `Publish_TMS.SharedKernal.Caching.bat` | `Publish_TMS.SharedKernal.Caching.obfuscar.bat` |
| **Kafka** | `Publish_TMS.SharedKernal.Kafka.bat` | `Publish_TMS.SharedKernal.Kafka.obfuscar.bat` |
| **RabbitMq** | `Publish_TMS.SharedKernal.RabbitMq.bat` | `Publish_TMS.SharedKernal.RabbitMq.obfuscar.bat` |
| **SmoothRedis** | `Publish_TMS.SharedKernal.SmoothRedis.bat` | `Publish_TMS.SharedKernal.SmoothRedis.obfuscar.bat` |
| **Authentication** | `Publish_TMS.SharedKernel.Authentication.bat` | `Publish_TMS.SharedKernel.Authentication.obfuscar.bat` |
| **BaseGrpc** | `Publish_TMS.SharedKernel.BaseGrpc.bat` | `Publish_TMS.SharedKernel.BaseGrpc.obfuscar.bat` |
| **Constants** | `Publish_TMS.SharedKernel.Constants.bat` | `Publish_TMS.SharedKernel.Constants.obfuscar.bat` |
| **Domain** | `Publish_TMS.SharedKernel.Domain.bat` | `Publish_TMS.SharedKernel.Domain.obfuscar.bat` |
| **EntityFrameworkCore** | `Publish_TMS.SharedKernel.EntityFrameworkCore.bat` | `Publish_TMS.SharedKernel.EntityFrameworkCore.obfuscar.bat` |
| **Utilities** | `Publish_TMS.SharedKernel.Utilities.bat` | `Publish_TMS.SharedKernel.Utilities.obfuscar.bat` |

## Obfuscation Details

### What Gets Obfuscated:
- ✅ String literals (encrypted)
- ✅ Method bodies (control flow scrambled)
- ✅ Constants (hidden)

### What Stays Unchanged (100% Backward Compatible):
- ✅ Class names
- ✅ Method names and signatures
- ✅ Property names
- ✅ Field names
- ✅ Parameter names

**Result:** Obfuscated packages are **100% backward compatible** - consumers can upgrade without any code changes!

## Recommendations

### For Internal Development
Use **simple publish** (faster, easier to debug):
```batch
Publish_TMS.SharedKernal.Caching.bat
```

### For Production/External Release
Use **obfuscated publish** (protected implementation):
```batch
Publish_TMS.SharedKernal.Caching.obfuscar.bat
```

## Performance

| Aspect | Simple | Obfuscated |
|--------|--------|------------|
| Build time | Fast | +5-10 seconds |
| Package size | Normal | Same or +1-2% |
| Runtime performance | 100% | 98-95% (minimal impact) |
| Debugging | Easy | Slightly harder (but stack traces work) |
| Compatibility | 100% | 100% |

## More Information

- **Obfuscar Guide:** See `OBFUSCAR_GUIDE.md`
- **What Gets Obfuscated:** See `WHAT_GETS_OBFUSCATED.md`
- **Verification:** Run `Verify_TMS.SharedKernal.Caching.bat` (optional)

## Quick Reference

```bash
# Simple publish (no obfuscation)
Publish_TMS.SharedKernal.Caching.bat

# Obfuscated publish (protected)
Publish_TMS.SharedKernal.Caching.obfuscar.bat

# Verify compatibility (optional)
Verify_TMS.SharedKernal.Caching.bat
```
