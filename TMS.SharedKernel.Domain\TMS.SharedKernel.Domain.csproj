﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.5.0</Version>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Repository\QueryableExtensions.cs" />
    <Compile Remove="Repository\SortBuilder.cs" />
    <Compile Remove="Repository\SortOption.cs" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.SharedKernal.Kafka\TMS.SharedKernal.Kafka.csproj" />
    <ProjectReference Include="..\TMS.SharedKernal.RabbitMq\TMS.SharedKernal.RabbitMq.csproj" />
  </ItemGroup>

</Project>