﻿using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;
using TMS.TruckService.Domain.Entities;

namespace TMS.TruckService.Infra.Data;

public class ApplicationDbContext : BaseDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, ICurrentFactorProvider currentFactorProvider) : 
        base(options, currentFactorProvider)
    {
    }

    public DbSet<Todo> Todos { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
    }
}
