﻿using System.Security.Claims;
using TMS.SharedKernel.Domain.Enums;

namespace TMS.SharedKernel.Domain;

public interface IQuartzJobContext
{
    ClaimsPrincipal? CurrentUser { get; }
    Guid CurrentFactorId { get; }
    string CurrentFactorName { get; }
    Guid CompanyId { get; }
    Guid DepartmentId { get; }
    string Roles { get; }
    ClientType ClientType { get; }
}

public interface IQuartzJobContextAccessor
{
    IQuartzJobContext? JobContext { get; set; }
}
