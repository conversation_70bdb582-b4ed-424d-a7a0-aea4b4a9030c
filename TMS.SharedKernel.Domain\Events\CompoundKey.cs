﻿namespace TMS.SharedKernel.Domain.Events;

/// <summary>
/// Represents a compound key to be used in distribute system message
/// </summary>
/// <typeparam name="T"></typeparam>
public class CompoundKey<T> where T : class, new()
{
    private const string Delimiter = "|";
    private readonly string[] _keys;

    /// <summary>
    /// Create a new instance of <see cref="CompoundKey{T}"/>
    /// </summary>
    /// <param name="keys"></param>
    protected CompoundKey(params string[] keys)
    {
        _keys = keys;
    }


    /// <summary>
    /// Override default ToString method with <c>Join</c> <c>keys</c> with <c>delimiter</c>
    /// </summary>
    /// <returns></returns>
    public override string ToString() => string.Join(Delimiter, _keys);

    /// <summary>
    /// Create a new instance of <see cref="CompoundKey{T}"/> from keys
    /// </summary>
    /// <param name="keys"></param>
    /// <returns></returns>
    public static string FromKeys(params string[] keys) => new CompoundKey<T>(keys).ToString();

    /// <summary>
    /// Create a new instance of <see cref="CompoundKey{T}"/> from string
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static T FromString(string value)
    {
        var values = value.Split(Delimiter);

        if (values.Any(string.IsNullOrWhiteSpace))
            throw new ArgumentException($"{nameof(CompoundKey<T>)} is not in the correct format.");

        return Activator.CreateInstance(typeof(T), values) as T ?? new T();
    }
}
