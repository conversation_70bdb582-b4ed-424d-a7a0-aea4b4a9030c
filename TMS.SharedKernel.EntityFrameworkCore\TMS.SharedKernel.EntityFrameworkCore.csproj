<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.7.0</Version>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.SharedKernel.Domain\TMS.SharedKernel.Domain.csproj" />
    <ProjectReference Include="..\TMS.SharedKernal.SmoothRedis\TMS.SharedKernal.SmoothRedis.csproj" />
  </ItemGroup>

</Project>
