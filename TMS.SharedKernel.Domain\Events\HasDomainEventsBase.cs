﻿using System.ComponentModel.DataAnnotations.Schema;

namespace TMS.SharedKernel.Domain.Events;

/// <summary>
/// Represents an entity that can raise domain events.
/// </summary>
public abstract class HasDomainEventsBase
{
    private readonly List<DomainEventBase> _domainEvents = [];

    /// <summary>
    /// Gets the domain events raised by this entity.
    /// </summary>
    [NotMapped]
    public IEnumerable<DomainEventBase> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Registers a domain event to be raised by this entity.
    /// </summary>
    /// <param name="domainEvent"></param>
    protected void RegisterDomainEvent(DomainEventBase domainEvent) => _domainEvents.Add(domainEvent);

    /// <summary>
    /// Clears all domain events raised by this entity.
    /// </summary>
    public void ClearDomainEvents() => _domainEvents.Clear();
}
