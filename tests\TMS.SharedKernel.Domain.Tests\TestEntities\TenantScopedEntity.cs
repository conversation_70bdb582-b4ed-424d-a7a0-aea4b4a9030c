using TMS.SharedKernel.Domain.Entities;
using TMS.SharedKernel.Domain.Entities.Interfaces;

namespace TMS.SharedKernel.Domain.Tests.TestEntities;

/// <summary>
/// Test entity that implements ITenant and ISoftDelete - should be filtered by CompanyId AND IsDeleted
/// </summary>
public class TenantScopedEntity : AuditableEntity, ITenant, ISoftDelete
{
    public Guid CompanyId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsDeleted { get; set; }
}
