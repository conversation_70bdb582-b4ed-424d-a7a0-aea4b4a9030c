using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.Kafka.Configuration;
using TMS.SharedKernal.Kafka.Services;
using Xunit;

namespace TMS.SharedKernal.Kafka.Tests;

/// <summary>
/// Integration tests for topic auto-creation feature
/// </summary>
public class TopicAutoCreationIntegrationTests
{
    [Fact]
    public void ServiceConfiguration_WithAutoCreate_RegistersTopicManager()
    {
        // Arrange
        var services = new ServiceCollection();

        // Add logging
        services.AddLogging();

        // Simulate the configuration that would register ITopicManager
        var hasAutoCreate = true;

        // Act
        if (hasAutoCreate)
        {
            services.AddSingleton<ITopicManager>(provider =>
            {
                var options = new KafkaFlowOptions
                {
                    BootstrapServers = "localhost:9092",
                    Topics = new List<TopicConfiguration>()
                };
                var logger = provider.GetRequiredService<ILogger<KafkaTopicManager>>();
                return new KafkaTopicManager(options, logger);
            });
        }

        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var topicManager = serviceProvider.GetService<ITopicManager>();
        Assert.NotNull(topicManager);
    }

    [Fact]
    public void ServiceConfiguration_WithoutAutoCreate_DoesNotRegisterTopicManager()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        var hasAutoCreate = false;

        // Act
        if (hasAutoCreate)
        {
            services.AddSingleton<ITopicManager, KafkaTopicManager>();
        }

        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var topicManager = serviceProvider.GetService<ITopicManager>();
        Assert.Null(topicManager);
    }

    [Fact]
    public void TopicList_WithMixedAutoCreate_FiltersCorrectly()
    {
        // Arrange
        var topics = new List<TopicConfiguration>
        {
            new() { TopicName = "topic-1", AutoCreateTopic = true },
            new() { TopicName = "topic-2", AutoCreateTopic = false },
            new() { TopicName = "topic-3", AutoCreateTopic = true },
        };

        // Act
        var topicsToCreate = topics.Where(t => t.AutoCreateTopic).ToList();

        // Assert
        Assert.Equal(2, topicsToCreate.Count);
        Assert.Contains(topicsToCreate, t => t.TopicName == "topic-1");
        Assert.Contains(topicsToCreate, t => t.TopicName == "topic-3");
        Assert.DoesNotContain(topicsToCreate, t => t.TopicName == "topic-2");
    }

    [Fact]
    public void TopicCreation_GeneratesExpectedTopicList()
    {
        // Arrange
        var options = new KafkaFlowOptions
        {
            BootstrapServers = "localhost:9092",
            Topics = new List<TopicConfiguration>
            {
                new()
                {
                    TopicName = "orders",
                    Partitions = 6,
                    ReplicationFactor = 3,
                    AutoCreateTopic = true
                },
                new()
                {
                    TopicName = "payments",
                    Partitions = 3,
                    ReplicationFactor = 2,
                    AutoCreateTopic = true
                }
            },
            DeadLetter = new DeadLetterOptions
            {
                Enabled = true,
                TopicName = "app-dlq"
            }
        };

        // Act
        var topicsToCreate = new List<(string name, int partitions, short replicationFactor)>();

        foreach (var topicConfig in options.Topics)
        {
            if (topicConfig.AutoCreateTopic)
            {
                topicsToCreate.Add((topicConfig.TopicName, topicConfig.Partitions, topicConfig.ReplicationFactor));
                topicsToCreate.Add(($"{topicConfig.TopicName}.retry", topicConfig.Partitions, topicConfig.ReplicationFactor));
            }
        }

        if (options.DeadLetter.Enabled)
        {
            topicsToCreate.Add((options.DeadLetter.TopicName, 1, 1));
        }

        // Assert
        Assert.Equal(5, topicsToCreate.Count);

        // Verify orders
        Assert.Contains(topicsToCreate, t => t.name == "orders" && t.partitions == 6 && t.replicationFactor == 3);
        Assert.Contains(topicsToCreate, t => t.name == "orders.retry" && t.partitions == 6 && t.replicationFactor == 3);

        // Verify payments
        Assert.Contains(topicsToCreate, t => t.name == "payments" && t.partitions == 3 && t.replicationFactor == 2);
        Assert.Contains(topicsToCreate, t => t.name == "payments.retry" && t.partitions == 3 && t.replicationFactor == 2);

        // Verify DLQ
        Assert.Contains(topicsToCreate, t => t.name == "app-dlq" && t.partitions == 1 && t.replicationFactor == 1);
    }

    [Fact]
    public void TopicCreation_WithDuplicates_RemovesDuplicates()
    {
        // Arrange
        var topicsToCreate = new List<(string name, int partitions, short replicationFactor)>
        {
            ("orders", 3, 2),
            ("orders.retry", 3, 2),
            ("orders", 3, 2), // Duplicate
            ("payments", 3, 2),
        };

        // Act
        var distinctTopics = topicsToCreate.Distinct().ToList();

        // Assert
        Assert.Equal(3, distinctTopics.Count);
    }

    [Theory]
    [InlineData("orders", 1, 1)]
    [InlineData("payments", 3, 2)]
    [InlineData("user-events", 6, 3)]
    [InlineData("system-logs", 12, 3)]
    public void TopicConfiguration_WithVariousScales_IsValid(string topicName, int partitions, short replicationFactor)
    {
        // Arrange
        var topic = new TopicConfiguration
        {
            TopicName = topicName,
            Partitions = partitions,
            ReplicationFactor = replicationFactor,
            AutoCreateTopic = true
        };

        // Assert
        Assert.Equal(topicName, topic.TopicName);
        Assert.Equal(partitions, topic.Partitions);
        Assert.Equal(replicationFactor, topic.ReplicationFactor);
        Assert.True(topic.AutoCreateTopic);
    }

    [Fact]
    public void TopicRetention_DefaultValue_Is7Days()
    {
        // Default retention policy in KafkaTopicManager
        var retentionMs = "604800000"; // 7 days in milliseconds

        // Assert
        Assert.Equal("604800000", retentionMs);
        Assert.Equal(7 * 24 * 60 * 60 * 1000, int.Parse(retentionMs));
    }

    [Fact]
    public void TopicCompression_DefaultType_IsSnappy()
    {
        // Default compression type in KafkaTopicManager
        var compressionType = "snappy";

        // Assert
        Assert.Equal("snappy", compressionType);
    }

    [Fact]
    public void MinInsyncReplicas_WithReplicationFactor1_Returns1()
    {
        // Arrange
        short replicationFactor = 1;

        // Act
        var minInsyncReplicas = Math.Min((short)1, replicationFactor);

        // Assert
        Assert.Equal(1, minInsyncReplicas);
    }

    [Fact]
    public void MinInsyncReplicas_WithReplicationFactor3_Returns1()
    {
        // Arrange
        short replicationFactor = 3;

        // Act
        var minInsyncReplicas = Math.Min((short)1, replicationFactor);

        // Assert
        Assert.Equal(1, minInsyncReplicas);
    }

    [Fact]
    public void ConsumerStartup_WithTopicManager_InvokesCreateAllTopics()
    {
        // This test verifies the integration flow
        // In actual implementation, consumer calls topicManager.CreateAllTopicsAsync()

        // Arrange
        var topicManagerCalled = false;
        var mockTopicManager = new Mock<ITopicManager>();

        mockTopicManager
            .Setup(tm => tm.CreateAllTopicsAsync(It.IsAny<CancellationToken>()))
            .Callback(() => topicManagerCalled = true)
            .Returns(Task.CompletedTask);

        // Act - Simulate consumer startup
        var result = mockTopicManager.Object.CreateAllTopicsAsync(CancellationToken.None);
        result.Wait();

        // Assert
        Assert.True(topicManagerCalled);
        mockTopicManager.Verify(tm => tm.CreateAllTopicsAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ConsumerStartup_WithoutTopicManager_ContinuesNormally()
    {
        // Arrange
        ITopicManager? topicManager = null;
        var consumerStarted = false;

        // Act - Simulate consumer startup without topic manager
        if (topicManager != null)
        {
            await topicManager.CreateAllTopicsAsync(CancellationToken.None);
        }

        // Consumer continues even without topic manager
        consumerStarted = true;

        // Assert
        Assert.True(consumerStarted);
    }

    [Fact]
    public async Task ConsumerStartup_WhenTopicCreationFails_ContinuesWithWarning()
    {
        // Arrange
        var mockTopicManager = new Mock<ITopicManager>();
        var exceptionThrown = false;
        var consumerContinued = false;

        mockTopicManager
            .Setup(tm => tm.CreateAllTopicsAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Kafka broker not available"));

        // Act - Simulate consumer startup with topic creation failure
        try
        {
            await mockTopicManager.Object.CreateAllTopicsAsync(CancellationToken.None);
        }
        catch
        {
            exceptionThrown = true;
            // Consumer continues despite exception
            consumerContinued = true;
        }

        // Assert
        Assert.True(exceptionThrown);
        Assert.True(consumerContinued);
    }

    [Fact]
    public void EnableAutoCreateTopics_EnablesAllTopics()
    {
        // Arrange
        var topics = new List<TopicConfiguration>
        {
            new() { TopicName = "topic-1", AutoCreateTopic = false },
            new() { TopicName = "topic-2", AutoCreateTopic = false },
            new() { TopicName = "topic-3", AutoCreateTopic = false },
        };

        // Act - Simulate EnableAutoCreateTopics()
        foreach (var topic in topics)
        {
            topic.AutoCreateTopic = true;
        }

        // Assert
        Assert.True(topics.All(t => t.AutoCreateTopic));
    }

    [Fact]
    public void BuilderPattern_WithAutoCreate_ConfiguresCorrectly()
    {
        // This test simulates the builder pattern usage

        // Arrange & Act
        var topics = new List<TopicConfiguration>();

        // Simulate .AddTopic() calls
        topics.Add(new TopicConfiguration
        {
            TopicName = "orders",
            ConsumerGroup = "order-service",
            Partitions = 3,
            ReplicationFactor = 2,
            AutoCreateTopic = true
        });

        topics.Add(new TopicConfiguration
        {
            TopicName = "payments",
            ConsumerGroup = "payment-service",
            Partitions = 3,
            ReplicationFactor = 2,
            AutoCreateTopic = true
        });

        // Assert
        Assert.Equal(2, topics.Count);
        Assert.True(topics.All(t => t.AutoCreateTopic));
        Assert.True(topics.All(t => t.Partitions == 3));
        Assert.True(topics.All(t => t.ReplicationFactor == 2));
    }

    [Fact]
    public void DevelopmentEnvironment_UsesLowResourceConfiguration()
    {
        // Arrange - Development configuration
        var devConfig = new TopicConfiguration
        {
            TopicName = "dev-topic",
            Partitions = 1,
            ReplicationFactor = 1,
            AutoCreateTopic = true
        };

        // Assert
        Assert.Equal(1, devConfig.Partitions);
        Assert.Equal(1, devConfig.ReplicationFactor);
        Assert.True(devConfig.AutoCreateTopic);
    }

    [Fact]
    public void ProductionEnvironment_UsesHighAvailabilityConfiguration()
    {
        // Arrange - Production configuration
        var prodConfig = new TopicConfiguration
        {
            TopicName = "prod-topic",
            Partitions = 6,
            ReplicationFactor = 3,
            AutoCreateTopic = false // Typically disabled in production
        };

        // Assert
        Assert.Equal(6, prodConfig.Partitions);
        Assert.Equal(3, prodConfig.ReplicationFactor);
        Assert.False(prodConfig.AutoCreateTopic);
    }

    [Theory]
    [InlineData(true, 5)]  // 2 main + 2 retry + 1 DLQ
    [InlineData(false, 4)] // 2 main + 2 retry, no DLQ
    public void TopicCount_WithDLQToggle_CalculatesCorrectly(bool dlqEnabled, int expectedCount)
    {
        // Arrange
        var options = new KafkaFlowOptions
        {
            Topics = new List<TopicConfiguration>
            {
                new() { TopicName = "topic-1", AutoCreateTopic = true },
                new() { TopicName = "topic-2", AutoCreateTopic = true }
            },
            DeadLetter = new DeadLetterOptions
            {
                Enabled = dlqEnabled,
                TopicName = "test-dlq"
            }
        };

        // Act
        var count = 0;
        foreach (var topic in options.Topics.Where(t => t.AutoCreateTopic))
        {
            count += 2; // Main + retry
        }
        if (options.DeadLetter.Enabled)
        {
            count++;
        }

        // Assert
        Assert.Equal(expectedCount, count);
    }

    [Fact]
    public void ParallelTopicCreation_CalculatesCorrectly()
    {
        // Arrange
        var topics = new List<(string name, int partitions, short replicationFactor)>
        {
            ("topic-1", 3, 2),
            ("topic-1.retry", 3, 2),
            ("topic-2", 3, 2),
            ("topic-2.retry", 3, 2),
            ("dlq", 1, 1)
        };

        // Act - Simulate parallel creation
        var distinctTopics = topics.Distinct().ToList();
        var taskCount = distinctTopics.Count;

        // Assert
        Assert.Equal(5, taskCount);
    }

    [Fact]
    public void TopicCreationSuccess_CountsCorrectly()
    {
        // Arrange
        var results = new[] { true, true, true, false, true }; // 4 success, 1 failure

        // Act
        var successCount = results.Count(r => r);
        var failureCount = results.Length - successCount;

        // Assert
        Assert.Equal(4, successCount);
        Assert.Equal(1, failureCount);
    }

    [Fact]
    public void SecurityConfiguration_WithSASL_IsApplied()
    {
        // Arrange
        var options = new KafkaFlowOptions
        {
            BootstrapServers = "kafka-cluster:9092",
            Security = new SecurityOptions
            {
                Sasl = new SaslOptions
                {
                    Enabled = true,
                    Mechanism = "SCRAM-SHA-256",
                    Username = "kafka-user",
                    Password = "kafka-password"
                }
            }
        };

        // Assert
        Assert.True(options.Security.Sasl.Enabled);
        Assert.Equal("SCRAM-SHA-256", options.Security.Sasl.Mechanism);
    }

    [Fact]
    public void TopicNaming_WithSpecialCharacters_IsValid()
    {
        // Arrange
        var topicNames = new[]
        {
            "orders",
            "order-created",
            "order.created",
            "order_created",
            "system.events.orders"
        };

        // Act & Assert
        foreach (var topicName in topicNames)
        {
            Assert.False(string.IsNullOrEmpty(topicName));
            // All these naming conventions are valid in Kafka
        }
    }
}
