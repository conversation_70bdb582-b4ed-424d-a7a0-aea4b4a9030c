﻿using Microsoft.EntityFrameworkCore.Storage;

namespace TMS.SharedKernel.Domain;
/// <summary>
/// Provides an interface for managing transactions and coordinating the saving of changes in a unit of work pattern.
/// </summary>
public interface IUnitOfWork
{
    /// <summary>
    /// Executes the specified operation within a transaction, using an execution strategy that supports retries on failure.
    /// </summary>
    /// <param name="operation">
    /// The operation to execute within the transaction. The operation should include any necessary database changes
    /// </param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task ExecuteTransactionAsync(Action operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes the specified operation within a transaction, using an execution strategy that supports retries on failure.
    /// </summary>
    /// <typeparam name="TOutput">The type of the result returned by the operation.</typeparam>
    /// <param name="operation">
    /// The operation to execute within the transaction. The operation should include any necessary database changes
    /// </param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation, containing the result of type <typeparamref name="TOutput"/>.</returns>
    Task<TOutput> ExecuteTransactionAsync<TOutput>(Func<Task<TOutput>> operation,
        CancellationToken cancellationToken = default) where TOutput : notnull;

    /// <summary>
    /// Executes the specified operation within a transaction, using an execution strategy that supports retries on failure.
    /// </summary>
    /// <param name="operation">
    /// The operation to execute within the transaction. The operation should include any necessary database changes
    /// </param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task ExecuteTransactionAsync(Func<Task> operation,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Begins a new transaction asynchronously.
    /// </summary>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Begins a new transaction asynchronously.
    /// </summary>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>An instance of <see cref="IDbContextTransaction"/></returns>
    Task<IDbContextTransaction> BeginTransactionWithContextAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Commits the current transaction asynchronously. Throws an <see cref="InvalidOperationException"/> if no transaction is in progress.
    /// </summary>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rolls back the current transaction asynchronously. Throws an <see cref="InvalidOperationException"/> if no transaction is in progress.
    /// </summary>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves all changes made in the current context to the database asynchronously.
    /// </summary>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task representing the asynchronous operation, with the result indicating the number of state entries written to the database.</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
