syntax = "proto3";

option csharp_namespace = "TMS.TruckService.Api.Grpc";

package order;

// The order service definition
service OrderService {
  // Get an order by ID
  rpc GetOrder (GetOrderRequest) returns (GetOrderReply);

  // Create a new order
  rpc CreateOrder (CreateOrderRequest) returns (CreateOrderReply);

  // List orders with pagination
  rpc ListOrders (ListOrdersRequest) returns (ListOrdersReply);

  // Update order status
  rpc UpdateOrderStatus (UpdateOrderStatusRequest) returns (UpdateOrderStatusReply);

  // Delete an order
  rpc DeleteOrder (DeleteOrderRequest) returns (DeleteOrderReply);
}

// Request message for GetOrder
message GetOrderRequest {
  int32 order_id = 1;
}

// Reply message for GetOrder
message GetOrderReply {
  bool success = 1;
  string error_message = 2;
  string error_code = 3;
  OrderDto order = 4;
}

// Request message for CreateOrder
message CreateOrderRequest {
  string customer_name = 1;
  double amount = 2;
  string description = 3;
}

// Reply message for CreateOrder
message CreateOrderReply {
  bool success = 1;
  string error_message = 2;
  string error_code = 3;
  OrderDto order = 4;
}

// Request message for ListOrders
message ListOrdersRequest {
  int32 page_number = 1;
  int32 page_size = 2;
  string status = 3;
}

// Reply message for ListOrders
message ListOrdersReply {
  bool success = 1;
  string error_message = 2;
  string error_code = 3;
  repeated OrderDto orders = 4;
  int32 page_number = 5;
  int32 page_size = 6;
  int64 total_count = 7;
  int32 total_pages = 8;
  bool has_previous_page = 9;
  bool has_next_page = 10;
}

// Request message for UpdateOrderStatus
message UpdateOrderStatusRequest {
  int32 order_id = 1;
  string new_status = 2;
  string notes = 3;
}

// Reply message for UpdateOrderStatus
message UpdateOrderStatusReply {
  bool success = 1;
  string error_message = 2;
  string error_code = 3;
}

// Request message for DeleteOrder
message DeleteOrderRequest {
  int32 order_id = 1;
}

// Reply message for DeleteOrder
message DeleteOrderReply {
  bool success = 1;
  string error_message = 2;
  string error_code = 3;
}

// Order data transfer object
message OrderDto {
  int32 order_id = 1;
  string order_number = 2;
  string customer_name = 3;
  double amount = 4;
  string status = 5;
  string description = 6;
  string created_at = 7;
  string created_by = 8;
}
