﻿using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Query;

namespace TMS.SharedKernel.Domain;

/// <summary>
/// Generic repository interface for basic CRUD operations.
/// </summary>
/// <typeparam name="TEntity">The entity type.</typeparam>
public interface IBaseRepository<TEntity> where TEntity : class
{
    // Query operations
    IQueryable<TEntity> GetQueryable(bool asNoTracking = true);

    // Read operations
    Task<TEntity?> GetByIdAsync(object id, CancellationToken cancellationToken = default, bool asNoTracking = true);

    Task<List<TKey>> GetIdsAsync<TKey>(
        Expression<Func<TEntity, bool>> predicate,
        Expression<Func<TEntity, TKey>> keySelector,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true);

    Task<List<TResult>> GetColumnsAsync<TResult>(
        Expression<Func<TEntity, bool>> predicate,
        Expression<Func<TEntity, TResult>> selector,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true);

    Task<TEntity?> GetByIdAsync<TKey>(TKey id, CancellationToken cancellationToken = default, bool asNoTracking = true) where TKey : notnull;

    Task<IReadOnlyList<TEntity>> GetAllAsync(CancellationToken cancellationToken = default, bool asNoTracking = true);

    Task<IReadOnlyList<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default, bool asNoTracking = true);

    Task<IReadOnlyList<TEntity>> FindWithIncludeAsync(Expression<Func<TEntity, bool>> predicate,
        Func<IQueryable<TEntity>, IQueryable<TEntity>>? includeFunc = null,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true);

    Task<TEntity?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default, bool asNoTracking = true);
    Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default);
    Task<int> CountAsync(Expression<Func<TEntity, bool>>? predicate = null, CancellationToken cancellationToken = default);
    
    // Paging
    Task<PagedResult<TEntity>> GetPagedAsync(int page, int pageSize,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        CancellationToken cancellationToken = default);

    //Task<PagedResult<TEntity>> GetPagedAsync(int page, int pageSize, Expression<Func<TEntity, bool>>? predicate = null,
    //    IEnumerable<ISortOption<TEntity>>? sortOptions = null, CancellationToken cancellationToken = default,
    //    params Expression<Func<TEntity, object>>[] includes);

    Task<PagedResult<TEntity>> GetPagedAsync(int page, int pageSize,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        Func<IQueryable<TEntity>, IQueryable<TEntity>>? includeFunc = null,
        CancellationToken cancellationToken = default);

    Task<PagedResult<TResult>> GetPagedAsync<TResult>(int page, int pageSize,
        Expression<Func<TEntity, TResult>> selector,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        CancellationToken cancellationToken = default);

    //Task<PagedResult<TResult>> GetPagedAsync<TResult>(int page, int pageSize,
    //    Expression<Func<TEntity, TResult>> selector,
    //    Expression<Func<TEntity, bool>>? predicate = null,
    //    IEnumerable<ISortOption<TEntity>>? sortOptions = null,
    //    CancellationToken cancellationToken = default,
    //    params Expression<Func<TEntity, object>>[] includes);

    Task<PagedResult<TResult>> GetPagedAsync<TResult>(int page, int pageSize,
        Expression<Func<TEntity, TResult>> selector,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        Func<IQueryable<TEntity>, IQueryable<TEntity>>? includeFunc = null,
        CancellationToken cancellationToken = default);

    // Include operations
    Task<IReadOnlyList<TEntity>> FindWithIncludeAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true,
        params Expression<Func<TEntity, object>>[] includes);

    // Write operations
    Task AddAsync(TEntity entity, CancellationToken cancellationToken = default);
    Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);
    void Update(TEntity entity);
    void UpdateRange(IEnumerable<TEntity> entities);
    void Remove(TEntity entity);
    void RemoveRange(IEnumerable<TEntity> entities);
    Task<bool> RemoveByIdAsync(object id, CancellationToken cancellationToken = default);

    // Bulk operations using ExecuteUpdateAsync
    Task<int> ExecuteUpdateAsync(Expression<Func<TEntity, bool>> predicate,
        Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> setterExpression,
        CancellationToken cancellationToken = default);

    // Upsert operations (PostgreSQL)
    /// <summary>
    /// Upserts a single entity using the entity's configured primary key
    /// </summary>
    Task<int> UpsertAsync(TEntity entity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Upserts a single entity using custom key columns specified by user
    /// </summary>
    /// <param name="entity">The entity to upsert</param>
    /// <param name="keyColumns">Property expressions for the key columns to use for conflict detection</param>
    /// <param name="updateColumns">Property expressions for the columns to update on conflict. If null, all non-key columns will be updated.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of affected rows</returns>
    Task<int> UpsertAsync(TEntity entity, Expression<Func<TEntity, object>>[] keyColumns, Expression<Func<TEntity, object>>[]? updateColumns = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Upserts multiple entities using the entity's configured primary key
    /// </summary>
    Task<int> UpsertRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default);

    /// <summary>
    /// Upserts multiple entities using custom key columns specified by user
    /// </summary>
    /// <param name="entities">The entities to upsert</param>
    /// <param name="keyColumns">Property expressions for the key columns to use for conflict detection</param>
    /// <param name="updateColumns">Property expressions for the columns to update on conflict. If null, all non-key columns will be updated.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of affected rows</returns>
    Task<int> UpsertRangeAsync(IEnumerable<TEntity> entities, Expression<Func<TEntity, object>>[] keyColumns, Expression<Func<TEntity, object>>[]? updateColumns = null, CancellationToken cancellationToken = default);
}
