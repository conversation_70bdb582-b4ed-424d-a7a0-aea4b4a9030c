﻿namespace TMS.SharedKernal.RabbitMq.Services;

using System.Diagnostics;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.SharedKernal.RabbitMq.Configuration;
using TMS.SharedKernal.RabbitMq.Serialization;
using RabbitMQ.Client;

public class RabbitMqEventPublisher : IEventPublisher, IDisposable
{
    private static readonly ActivitySource ActivitySource = new("TMS.RabbitMQ.Publisher");
    private static readonly TextMapPropagator Propagator = Propagators.DefaultTextMapPropagator;

    private readonly IConnection _connection;
    private readonly IModel _channel;
    private readonly RabbitMqOptions _options;
    private readonly IEventSerializer _serializer;
    private readonly ILogger<RabbitMqEventPublisher> _logger;

    public RabbitMqEventPublisher(
        RabbitMqOptions options,
        IEventSerializer serializer,
        ILogger<RabbitMqEventPublisher> logger)
    {
        _options = options;
        _serializer = serializer;
        _logger = logger;

        var factory = new ConnectionFactory()
        {
            HostName = _options.HostName,
            Port = _options.Port,
            UserName = _options.UserName,
            Password = _options.Password,
            VirtualHost = _options.VirtualHost
        };

        _connection = factory.CreateConnection();
        _channel = _connection.CreateModel();

        SetupExchange();
    }

    private void SetupExchange()
    {
        var exchangeType = _options.Exchange.Type switch
        {
            Configuration.ExchangeType.Direct => global::RabbitMQ.Client.ExchangeType.Direct,
            Configuration.ExchangeType.Topic => global::RabbitMQ.Client.ExchangeType.Topic,
            Configuration.ExchangeType.Fanout => global::RabbitMQ.Client.ExchangeType.Fanout,
            Configuration.ExchangeType.Headers => global::RabbitMQ.Client.ExchangeType.Headers,
            _ => global::RabbitMQ.Client.ExchangeType.Topic
        };

        _channel.ExchangeDeclare(_options.Exchange.Name, exchangeType, _options.Exchange.Durable);
    }

    public async Task PublishAsync<T>(T eventData, string? routingKey = null, CancellationToken cancellationToken = default) where T : class, IEvent
    {
        await Task.Run(() => Publish(eventData, routingKey), cancellationToken);
    }

    public void Publish<T>(T eventData, string? routingKey = null) where T : class, IEvent
    {
        var finalRoutingKey = routingKey ?? GenerateRoutingKey(eventData.EventType);

        // Start an activity for this publish operation with proper naming convention
        using var activity = ActivitySource.StartActivity($"{_options.Exchange.Name} publish", ActivityKind.Producer);

        try
        {
            var json = _serializer.Serialize(eventData);
            var body = Encoding.UTF8.GetBytes(json);

            var properties = _channel.CreateBasicProperties();
            properties.Persistent = true;
            properties.MessageId = eventData.EventId;
            properties.Type = eventData.EventType;
            properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
            properties.Headers = new Dictionary<string, object>
            {
                ["event_type"] = eventData.EventType,
                ["created_at"] = eventData.CreatedAt.ToString("O"),
                ["event_id"] = eventData.EventId
            };

            // Inject trace context into message headers for distributed tracing
            var propagationContext = activity?.Context ?? Activity.Current?.Context ?? default;
            Propagator.Inject(new PropagationContext(propagationContext, Baggage.Current), properties.Headers, InjectTraceContext);

            // Add OpenTelemetry messaging semantic convention tags
            activity?.SetTag("messaging.system", "rabbitmq");
            activity?.SetTag("messaging.destination.name", _options.Exchange.Name);
            activity?.SetTag("messaging.destination.kind", "topic");
            activity?.SetTag("messaging.operation", "publish");
            activity?.SetTag("messaging.message.id", eventData.EventId);
            activity?.SetTag("messaging.message.conversation_id", eventData.EventId);
            activity?.SetTag("messaging.rabbitmq.destination.routing_key", finalRoutingKey);
            activity?.SetTag("net.peer.name", _options.HostName);
            activity?.SetTag("net.peer.port", _options.Port);

            // Custom tags
            activity?.SetTag("messaging.event_type", eventData.EventType);
            activity?.SetTag("messaging.message.body.size", body.Length);

            _channel.BasicPublish(
                exchange: _options.Exchange.Name,
                routingKey: finalRoutingKey,
                basicProperties: properties,
                body: body
            );

            _logger.LogInformation("Published event {EventType} with ID {EventId} to routing key {RoutingKey}",
                eventData.EventType, eventData.EventId, finalRoutingKey);
        }
        catch (Exception ex)
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.SetTag("exception.type", ex.GetType().FullName);
            activity?.SetTag("exception.message", ex.Message);
            activity?.SetTag("exception.stacktrace", ex.StackTrace);

            _logger.LogError(ex, "Failed to publish event {EventType} with ID {EventId}",
                eventData.EventType, eventData.EventId);
            throw;
        }
    }

    private static void InjectTraceContext(IDictionary<string, object> headers, string key, string value)
    {
        headers[key] = value;
    }

    private string GenerateRoutingKey(string eventType)
    {
        // Convert PascalCase to routing key format
        // OrderCreated -> event.order.created
        var parts = new List<string> { "event" };

        for (int i = 0; i < eventType.Length; i++)
        {
            if (i > 0 && char.IsUpper(eventType[i]))
            {
                parts.Add(eventType.Substring(0, i).ToLower());
                eventType = eventType.Substring(i);
                i = 0;
            }
        }
        parts.Add(eventType.ToLower());

        return string.Join(".", parts);
    }

    public void Dispose()
    {
        _channel?.Dispose();
        _connection?.Dispose();
    }
}
