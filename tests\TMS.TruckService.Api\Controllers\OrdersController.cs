﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.TruckService.Application;
using TMS.TruckService.Contracts.Orders;
using TMS.TruckService.Domain.Events;

namespace TMS.TruckService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public class OrdersController : ControllerBase
{
    private readonly IEventPublisher _eventPublisher;

    public OrdersController([FromKeyedServices(Constants.RabbitMqCompanyEvents)] IEventPublisher eventPublisher)
    {
        _eventPublisher = eventPublisher;
    }

    [AllowAnonymous]
    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderRequest request)
    {
        Log.ForContext("customer.name", $"{request.CustomerName}").Information("Creating order for {CustomerName} with amount {Amount}", request.CustomerName, request.Amount);

        var orderEvent = new OrderCreatedEvent
        {
            OrderId = Guid.NewGuid().ToString(),
            CustomerName = request.CustomerName,
            Amount = request.Amount
        };

        await _eventPublisher.PublishAsync(orderEvent); //=> queue

        return Ok(new { OrderId = orderEvent.OrderId, EventId = orderEvent.EventId });
    }
}

