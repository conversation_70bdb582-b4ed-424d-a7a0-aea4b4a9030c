﻿namespace TMS.SharedKernal.Caching;

/// <summary>
/// Generic metadata caching service for storing and retrieving lists of entities in Redis
/// Provides both generic methods and convenience methods for common entity types
/// </summary>
public interface IMetadataCacheService
{
    #region Generic Methods

    /// <summary>
    /// Sets a list of items in Redis cache for the specified type
    /// </summary>
    /// <typeparam name="T">Type of items to cache</typeparam>
    /// <param name="items">List of items to cache</param>
    /// <param name="prefix">Optional prefix for the Redis key (e.g., companyId)</param>
    Task SetAsync<T>(List<T> items, string prefix = "") where T : class;

    /// <summary>
    /// Gets a list of items from Redis cache for the specified type
    /// </summary>
    /// <typeparam name="T">Type of items to retrieve</typeparam>
    /// <param name="prefix">Optional prefix for the Redis key (e.g., companyId)</param>
    /// <returns>List of cached items</returns>
    Task<List<T>> GetAsync<T>(string prefix = "") where T : class;

    /// <summary>
    /// Sets a dictionary of items in Redis cache for the specified type
    /// </summary>
    /// <typeparam name="T">Type of items to cache</typeparam>
    /// <param name="items">Dictionary of items to cache</param>
    /// <param name="prefix">Optional prefix for the Redis key (e.g., companyId)</param>
    Task SetAsync<T>(Dictionary<string, T> items, string prefix = "") where T : class;

    /// <summary>
    /// Gets a dictionary of items from Redis cache for the specified type
    /// </summary>
    /// <typeparam name="T">Type of items to retrieve</typeparam>
    /// <param name="prefix">Optional prefix for the Redis key (e.g., companyId)</param>
    /// <returns>Dictionary of cached items</returns>
    Task<Dictionary<string, T>> GetDictionaryAsync<T>(string prefix = "") where T : class;

    #endregion

    #region Convenience Methods - ServiceType

    /// <summary>
    /// Sets service type metadata in cache
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetServiceTypesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// Gets service type metadata from cache
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetServiceTypesAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - ExtraService

    /// <summary>
    /// Sets extra service metadata in cache
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetExtraServicesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// Gets extra service metadata from cache
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetExtraServicesAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - OrderStatus

    /// <summary>
    /// Sets order status metadata in cache
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetOrderStatusesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// Gets order status metadata from cache
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetOrderStatusesAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - PriorityPlan

    /// <summary>
    /// Sets priority plan metadata in cache (requires company prefix)
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetPriorityPlansAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// Gets priority plan metadata from cache (requires company prefix)
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetPriorityPlansAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - Wards, Provinces

    /// <summary>
    /// SetWardsAsync
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="items"></param>
    /// <param name="provinceId"></param>
    /// <returns></returns>
    Task SetWardsAsync<T>(List<T> items, string provinceId) where T : class;

    /// <summary>
    /// GetWardsAsync
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="provinceId"></param>
    /// <returns></returns>
    Task<List<T>> GetWardsAsync<T>(string provinceId) where T : class;

    /// <summary>
    /// SetProvincesAsync
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="items"></param>
    /// <param name="countryId"></param>
    /// <returns></returns>
    Task SetProvincesAsync<T>(List<T> items, string countryId) where T : class;

    /// <summary>
    /// GetProvincesAsync
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    Task<List<T>> GetProvincesAsync<T>(string countryId) where T : class;

    #endregion

    #region Convenience Methods - PostOffices

    /// <summary>
    /// Sets post office metadata in cache using batch operations
    /// Each post office is stored with individual key: metadata:postoffices:{code}
    /// </summary>
    /// <typeparam name="T">Entity type (must have PostOfficeCode property)</typeparam>
    /// <param name="items">List of post offices to cache</param>
    /// <param name="expiry">Optional expiration time</param>
    /// <returns></returns>
    Task SetPostOfficesAsync<T>(List<T> items, TimeSpan? expiry = null) where T : class;

    /// <summary>
    /// Gets post office metadata from cache by codes using batch operations
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    /// <param name="codes">List of post office codes to retrieve</param>
    /// <returns>Dictionary of post offices by code</returns>
    Task<Dictionary<string, T>> GetPostOfficesAsync<T>(List<string> codes) where T : class;

    #endregion

    #region Convenience Methods - Zone

    /// <summary>
    /// GetZonesAsync
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetZonesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// GetZonesAsync
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetZonesAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - LeadTimeType

    /// <summary>
    /// SetLeadTimeTypesAsync
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetLeadTimeTypesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// GetLeadTimeTypesAsync
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetLeadTimeTypesAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - TransportMethodType

    /// <summary>
    /// SetTransportMethodTypesAsync
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetTransportMethodTypesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// GetTransportMethodTypesAsync
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetTransportMethodTypesAsync<T>() where T : class;

    #endregion

    #region Convenience Methods - TransportVehicleType

    /// <summary>
    /// SetTransportVehicleTypesAsync
    /// </summary>
    /// <typeparam name="T">Entity type (inferred from items parameter)</typeparam>
    Task SetTransportVehicleTypesAsync<T>(List<T> items) where T : class;

    /// <summary>
    /// GetTransportVehicleTypesAsync
    /// </summary>
    /// <typeparam name="T">Entity type to retrieve</typeparam>
    Task<List<T>> GetTransportVehicleTypesAsync<T>() where T : class;

    #endregion

}
