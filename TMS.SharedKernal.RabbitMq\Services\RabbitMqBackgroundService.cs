﻿using Microsoft.Extensions.Logging;
using TMS.SharedKernal.RabbitMq.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace TMS.SharedKernal.RabbitMq.Services;

public class RabbitMqBackgroundService<T> : BackgroundService where T : class, IEvent
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RabbitMqBackgroundService<T>> _logger;
    private readonly string _serviceKey;
    private readonly string _queueName;
    private IEventConsumer? _consumer;

    public RabbitMqBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<RabbitMqBackgroundService<T>> logger,
        string serviceKey,
        string queueName)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _serviceKey = serviceKey;
        _queueName = queueName;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        return Task.Run(async () =>
        {
            // Scope to get the consumer
            using var scope = _serviceProvider.CreateScope();

            var sp = scope.ServiceProvider;

            // consumer with key
            _consumer = sp.GetKeyedService<IEventConsumer>(_serviceKey);

            // get handler
            var handler = sp.GetService<IEventHandler<T>>();
            if (handler == null)
            {
                _logger.LogError("No event handler registered for type {EventType}", typeof(T).Name);
                return;
            }

            // start consuming
            _consumer?.StartConsuming<T>(_queueName, async (eventData) =>
            {
                try
                {
                    using var handlerScope = _serviceProvider.CreateScope();
                    var scopedHandler = handlerScope.ServiceProvider.GetRequiredService<IEventHandler<T>>();

                    return await scopedHandler.HandleAsync(eventData, stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error handling event of type {EventType}", typeof(T).Name);
                }

                return true; // Acknowledge even on failure to avoid infinite requeue
            });

            // Keep the service running
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }, stoppingToken);
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _consumer?.StopConsuming();
        return base.StopAsync(cancellationToken);
    }
}
