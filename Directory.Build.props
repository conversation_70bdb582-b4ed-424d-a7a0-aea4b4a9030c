<Project>
	<Choose>
		<When Condition="!$(MSBuildProjectName.EndsWith('.UnitTests')) AND '$(MSBuildProjectName)' != 'Solution Items' AND !$(MSBuildProjectName.EndsWith('.IntegrationTests'))">
			<PropertyGroup>
				<TargetFramework>net9.0</TargetFramework>
				<ImplicitUsings>enable</ImplicitUsings>
				<Nullable>enable</Nullable>
			
				<GenerateDocumentationFile>true</GenerateDocumentationFile>
			</PropertyGroup>

      <PropertyGroup Condition="'$(CI)' == 'true' AND '$(MSBuildProjectName)' != 'TMS.SharedKernel.EntityFrameworkCore.Benchmark'">
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <WarningsAsErrors>1591</WarningsAsErrors>
      </PropertyGroup>
      
			<ItemGroup>
				
				<None Update="**\*">
					<Exclude>**\log*.txt;**\bin\**;**\obj\**;**\.vs\**;**\.git\**;**\.idea\**;**\*.nupkg</Exclude>
				</None>
			</ItemGroup>
		</When>
		<Otherwise/>
	</Choose>	
</Project>