# TMS.SharedKernel
The instrumentation already collects comprehensive metrics:

  ASP.NET Core Metrics:
  - http.server.request.duration - Request duration histogram
  - http.server.active_requests - Current active requests
  - http.server.request.body.size - Request body sizes
  - http.server.response.body.size - Response body sizes

  HTTP Client Metrics:
  - http.client.request.duration - Outgoing HTTP request durations
  - http.client.active_requests - Active outgoing requests

  Runtime Metrics:
  - process.runtime.dotnet.gc.collections.count - GC collection counts
  - process.runtime.dotnet.gc.heap.size - Heap size
  - process.runtime.dotnet.gc.duration - GC pause times
  - process.runtime.dotnet.monitor.lock_contentions.count - Thread contention

  Process Metrics:
  - process.cpu.utilization - CPU usage
  - process.memory.usage - Memory usage
  - process.threads - Thread count
  
  
Basic Examples

  // Example 1: Using default configuration key
  services.AddRefitClient<IPmsApi>()
      .ConfigureHttpClient(c => c.BaseAddress = new Uri("https://api.pms.com"))
      .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
          sp.GetRequiredService<IConfiguration>()
      ));

  // Example 2: Custom configuration key and query parameter
  services.AddRefitClient<IExternalApi>()
      .ConfigureHttpClient(c => c.BaseAddress = new Uri("https://external-api.com"))
      .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
          sp.GetRequiredService<IConfiguration>(),
          configurationKey: "ExternalApi:ApiKey",
          queryParameterName: "api_key"
      ));

  // Example 3: Multiple APIs with different keys
  services.AddRefitClient<IWeatherApi>()
      .ConfigureHttpClient(c => c.BaseAddress = new Uri("https://weather-api.com"))
      .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
          sp.GetRequiredService<IConfiguration>(),
          configurationKey: "WeatherApi:Key",
          queryParameterName: "key"
      ));

  Complete Example

  // 1. Define your API interface
  public interface IPmsApi
  {
      [Get("/api/planning/routes")]
      Task<List<Route>> GetRoutesAsync();

      [Get("/api/planning/orders/{orderId}")]
      Task<Order> GetOrderAsync(string orderId);

      [Post("/api/planning/optimize")]
      Task<OptimizationResult> OptimizeAsync([Body] OptimizationRequest request);
  }

  // 2. Register in Program.cs or Startup.cs
  public static IServiceCollection AddPmsApiClient(
      this IServiceCollection services,
      IConfiguration configuration)
  {
      var baseUrl = configuration["PmsApi:BaseUrl"]
          ?? throw new InvalidOperationException("PmsApi:BaseUrl is not configured");

      services.AddRefitClient<IPmsApi>(new RefitSettings
      {
          ContentSerializer = new SystemTextJsonContentSerializer(
              new JsonSerializerOptions
              {
                  PropertyNamingPolicy = JsonNamingPolicy.CamelCase
              })
      })
      .ConfigureHttpClient(c => c.BaseAddress = new Uri(baseUrl))
      .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
          sp.GetRequiredService<IConfiguration>(),
          configurationKey: "PmsApi:ApiKey",
          queryParameterName: "apikey"
      ))
      .SetHandlerLifetime(TimeSpan.FromMinutes(5)); // Optional: set handler lifetime

      return services;
  }

  // 3. Configuration in appsettings.json
  {
    "PmsApi": {
      "BaseUrl": "https://api.pms.com",
      "ApiKey": "your-secret-api-key-here"
    }
  }

  // 4. Usage in your service/controller
  public class PlanningService
  {
      private readonly IPmsApi _pmsApi;

      public PlanningService(IPmsApi pmsApi)
      {
          _pmsApi = pmsApi;
      }

      public async Task<List<Route>> GetRoutesAsync()
      {
          // The API key will be automatically added as ?apikey=your-secret-api-key-here
          return await _pmsApi.GetRoutesAsync();
      }
  }

  Advanced Example with Multiple Handlers

  // Combining with other handlers (e.g., retry policy, logging)
  services.AddRefitClient<IPmsApi>()
      .ConfigureHttpClient(c => c.BaseAddress = new Uri("https://api.pms.com"))
      .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
          sp.GetRequiredService<IConfiguration>(),
          configurationKey: "PmsApi:ApiKey",
          queryParameterName: "apikey"
      ))
      .AddHttpMessageHandler<AuthorizationDelegatingHandler>() // JWT token if needed
      .AddPolicyHandler(GetRetryPolicy()) // Polly retry policy
      .AddPolicyHandler(GetCircuitBreakerPolicy()); // Circuit breaker

  private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
  {
      return HttpPolicyExtensions
          .HandleTransientHttpError()
          .WaitAndRetryAsync(3, retryAttempt =>
              TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
  }

  Example with Different APIs

  // Register multiple APIs in DependencyInjection.cs or Program.cs
  public static class ServiceCollectionExtensions
  {
      public static IServiceCollection AddExternalApiClients(
          this IServiceCollection services,
          IConfiguration configuration)
      {
          // PMS Planning API
          services.AddRefitClient<IPmsPlanningApi>()
              .ConfigureHttpClient(c => c.BaseAddress = new Uri(configuration["PmsPlanningPull:BaseUrl"]!))
              .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
                  sp.GetRequiredService<IConfiguration>(),
                  configurationKey: "PmsPlanningPull:ApiKey",
                  queryParameterName: "apikey"
              ));

          // Weather API
          services.AddRefitClient<IWeatherApi>()
              .ConfigureHttpClient(c => c.BaseAddress = new Uri(configuration["WeatherApi:BaseUrl"]!))
              .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
                  sp.GetRequiredService<IConfiguration>(),
                  configurationKey: "WeatherApi:Key",
                  queryParameterName: "key"
              ));

          // Google Maps API
          services.AddRefitClient<IGoogleMapsApi>()
              .ConfigureHttpClient(c => c.BaseAddress = new Uri("https://maps.googleapis.com"))
              .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
                  sp.GetRequiredService<IConfiguration>(),
                  configurationKey: "GoogleMaps:ApiKey",
                  queryParameterName: "key"
              ));

          return services;
      }
  }

  The handler will automatically append the API key to all requests made through the Refit client, so you don't need
   to manually add it to each method call.