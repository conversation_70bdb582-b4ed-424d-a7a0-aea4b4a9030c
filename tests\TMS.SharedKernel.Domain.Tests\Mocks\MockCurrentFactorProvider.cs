using System.Security.Claims;
using TMS.SharedKernel.Domain.Enums;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.SharedKernel.Domain.Tests.Mocks;

/// <summary>
/// Mock implementation of ICurrentFactorProvider for testing
/// </summary>
public class MockCurrentFactorProvider : ICurrentFactorProvider
{
    public Guid CurrentFactorId { get; set; } = Guid.NewGuid();
    public string CurrentFactorName { get; set; } = "Test User";
    public Guid CompanyId { get; set; } = Guid.Empty;
    public Guid DepartmentId { get; set; } = Guid.Empty;
    public string Roles { get; set; } = string.Empty;
    public ClientType ClientType { get; set; } = ClientType.Unknown;
    public bool IsAdmin { get; set; } = false;
    public string DepartmentCode { get; set; } = string.Empty;
    public ClaimsPrincipal? CurrentUser { get; set; } = null;
}
