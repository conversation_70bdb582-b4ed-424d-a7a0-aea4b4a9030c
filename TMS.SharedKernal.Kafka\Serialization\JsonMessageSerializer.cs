﻿
using TMS.SharedKernal.Kafka.Abstractions;
using Newtonsoft.Json;

namespace TMS.SharedKernal.Kafka.Services;

/// <summary>
/// JsonMessageSerializer
/// </summary>
public class JsonMessageSerializer : IMessageSerializer
{
    private readonly JsonSerializerSettings _options;

    /// <summary>
    /// JsonMessageSerializer
    /// </summary>
    public JsonMessageSerializer()
    {
        _options = new JsonSerializerSettings
        {
            Formatting = Formatting.Indented,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };
    }

    public string Serialize<T>(T message) where T : class
    {
        return JsonConvert.SerializeObject(message, _options);
    }

    public T Deserialize<T>(string message) where T : class
    {
        return JsonConvert.DeserializeObject<T>(message, _options);
    }

    public object Deserialize(string message, Type type)
    {
        return JsonConvert.DeserializeObject(message, type, _options);
    }
}
