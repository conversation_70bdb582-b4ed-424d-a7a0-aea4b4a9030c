﻿using System.Security;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;

namespace TMS.SharedKernel.Authentication;

/// <summary>
/// PermissionRegister
/// </summary>
public static class PermissionRegister
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="services"></param>
    /// <param name="permissions"></param>
    public static void AddPolicyBasedAuthorization(this IServiceCollection services, IEnumerable<string> permissions)
    {
        services.AddScoped<IAuthorizationHandler, PermissionHandler>();

        services.AddAuthorization(options =>
        {
            options.FallbackPolicy = new AuthorizationPolicyBuilder()
                .AddRequirements(new PermissionRequirement())
                .Build();

            foreach (var permission in permissions)
            {
                options.AddPolicy(permission, policy => policy.Requirements.Add(new PermissionRequirement(permission)));
            }
        });
    }
}
