﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.Kafka.Configuration;
using TMS.SharedKernal.Kafka.Services;

namespace TMS.SharedKernal.Kafka.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddMessageHandler<TMessage, THandler>(this IServiceCollection services)
        where TMessage : class, IMessage
        where THandler : class, IMessageHandler<TMessage>
    {
        ArgumentNullException.ThrowIfNull(services);

        // Register both interface and concrete type
        services.AddScoped<IMessageHandler<TMessage>, THandler>();
        services.AddScoped<THandler>();
        return services;
    }

    // Add keyed message handler for specific service keys
    public static IServiceCollection AddKeyedMessageHandler<TMessage, THandler>(
        this IServiceCollection services,
        string serviceKey)
        where TMessage : class, IMessage
        where THandler : class, IMessageHandler<TMessage>
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentException.ThrowIfNullOrWhiteSpace(serviceKey);

        services.AddKeyedScoped<IMessageHandler<TMessage>, THandler>(serviceKey);
        services.AddScoped<THandler>(); // Concrete type still registered normally
        return services;
    }
}

public class KafkaFlowBuilder
{
    private readonly IServiceCollection _services;
    private readonly KafkaFlowOptions _options;
    private readonly string _serviceKey;

    public KafkaFlowBuilder(IServiceCollection services, string serviceKey = "Default")
    {
        _services = services ?? throw new ArgumentNullException(nameof(services));
        _serviceKey = serviceKey ?? throw new ArgumentNullException(nameof(serviceKey));
        _options = new KafkaFlowOptions
        {
            Security = new SecurityOptions(),
            Producer = new ProducerOptions(),
            Consumer = new ConsumerOptions(),
            DeadLetter = new DeadLetterOptions(),
            Topics = new List<TopicConfiguration>()
        };
    }

    public KafkaFlowBuilder WithBootstrapServers(string servers)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(servers);
        _options.BootstrapServers = servers;
        return this;
    }

    public KafkaFlowBuilder WithSaslAuthentication(string username, string password, string mechanism = "PLAIN")
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(username);
        ArgumentException.ThrowIfNullOrWhiteSpace(password);

        _options.Security.Sasl.Enabled = true;
        _options.Security.Sasl.Username = username;
        _options.Security.Sasl.Password = password;
        _options.Security.Sasl.Mechanism = mechanism;
        return this;
    }

    public KafkaFlowBuilder WithSsl(string caLocation = null, string certLocation = null, string keyLocation = null, string keyPassword = null)
    {
        _options.Security.EnableSsl = true;
        if (!string.IsNullOrEmpty(caLocation))
            _options.Security.Ssl.CaLocation = caLocation;
        if (!string.IsNullOrEmpty(certLocation))
            _options.Security.Ssl.CertificateLocation = certLocation;
        if (!string.IsNullOrEmpty(keyLocation))
            _options.Security.Ssl.KeyLocation = keyLocation;
        if (!string.IsNullOrEmpty(keyPassword))
            _options.Security.Ssl.KeyPassword = keyPassword;
        return this;
    }

    public KafkaFlowBuilder WithSaslSsl(string username, string password, string caLocation, string mechanism = "PLAIN")
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(username);
        ArgumentException.ThrowIfNullOrWhiteSpace(password);
        ArgumentException.ThrowIfNullOrWhiteSpace(caLocation);

        _options.Security.Sasl.Enabled = true;
        _options.Security.Sasl.Username = username;
        _options.Security.Sasl.Password = password;
        _options.Security.Sasl.Mechanism = mechanism;
        _options.Security.EnableSsl = true;
        _options.Security.Ssl.CaLocation = caLocation;
        return this;
    }

    public KafkaFlowBuilder AddTopic<TMessage, THandler>(
        string topicName,
        string consumerGroup,
        int partitions = 8,
        short replicationFactor = 1,
        bool autoCreate = true)
        where TMessage : class, IMessage
        where THandler : class, IMessageHandler<TMessage>
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(topicName);
        ArgumentException.ThrowIfNullOrWhiteSpace(consumerGroup);

        _options.Topics.Add(new TopicConfiguration
        {
            TopicName = topicName,
            ConsumerGroup = consumerGroup,
            MessageType = typeof(TMessage),
            HandlerType = typeof(THandler),
            Partitions = partitions,
            ReplicationFactor = replicationFactor,
            AutoCreateTopic = autoCreate
        });

        // Register message handler for this service key
        if (_serviceKey == "Default")
        {
            _services.AddMessageHandler<TMessage, THandler>();
        }
        else
        {
            _services.AddKeyedMessageHandler<TMessage, THandler>(_serviceKey);
        }

        return this;
    }

    public KafkaFlowBuilder EnableAutoCreateTopics(bool enable = true)
    {
        // Enable auto-create for all topics
        foreach (var topic in _options.Topics)
        {
            topic.AutoCreateTopic = enable;
        }
        return this;
    }

    public KafkaFlowBuilder EnableDeadLetter(string topicName = "dead-letter-queue")
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(topicName);

        _options.DeadLetter.Enabled = true;
        _options.DeadLetter.TopicName = topicName;
        return this;
    }

    public KafkaFlowBuilder WithProducerConfig(Action<ProducerOptions> configure)
    {
        ArgumentNullException.ThrowIfNull(configure);
        configure(_options.Producer);
        return this;
    }

    public KafkaFlowBuilder WithConsumerConfig(Action<ConsumerOptions> configure)
    {
        ArgumentNullException.ThrowIfNull(configure);
        configure(_options.Consumer);
        return this;
    }

    public IServiceCollection Build()
    {
        // Validate required options
        if (string.IsNullOrEmpty(_options.BootstrapServers))
        {
            throw new InvalidOperationException("BootstrapServers must be configured before building.");
        }

        // Register the singleton serializer if not already registered
        _services.TryAddSingleton<IMessageSerializer, JsonMessageSerializer>();

        // Register topic manager if any topic has auto-create enabled
        if (_options.Topics.Any(t => t.AutoCreateTopic))
        {
            _services.TryAddSingleton<ITopicManager>(provider =>
            {
                var optionsMonitor = provider.GetRequiredService<IOptionsMonitor<KafkaFlowOptions>>();
                var options = optionsMonitor.Get(_serviceKey);
                var logger = provider.GetRequiredService<ILogger<KafkaTopicManager>>();
                return new KafkaTopicManager(options, logger);
            });
        }

        // Register options for this service key
        _services.Configure<KafkaFlowOptions>(_serviceKey, options =>
        {
            options.BootstrapServers = _options.BootstrapServers;
            options.Topics = _options.Topics;
            options.DeadLetter = _options.DeadLetter;
            options.Producer = _options.Producer;
            options.Consumer = _options.Consumer;
            options.Security = _options.Security;
        });

        // Add validation
        _services.AddOptionsWithValidateOnStart<KafkaFlowOptions>(_serviceKey)
               .Validate(options => !string.IsNullOrEmpty(options.BootstrapServers), "BootstrapServers is required");

        // Register keyed services for this service key
        _services.AddKeyedSingleton<IMessageProducer>(_serviceKey, (provider, key) =>
        {
            var optionsMonitor = provider.GetRequiredService<IOptionsMonitor<KafkaFlowOptions>>();
            var options = optionsMonitor.Get(_serviceKey);
            var eventSerializer = provider.GetRequiredService<IMessageSerializer>();
            var logger = provider.GetRequiredService<ILogger<KafkaMessageProducer>>();

            try
            {
                return new KafkaMessageProducer(options, eventSerializer, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create KafkaMessageProducer for key: {ServiceKey}", _serviceKey);
                throw;
            }
        });

        _services.AddSingleton<IHostedService>(provider => CreateKafkaConsumer(provider, _serviceKey));

        return _services;
    }

    public IServiceCollection BuildProducer()
    {
        // Validate required options
        if (string.IsNullOrEmpty(_options.BootstrapServers))
        {
            throw new InvalidOperationException("BootstrapServers must be configured before building.");
        }

        // Register the singleton serializer if not already registered
        _services.TryAddSingleton<IMessageSerializer, JsonMessageSerializer>();

        // Register options for this service key
        _services.Configure<KafkaFlowOptions>(_serviceKey, options =>
        {
            options.BootstrapServers = _options.BootstrapServers;
            options.Topics = _options.Topics;
            options.DeadLetter = _options.DeadLetter;
            options.Producer = _options.Producer;
            options.Consumer = _options.Consumer;
            options.Security = _options.Security;
        });

        // Add validation
        _services.AddOptionsWithValidateOnStart<KafkaFlowOptions>(_serviceKey)
               .Validate(options => !string.IsNullOrEmpty(options.BootstrapServers), "BootstrapServers is required");

        // Register keyed services for this service key
        _services.AddKeyedSingleton<IMessageProducer>(_serviceKey, (provider, key) =>
        {
            var optionsMonitor = provider.GetRequiredService<IOptionsMonitor<KafkaFlowOptions>>();
            var options = optionsMonitor.Get(_serviceKey);
            var eventSerializer = provider.GetRequiredService<IMessageSerializer>();
            var logger = provider.GetRequiredService<ILogger<KafkaMessageProducer>>();

            try
            {
                return new KafkaMessageProducer(options, eventSerializer, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create KafkaMessageProducer for key: {ServiceKey}", _serviceKey);
                throw;
            }
        });

        return _services;
    }

    public IServiceCollection BuildConsumer()
    {
        // Validate required options
        if (string.IsNullOrEmpty(_options.BootstrapServers))
        {
            throw new InvalidOperationException("BootstrapServers must be configured before building.");
        }

        // Register the singleton serializer if not already registered
        _services.TryAddSingleton<IMessageSerializer, JsonMessageSerializer>();

        // Register topic manager if any topic has auto-create enabled
        if (_options.Topics.Any(t => t.AutoCreateTopic))
        {
            _services.TryAddSingleton<ITopicManager>(provider =>
            {
                var optionsMonitor = provider.GetRequiredService<IOptionsMonitor<KafkaFlowOptions>>();
                var options = optionsMonitor.Get(_serviceKey);
                var logger = provider.GetRequiredService<ILogger<KafkaTopicManager>>();
                return new KafkaTopicManager(options, logger);
            });
        }

        // Register options for this service key
        _services.Configure<KafkaFlowOptions>(_serviceKey, options =>
        {
            options.BootstrapServers = _options.BootstrapServers;
            options.Topics = _options.Topics;
            options.DeadLetter = _options.DeadLetter;
            options.Producer = _options.Producer;
            options.Consumer = _options.Consumer;
            options.Security = _options.Security;
        });

        // Add validation
        _services.AddOptionsWithValidateOnStart<KafkaFlowOptions>(_serviceKey)
               .Validate(options => !string.IsNullOrEmpty(options.BootstrapServers), "BootstrapServers is required");

        _services.AddSingleton<IHostedService>(provider => CreateKafkaConsumer(provider, _serviceKey));

        return _services;
    }

    private KafkaMessageConsumer CreateKafkaConsumer(IServiceProvider provider, string serviceKey)
    {
        var optionsMonitor = provider.GetRequiredService<IOptionsMonitor<KafkaFlowOptions>>();
        var options = optionsMonitor.Get(serviceKey);
        var eventSerializer = provider.GetRequiredService<IMessageSerializer>();
        var logger = provider.GetRequiredService<ILogger<KafkaMessageConsumer>>();

        // Get topic manager if registered
        var topicManager = provider.GetService<ITopicManager>();

        try
        {
            return new KafkaMessageConsumer(
                provider,
                options,
                eventSerializer,
                logger,
                serviceKey,
                topicManager);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create KafkaMessageConsumer for ServiceKey: {ServiceKey}"
                , serviceKey);
            throw;
        }
    }
}

public static class KafkaFlowServiceCollectionExtensions
{
    public static KafkaFlowBuilder AddKafkaFlowBuilder(this IServiceCollection services, string serviceKey = "Default")
    {
        return new KafkaFlowBuilder(services, serviceKey);
    }

    // Extension for multiple builders with different service keys
    public static IServiceCollection AddKafkaFlowBuilders(
        this IServiceCollection services,
        params (string serviceKey, Action<KafkaFlowBuilder> configure)[] configurations)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configurations);

        foreach (var (serviceKey, configure) in configurations)
        {
            var builder = services.AddKafkaFlowBuilder(serviceKey);
            configure(builder);
            builder.BuildProducer();
        }

        return services;
    }
}
