﻿using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides methods to redact sensitive information from log messages
/// </summary>
[ExcludeFromCodeCoverage]
public static partial class SensitiveDataRedactor
{
    private const string RedactedPlaceholder = "[REDACTED]";

    /// <summary>
    /// Patterns for detecting sensitive data in logs
    /// </summary>
    private static readonly List<(Regex Pattern, string Name)> SensitivePatterns = new()
    {
        // JWT tokens (both in query strings and raw)
        (JwtTokenRegex(), "JWT Token"),

        // Generic access_token parameter
        (AccessTokenRegex(), "Access Token"),

        // Bearer tokens
        (BearerTokenRegex(), "Bearer Token"),

        // API keys
        (ApiKeyRegex(), "API Key"),

        // Passwords in various formats
        (PasswordRegex(), "Password"),

        // Credit card numbers (basic pattern)
        (CreditCardRegex(), "Credit Card"),

        // Email addresses (optional - uncomment if needed)
        (EmailRegex(), "Email"),

        // Connection strings
        (ConnectionStringRegex(), "Connection String"),

        // Private keys
        (PrivateKeyRegex(), "Private Key"),

        // Authorization headers
        (AuthHeaderRegex(), "Authorization Header"),

        // Session IDs
        (SessionIdRegex(), "Session ID"),

        // GUID-like secrets in common parameter names
        (SecretGuidRegex(), "Secret GUID")
    };

    /// <summary>
    /// Redacts sensitive information from a log message
    /// </summary>
    /// <param name="message">The message to redact</param>
    /// <returns>The redacted message</returns>
    public static string RedactSensitiveData(string message)
    {
        if (string.IsNullOrEmpty(message))
            return message;

        var redactedMessage = message;

        foreach (var (pattern, name) in SensitivePatterns)
        {
            if (pattern.IsMatch(redactedMessage))
            {
                redactedMessage = pattern.Replace(redactedMessage, match =>
                {
                    // Keep the parameter name but redact the value
                    if (match.Groups.Count > 1 && match.Groups[1].Success)
                    {
                        return $"{match.Groups[1].Value}{RedactedPlaceholder}";
                    }
                    return RedactedPlaceholder;
                });
            }
        }

        return redactedMessage;
    }

    /// <summary>
    /// Redacts sensitive data from an object (for structured logging)
    /// </summary>
    /// <param name="obj">The object to redact</param>
    /// <returns>The redacted object</returns>
    public static object? RedactSensitiveData(object? obj)
    {
        if (obj == null)
            return null;

        if (obj is string str)
            return RedactSensitiveData(str);

        // Handle dictionaries
        if (obj is IDictionary<string, object> dict)
        {
            var redactedDict = new Dictionary<string, object>();
            foreach (var kvp in dict)
            {
                if (IsSensitiveKey(kvp.Key))
                {
                    redactedDict[kvp.Key] = RedactedPlaceholder;
                }
                else
                {
                    redactedDict[kvp.Key] = RedactSensitiveData(kvp.Value) ?? kvp.Value;
                }
            }
            return redactedDict;
        }

        return obj;
    }

    /// <summary>
    /// Checks if a dictionary key name suggests sensitive data
    /// </summary>
    private static bool IsSensitiveKey(string key)
    {
        var lowerKey = key.ToLowerInvariant();
        return lowerKey.Contains("password") ||
               lowerKey.Contains("token") ||
               lowerKey.Contains("secret") ||
               lowerKey.Contains("apikey") ||
               lowerKey.Contains("api_key") ||
               lowerKey.Contains("authorization") ||
               lowerKey.Contains("auth") ||
               lowerKey.Contains("credential") ||
               lowerKey.Contains("connectionstring") ||
               lowerKey.Contains("privatekey") ||
               lowerKey.Contains("private_key") ||
               lowerKey.Contains("access_token") ||
               lowerKey.Contains("refresh_token") ||
               lowerKey.Contains("session") ||
               lowerKey.Contains("cookie");
    }

    #region Regex Patterns

    // JWT Token Pattern - matches standard JWT format (header.payload.signature)
    [GeneratedRegex(@"(access_token=|token=|jwt=)?eyJ[A-Za-z0-9_-]+\.eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex JwtTokenRegex();

    // Access Token Pattern - matches access_token parameter
    [GeneratedRegex(@"(access_token=)[A-Za-z0-9_\-\.]+", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex AccessTokenRegex();

    // Bearer Token Pattern
    [GeneratedRegex(@"(Bearer\s+)[A-Za-z0-9_\-\.=]+", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex BearerTokenRegex();

    // API Key Pattern - matches common API key formats
    [GeneratedRegex(@"(api[_-]?key[=:\s]+['""]?)[A-Za-z0-9_\-]{20,}", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex ApiKeyRegex();

    // Password Pattern - matches password parameters
    [GeneratedRegex(@"(password[=:\s]+['""]?)[^\s&;'""]+", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex PasswordRegex();

    // Credit Card Pattern - basic pattern for credit card numbers
    [GeneratedRegex(@"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b", RegexOptions.Compiled)]
    private static partial Regex CreditCardRegex();

    // Email Pattern (optional)
    [GeneratedRegex(@"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", RegexOptions.Compiled)]
    private static partial Regex EmailRegex();

    // Connection String Pattern
    [GeneratedRegex(@"(Server|Data Source|Initial Catalog|User ID|Password|Integrated Security|Trusted_Connection)=[^;]+", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex ConnectionStringRegex();

    // Private Key Pattern
    [GeneratedRegex(@"-----BEGIN\s+(?:RSA\s+)?PRIVATE\s+KEY-----[\s\S]+?-----END\s+(?:RSA\s+)?PRIVATE\s+KEY-----", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex PrivateKeyRegex();

    // Authorization Header Pattern
    [GeneratedRegex(@"(Authorization[:\s]+)[^\r\n]+", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex AuthHeaderRegex();

    // Session ID Pattern
    [GeneratedRegex(@"(session[_-]?id[=:\s]+['""]?)[A-Za-z0-9_\-]{20,}", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex SessionIdRegex();

    // Secret GUID Pattern - matches common secret parameter names with GUID values
    [GeneratedRegex(@"(secret[=:\s]+['""]?|client[_-]?secret[=:\s]+['""]?)[A-Fa-f0-9\-]{20,}", RegexOptions.IgnoreCase | RegexOptions.Compiled)]
    private static partial Regex SecretGuidRegex();

    #endregion
}
