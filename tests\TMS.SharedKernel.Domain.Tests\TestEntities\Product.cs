using TMS.SharedKernel.Domain.Entities;

namespace TMS.SharedKernel.Domain.Tests.TestEntities;

/// <summary>
/// Test entity for self-referencing scenarios
/// </summary>
public class Product : AuditableEntity
{
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;

    // Self-referencing foreign key
    public Guid? ParentProductId { get; set; }
    public Product? ParentProduct { get; set; }

    // Multiple foreign keys scenario
    public Guid? CategoryId { get; set; }
    public Category? Category { get; set; }
}
