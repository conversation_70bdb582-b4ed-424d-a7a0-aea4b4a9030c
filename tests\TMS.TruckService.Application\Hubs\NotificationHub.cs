﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using TMS.TruckService.Application;

namespace TMS.NotificationService.Application.Hubs;

[Authorize(Policy = PermissionDefinition.NotificationPolicy)]
public class NotificationHub : Hub
{
    private readonly ILogger<NotificationHub> _logger;

    public NotificationHub(ILogger<NotificationHub> logger)
    {
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        var groupName = $"user-{userId}";

        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Connection {ConnectionId} joined group {GroupName}", Context.ConnectionId, groupName);

        await base.OnConnectedAsync();
    }

    private string? GetUserId()
    {
        return Context.User?.FindFirst("employeeId")?.Value
               ?? Context.User?.FindFirst("sub")?.Value
               ?? Context.User?.FindFirst("id")?.Value
               ?? Context.User?.FindFirst("user_id")?.Value
               ?? Context.UserIdentifier; // SignalR's default user identifier
    }

    public override async Task OnDisconnectedAsync(Exception exception)
    {
        _logger.LogInformation("Client disconnected: {ConnectionId}, Exception: {Exception}",
            Context.ConnectionId, exception?.Message);

        var userId = GetUserId();
        var groupName = $"user-{userId}";

        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

        _logger.LogInformation("Connection {ConnectionId} left group {GroupName}", Context.ConnectionId, groupName);

        await base.OnDisconnectedAsync(exception);
    }
}
