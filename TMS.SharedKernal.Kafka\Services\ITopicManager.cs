namespace TMS.SharedKernal.Kafka.Services;

/// <summary>
/// Interface for managing Kafka topics (creation, validation, etc.)
/// </summary>
public interface ITopicManager
{
    /// <summary>
    /// Ensures that a topic exists. Creates it if it doesn't exist and auto-creation is enabled.
    /// </summary>
    /// <param name="topicName">The name of the topic</param>
    /// <param name="partitions">Number of partitions (default: 1)</param>
    /// <param name="replicationFactor">Replication factor (default: 1)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if topic exists or was created successfully</returns>
    Task<bool> EnsureTopicExistsAsync(
        string topicName,
        int partitions = 1,
        short replicationFactor = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates all configured topics including main topics, retry topics, and DLQ
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    Task CreateAllTopicsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a topic exists
    /// </summary>
    /// <param name="topicName">The name of the topic</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if topic exists</returns>
    Task<bool> TopicExistsAsync(string topicName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets metadata about a topic
    /// </summary>
    /// <param name="topicName">The name of the topic</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Topic metadata or null if topic doesn't exist</returns>
    Task<TopicMetadata?> GetTopicMetadataAsync(string topicName, CancellationToken cancellationToken = default);
}

/// <summary>
/// Metadata about a Kafka topic
/// </summary>
public class TopicMetadata
{
    public string TopicName { get; set; } = string.Empty;
    public int Partitions { get; set; }
    public short ReplicationFactor { get; set; }
    public Dictionary<string, string> Config { get; set; } = new();
}
