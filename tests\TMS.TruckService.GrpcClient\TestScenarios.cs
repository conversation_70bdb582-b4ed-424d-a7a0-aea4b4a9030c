using TMS.TruckService.Api.Grpc;

namespace TMS.TruckService.GrpcClient;

/// <summary>
/// Test scenarios for demonstrating gRPC client usage
/// </summary>
public static class TestScenarios
{
    /// <summary>
    /// Test 1: Get existing order
    /// </summary>
    public static async Task TestGetOrder(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 1: Get Existing Order ===");

        try
        {
            var reply = await client.GetOrderAsync(1);

            if (reply.Success)
            {
                Console.WriteLine("✓ Success!");
                DisplayOrder(reply.Order);
            }
            else
            {
                Console.WriteLine($"✗ Failed: {reply.ErrorMessage} (Code: {reply.ErrorCode})");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 2: Get non-existent order (error handling)
    /// </summary>
    public static async Task TestGetNonExistentOrder(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 2: Get Non-Existent Order (Error Handling) ===");

        try
        {
            var reply = await client.GetOrderAsync(999);

            if (reply.Success)
            {
                Console.WriteLine("✓ Success!");
                DisplayOrder(reply.Order);
            }
            else
            {
                Console.WriteLine($"✗ Failed: {reply.ErrorMessage} (Code: {reply.ErrorCode})");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 3: Create new order
    /// </summary>
    public static async Task TestCreateOrder(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 3: Create New Order ===");

        try
        {
            var reply = await client.CreateOrderAsync(
                customerName: "John Doe",
                amount: 2500.50,
                description: "Test order from gRPC client"
            );

            if (reply.Success)
            {
                Console.WriteLine("✓ Order created successfully!");
                DisplayOrder(reply.Order);
            }
            else
            {
                Console.WriteLine($"✗ Failed: {reply.ErrorMessage} (Code: {reply.ErrorCode})");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 4: Create order with validation error
    /// </summary>
    public static async Task TestCreateOrderValidation(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 4: Create Order - Validation Error ===");

        try
        {
            // This should fail - empty customer name
            var reply = await client.CreateOrderAsync(
                customerName: "",
                amount: 100.00
            );

            if (reply.Success)
            {
                Console.WriteLine("✓ Order created (unexpected)");
                DisplayOrder(reply.Order);
            }
            else
            {
                Console.WriteLine($"✗ Failed as expected: {reply.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✓ Validation error caught as expected: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 5: List orders with pagination
    /// </summary>
    public static async Task TestListOrders(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 5: List Orders (Pagination) ===");

        try
        {
            var reply = await client.ListOrdersAsync(
                pageNumber: 1,
                pageSize: 10
            );

            if (reply.Success)
            {
                Console.WriteLine($"✓ Retrieved {reply.Orders.Count} orders");
                Console.WriteLine($"  Page {reply.PageNumber} of {reply.TotalPages}");
                Console.WriteLine($"  Total orders: {reply.TotalCount}");
                Console.WriteLine($"  Has next page: {reply.HasNextPage}");
                Console.WriteLine($"  Has previous page: {reply.HasPreviousPage}");

                Console.WriteLine("\nOrders:");
                foreach (var order in reply.Orders)
                {
                    Console.WriteLine($"  - {order.OrderNumber}: {order.CustomerName} - ${order.Amount} ({order.Status})");
                }
            }
            else
            {
                Console.WriteLine($"✗ Failed: {reply.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 6: Update order status
    /// </summary>
    public static async Task TestUpdateOrderStatus(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 6: Update Order Status ===");

        try
        {
            var reply = await client.UpdateOrderStatusAsync(
                orderId: 1,
                newStatus: "Completed",
                notes: "Order fulfilled successfully from client"
            );

            if (reply.Success)
            {
                Console.WriteLine("✓ Order status updated successfully!");
            }
            else
            {
                Console.WriteLine($"✗ Failed: {reply.ErrorMessage} (Code: {reply.ErrorCode})");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 7: Delete order
    /// </summary>
    public static async Task TestDeleteOrder(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 7: Delete Order ===");

        try
        {
            // First create an order to delete
            var createReply = await client.CreateOrderAsync(
                customerName: "Temp User",
                amount: 50.00,
                description: "Order to be deleted"
            );

            if (createReply.Success && createReply.Order != null)
            {
                var orderToDelete = createReply.Order.OrderId;
                Console.WriteLine($"Created order {createReply.Order.OrderNumber} to delete");

                // Now delete it
                var deleteReply = await client.DeleteOrderAsync(orderToDelete);

                if (deleteReply.Success)
                {
                    Console.WriteLine($"✓ Order {createReply.Order.OrderNumber} deleted successfully!");
                }
                else
                {
                    Console.WriteLine($"✗ Delete failed: {deleteReply.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Test 8: Concurrent requests
    /// </summary>
    public static async Task TestConcurrentRequests(IOrderServiceClient client)
    {
        Console.WriteLine("\n=== Test 8: Concurrent Requests (Performance) ===");

        var tasks = new List<Task<CreateOrderReply>>();
        var startTime = DateTime.UtcNow;

        // Create 10 orders concurrently
        for (int i = 1; i <= 10; i++)
        {
            var task = client.CreateOrderAsync(
                customerName: $"Customer {i}",
                amount: i * 100.0,
                description: $"Concurrent order {i}"
            );
            tasks.Add(task);
        }

        try
        {
            var results = await Task.WhenAll(tasks);
            var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;

            var successCount = results.Count(r => r.Success);
            Console.WriteLine($"✓ Created {successCount} orders concurrently in {duration:F2}ms");
            Console.WriteLine($"  Average: {duration / tasks.Count:F2}ms per order");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Exception: {ex.Message}");
        }
    }

    /// <summary>
    /// Run all tests in sequence
    /// </summary>
    public static async Task RunAllTests(IOrderServiceClient client)
    {
        Console.WriteLine("\n╔════════════════════════════════════════════════╗");
        Console.WriteLine("║    Running All gRPC Client Tests              ║");
        Console.WriteLine("╚════════════════════════════════════════════════╝");

        await TestGetOrder(client);
        await Task.Delay(500);

        await TestGetNonExistentOrder(client);
        await Task.Delay(500);

        await TestCreateOrder(client);
        await Task.Delay(500);

        await TestCreateOrderValidation(client);
        await Task.Delay(500);

        await TestListOrders(client);
        await Task.Delay(500);

        await TestUpdateOrderStatus(client);
        await Task.Delay(500);

        await TestDeleteOrder(client);
        await Task.Delay(500);

        await TestConcurrentRequests(client);

        Console.WriteLine("\n✓ All tests completed!");
    }

    /// <summary>
    /// Helper method to display order details
    /// </summary>
    private static void DisplayOrder(OrderDto? order)
    {
        if (order == null)
        {
            Console.WriteLine("  (No order data)");
            return;
        }

        Console.WriteLine($"  Order ID: {order.OrderId}");
        Console.WriteLine($"  Order Number: {order.OrderNumber}");
        Console.WriteLine($"  Customer: {order.CustomerName}");
        Console.WriteLine($"  Amount: ${order.Amount:F2}");
        Console.WriteLine($"  Status: {order.Status}");
        Console.WriteLine($"  Description: {order.Description}");
        Console.WriteLine($"  Created At: {order.CreatedAt}");
        Console.WriteLine($"  Created By: {order.CreatedBy}");
    }
}
