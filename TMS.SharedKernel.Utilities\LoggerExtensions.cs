﻿using System.Diagnostics.CodeAnalysis;
using System.Text;
using Elyspio.Utils.Telemetry.MongoDB.Extensions;
using Elyspio.Utils.Telemetry.Redis.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Npgsql;
using OpenTelemetry;
using OpenTelemetry.Exporter;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for logging.
/// </summary>
[ExcludeFromCodeCoverage]
public static partial class LoggerExtensions
{
    /// <summary>
    /// AddCentralLogs
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="otlpEndpoint"></param>
    /// <param name="env"></param>
    /// <param name="serviceName"></param>
    /// <param name="serviceVersion"></param>
    public static void AddCentralLogs(this WebApplicationBuilder builder, string otlpEndpoint, string env, string serviceName, string serviceVersion)
    {
        builder.AddCentralLogs(otlpEndpoint, key: string.Empty, env, serviceName, serviceVersion,
            protocol: Serilog.Sinks.OpenTelemetry.OtlpProtocol.HttpProtobuf);
    }

    /// <summary>
    /// AddCentralLogs
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="baseEndpoint"></param>
    /// <param name="key"></param>
    /// <param name="env"></param>
    /// <param name="serviceName"></param>
    /// <param name="serviceVersion"></param>
    /// <param name="protocol"></param>
    public static void AddCentralLogs(this WebApplicationBuilder builder,
        string baseEndpoint,
        string key,
        string env,
        string serviceName,
        string serviceVersion,
        Serilog.Sinks.OpenTelemetry.OtlpProtocol protocol)
    {
        var otlpEndpoint = baseEndpoint.TrimEnd('/');
        var proto = protocol == Serilog.Sinks.OpenTelemetry.OtlpProtocol.HttpProtobuf ?
            OtlpExportProtocol.HttpProtobuf : OtlpExportProtocol.Grpc;

        // Basic Authentication following standard format
        var base64Auth = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{key}"));
        var authHeader = $"Basic {base64Auth}";

        var hostName = $"{Environment.MachineName}";
        var instanceId = $"{Environment.MachineName}-{Environment.ProcessId}";
        var resAttributes = new Dictionary<string, object>
        {
            ["service.signature"] = "PH.",
            ["service.name"] = serviceName,
            ["service.version"] = serviceVersion,
            ["deployment.environment"] = env,
            ["host.name"] = hostName,
            ["service.instance.id"] = instanceId
        };

        var attributes = resAttributes.Select(kvp => new KeyValuePair<string, object>(kvp.Key, kvp.Value));

        var resourceBuilder = ResourceBuilder.CreateDefault()
            .AddService(
                serviceName: serviceName,
                serviceVersion: serviceVersion,
                serviceInstanceId: instanceId)
            .AddAttributes(attributes);

        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.InputEncoding = System.Text.Encoding.UTF8;
        
        Log.Logger = new LoggerConfiguration()
            .Enrich.FromLogContext()
            // "attributes": {
            // this one for attributes and ignore ambiguos name
            //},
            .Enrich.WithProperty("ServiceName", serviceName)
            .Enrich.WithProperty("Environment", env)
            .Enrich.WithProperty("HostName", hostName)
            .Enrich.WithSensitiveDataRedaction()
            .WriteTo.Console(
                outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}",
                theme: Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Code)
            .WriteTo.OpenTelemetry(opts =>
            {
                opts.Endpoint = $"{otlpEndpoint}/v1/logs";
                opts.Protocol = protocol;
                opts.Headers = new Dictionary<string, string>()
                {
                    { "Authorization", authHeader }
                };
                opts.ResourceAttributes = resAttributes;
            })
            .CreateLogger();

        Log.Information($"🚛 {serviceName} {serviceVersion} 🚛");

        // ===== CONFIGURE LOGGING PROVIDERS =====
        builder.Logging.ClearProviders();

        // ----- Configure Native OpenTelemetry Logging for SigNoz -----
        // OpenTelemetry will run alongside Serilog - Serilog handles console, OpenTelemetry handles SigNoz
        // Resource attributes (service.name, deployment.environment, etc.) are automatically included
        builder.Logging.AddOpenTelemetry(options =>
        {
            options.SetResourceBuilder(resourceBuilder);
            options.IncludeFormattedMessage = true;
            options.IncludeScopes = true;

            // Resource attributes (service.name, deployment.environment, etc.) are automatically included
            // No manual enrichment needed - this fixes the SigNoz filtering issue!

            options.AddOtlpExporter((o) =>
            {
                o.Endpoint = new Uri($"{otlpEndpoint}/v1/logs");
                o.Protocol = proto;
                o.Headers = $"Authorization={authHeader}";
                o.ExportProcessorType = OpenTelemetry.ExportProcessorType.Batch;
            });
        });

        // ----- Add OpenTelemetry tracing and metrics with batching -----
        builder.Services.AddOpenTelemetry()
            .WithTracing(tracerProviderBuilder =>
            {
                tracerProviderBuilder
                    .SetResourceBuilder(resourceBuilder)
                    .AddAppMongoInstrumentation() // Elyspio.Utils.MongoDB
                    .AddAppRedisInstrumentation() // Elyspio.Utils.Redis
                    .AddAspNetCoreInstrumentation(options =>
                    {
                        options.RecordException = true;
                        options.EnrichWithHttpRequest = (activity, request) =>
                        {
                            activity.SetTag("http.request.header.user_agent", request.Headers["User-Agent"].ToString());

                            // Redact sensitive data from URL (e.g., access_token in query string)
                            var url = $"{request.Scheme}://{request.Host}{request.Path}{request.QueryString}";
                            var redactedUrl = SensitiveDataRedactor.RedactSensitiveData(url);
                            if (redactedUrl != url)
                            {
                                activity.SetTag("http.url.redacted", redactedUrl);
                            }
                        };
                        options.EnrichWithHttpResponse = (activity, response) =>
                        {
                            activity.SetTag("http.response.status_code", response.StatusCode);
                        };
                        options.EnrichWithException = (activity, exception) =>
                        {
                            activity.SetTag("exception.type", exception.GetType().FullName);
                            activity.SetTag("exception.message", exception.Message);
                            activity.SetTag("exception.stacktrace", exception.StackTrace);
                        };
                    })
                    .AddHttpClientInstrumentation(options =>
                    {
                        options.RecordException = true;
                        options.EnrichWithHttpRequestMessage = (activity, request) =>
                        {
                            activity.SetTag("http.request.method", request.Method.Method);
                        };
                    })
                    .AddNpgsql()
                    .AddSource(serviceName) // For custom traces
                    .AddSource("TMS.RabbitMQ.Publisher") // RabbitMQ publisher traces
                    .AddSource("TMS.RabbitMQ.Consumer") // RabbitMQ consumer traces
                    .AddSource("TMS.Kafka.Producer") // Kafka producer traces
                    .AddSource("TMS.Kafka.Consumer") // Kafka consumer traces
                                            // always on 1.0 sampling for better visibility into issues
                                            // Apply sampling based on environment (100% dev, 50% prod for better error coverage)
                                            //.SetSampler(env.Equals("Development", StringComparison.OrdinalIgnoreCase)
                                            //    ? new AlwaysOnSampler()
                                            //    : new TraceIdRatioBasedSampler(0.5))  // 50% sampling in production - balances cost vs error visibility
                    .AddOtlpExporter(o =>
                    {
                        // Configure OTLP endpoint
                        o.Endpoint = new Uri($"{otlpEndpoint}/v1/traces");
                        o.Protocol = proto;
                        o.Headers = $"Authorization={authHeader}";

                        // Configure trace batching for near real-time export
                        // Traces are exported every 5 seconds or when batch reaches 512 spans
                        // Note: By default OpenTelemetry uses BatchExportProcessor with:
                        // - ScheduledDelayMilliseconds = 5000 (5 seconds) - already optimal
                        // - MaxQueueSize = 2048
                        // - MaxExportBatchSize = 512
                        // - ExporterTimeoutMilliseconds = 30000 (30 seconds)
                        o.ExportProcessorType = OpenTelemetry.ExportProcessorType.Batch;
                    });
            })
            .WithMetrics(metricProviderBuilder =>
            {
                metricProviderBuilder
                    .SetResourceBuilder(resourceBuilder)
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddProcessInstrumentation()
                    .AddMeter(serviceName) // For custom metrics
                    .AddOtlpExporter((o, metricReaderOptions) =>
                    {
                        // Configure OTLP endpoint
                        o.Endpoint = new Uri($"{otlpEndpoint}/v1/metrics");
                        o.Protocol = proto;
                        o.Headers = $"Authorization={authHeader}";

                        o.ExportProcessorType = OpenTelemetry.ExportProcessorType.Batch;

                        // Configure metrics collection interval to 30 seconds
                        metricReaderOptions.PeriodicExportingMetricReaderOptions.ExportIntervalMilliseconds = 30000; // 30 seconds
                        metricReaderOptions.PeriodicExportingMetricReaderOptions.ExportTimeoutMilliseconds = 15000;  // 15 seconds timeout
                    });
            });

        builder.Host.UseSerilog();
    }
}
