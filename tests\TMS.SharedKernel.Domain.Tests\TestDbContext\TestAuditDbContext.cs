using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.Domain.Tests.TestEntities;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.SharedKernel.Domain.Tests.TestDbContext;

/// <summary>
/// Test DbContext for verifying audit log interceptor behavior
/// </summary>
public class TestAuditDbContext : BaseDbContext
{
    public TestAuditDbContext(
        DbContextOptions<TestAuditDbContext> options,
        ICurrentFactorProvider currentFactorProvider)
        : base(options, currentFactorProvider)
    {
    }

    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderLine> OrderLines { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<Category> Categories { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Order
        modelBuilder.Entity<Order>(entity =>
        {
            entity.ToTable("Orders");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.TotalAmount).HasPrecision(18, 2);

            entity.HasMany(e => e.OrderLines)
                .WithOne(ol => ol.Order)
                .HasForeignKey(ol => ol.OrderId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // Configure OrderLine
        modelBuilder.Entity<OrderLine>(entity =>
        {
            entity.ToTable("OrderLines");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Quantity).IsRequired();
            entity.Property(e => e.UnitPrice).HasPrecision(18, 2);
        });

        // Configure Category
        modelBuilder.Entity<Category>(entity =>
        {
            entity.ToTable("Categories");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);

            entity.HasMany(e => e.Products)
                .WithOne(p => p.Category)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configure Product
        modelBuilder.Entity<Product>(entity =>
        {
            entity.ToTable("Products");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Code).IsRequired().HasMaxLength(50);

            // Self-referencing relationship
            entity.HasOne(e => e.ParentProduct)
                .WithMany()
                .HasForeignKey(e => e.ParentProductId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }
}
