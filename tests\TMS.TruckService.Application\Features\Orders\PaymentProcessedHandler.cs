﻿using Microsoft.Extensions.Logging;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.TruckService.Domain.Events;

namespace TMS.TruckService.Application.Events;

public class PaymentProcessedHandler : IEventHandler<PaymentProcessedEvent>
{
    private readonly ILogger<PaymentProcessedHandler> _logger;

    public PaymentProcessedHandler(ILogger<PaymentProcessedHandler> logger)
    {
        _logger = logger;
    }

    public async Task<bool> HandleAsync(PaymentProcessedEvent eventData, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing payment {PaymentId} for order {OrderId}",
                eventData.PaymentId, eventData.OrderId);

            // Simulate payment processing
            await Task.Delay(1000, cancellationToken);

            _logger.LogInformation("Payment {PaymentId} processed successfully", eventData.PaymentId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process payment {PaymentId}", eventData.PaymentId);
            return false;
        }
    }
}
