﻿using TMS.SharedKernel.Domain.Entities.Interfaces;

namespace TMS.SharedKernel.Domain.Entities;

/// <summary>
/// Represents an entity that supports audit tracking for creation and modification metadata.
/// Does NOT include tenant isolation by default - implement ITenant interface on specific entities that need multi-tenant isolation.
/// </summary>
public abstract class AuditableEntity : EntityBase, IAuditable
{
    /// <inheritdoc />
    public DateTime CreatedAt { get; set; }

    /// <inheritdoc />
    public DateTime UpdatedAt { get; set; }

    /// <inheritdoc />
    public Guid CreatedBy { get; set; }

    /// <inheritdoc />
    public Guid UpdatedBy { get; set; }
}

/// <summary>
/// Represents an auditable entity with a customizable primary key.
/// Does NOT include tenant isolation by default - implement ITenant interface on specific entities that need multi-tenant isolation.
/// </summary>
public abstract class AuditableEntity<TKey> : EntityBase<TKey>, IAuditable
    where TKey : IComparable, IComparable<TKey>, IEquatable<TKey>
{
    /// <inheritdoc />
    public DateTime CreatedAt { get; set; }

    /// <inheritdoc />
    public DateTime UpdatedAt { get; set; }

    /// <inheritdoc />
    public Guid CreatedBy { get; set; }

    /// <inheritdoc />
    public Guid UpdatedBy { get; set; }
}
