rem call Publish_config.bat
set A1=TMS.SharedKernel.EntityFrameworkCore

@echo ========================================
@echo Publishing %A1% with Obfuscar
@echo ========================================

rem Step 1: Obfuscate the library using Obfuscar
@echo Step 1: Obfuscating library with Obfuscar...
powershell.exe -ExecutionPolicy Bypass -File scripts\obfuscate-obfuscar.ps1 -ProjectPath "%A1%" -ProjectName "%A1%"
if %ERRORLEVEL% NEQ 0 (
    @echo Obfuscation failed!
    @pause
    exit /b 1
)

rem Step 2: Pack the NuGet package (with obfuscated DLL)
@echo.
@echo Step 2: Packing NuGet package...
dotnet pack -c Release %A1%/%A1%.csproj -o packages --no-build
if %ERRORLEVEL% NEQ 0 (
    @echo Packing failed!
    @pause
    exit /b 1
)

@echo.
@echo ========================================
@echo Package published successfully!
@echo ========================================
@echo NOTE: The package contains the OBFUSCATED version
@echo       - Method bodies are obfuscated (strings encrypted, control flow scrambled)
@echo       - All names unchanged (100%% backward compatible)
@echo       - Original DLL backed up with .original extension

rem Uncomment to push to NuGet server
rem dotnet nuget push packages/%A1%.1.5.8.nupkg --api-key %NUGET_KEY_247% --source %NUGET_SOURCE_247%/v3/index.json
