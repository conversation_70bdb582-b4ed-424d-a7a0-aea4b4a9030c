﻿using System.Security.Claims;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Enums;

namespace TMS.SharedKernel.EntityFrameworkCore;

//public class YourQuartzJob : IJob
//{
//    private readonly IQuartzJobContextAccessor _contextAccessor;
//    private readonly YourDbContext _dbContext;

//    public YourQuartzJob(IQuartzJobContextAccessor contextAccessor, YourDbContext dbContext)
//    {
//        _contextAccessor = contextAccessor;
//        _dbContext = dbContext;
//    }

//    public async Task Execute(IJobExecutionContext context)
//    {
//        // Set the job context before any DB operations
//        _contextAccessor.JobContext = new QuartzJobContext
//        {
//            CurrentFactorId = Guid.Parse("system-job-user-id"),
//            CurrentFactorName = "Background Job",
//            CompanyId = Guid.Parse("company-id"),
//            DepartmentId = Guid.Parse("dept-id"),
//            Roles = "System",
//            ClientType = ClientType.Unknown
//        };

//        // Now your DbContext will use the Quartz context for audit fields
//        // Any entities with ICreatedFactorTracking<Guid> or IUpdatedFactorTracking<Guid>
//        // will automatically get the CurrentFactorId from the job context

//        var entity = new YourEntity();
//        await _dbContext.AddAsync(entity);
//        await _dbContext.SaveChangesAsync(); // CreatedBy will be set from job context
//    }
//}
/// <summary>
/// QuartzJobContext
/// </summary>
public class QuartzJobContext : IQuartzJobContext
{
    /// <inheritdoc/>
    public ClaimsPrincipal? CurrentUser { get; set; }
    /// <inheritdoc/>
    public Guid CurrentFactorId { get; set; }
    /// <inheritdoc/>
    public string CurrentFactorName { get; set; } = string.Empty;
    /// <inheritdoc/>
    public Guid CompanyId { get; set; }
    /// <inheritdoc/>
    public Guid DepartmentId { get; set; }
    /// <inheritdoc/>
    public string Roles { get; set; } = string.Empty;
    /// <inheritdoc/>
    public ClientType ClientType { get; set; } = ClientType.Unknown;
}

/// <inheritdoc/>
public class QuartzJobContextAccessor : IQuartzJobContextAccessor
{
    private static readonly AsyncLocal<IQuartzJobContext?> _jobContext = new();

    /// <inheritdoc/>
    public IQuartzJobContext? JobContext
    {
        get => _jobContext.Value;
        set => _jobContext.Value = value;
    }
}
