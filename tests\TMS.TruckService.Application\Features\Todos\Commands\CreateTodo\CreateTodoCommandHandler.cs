﻿using MapsterMapper;
using MediatR;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.TruckService.Contracts.Todos;
using TMS.TruckService.Domain.Entities;

namespace TMS.TruckService.Application.Features.Todos.Commands.CreateTodo;

public class CreateTodoCommandHandler : IRequestHandler<CreateTodoCommand, TodoResponse>
{
    private readonly IBaseRepository<Todo> _todoRepository;
    private readonly IMapper _mapper;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentFactorProvider _currentFactorProvider;

    public CreateTodoCommandHandler(IUnitOfWork unitOfWork, IBaseRepository<Todo> todoRepository, IMapper mapper, ICurrentFactorProvider currentFactorProvider)
    {
        _todoRepository = todoRepository;
        _mapper = mapper;
        _unitOfWork = unitOfWork;
        _currentFactorProvider = currentFactorProvider;
    }

    public async Task<TodoResponse> Handle(CreateTodoCommand request, CancellationToken cancellationToken)
    {
        if (_currentFactorProvider.IsMobile)
        {
        }

        var todo = new Todo
        {
            Title = request.Title,
            Description = request.Description,
            IsCompleted = false,
        };

        //var toids = await _todoRepository.GetIdsAsync(x => x.Id == Guid.NewGuid(), x => x.Id);
        await _todoRepository.AddAsync(todo, cancellationToken);

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return _mapper.Map<TodoResponse>(todo);
    }
}
