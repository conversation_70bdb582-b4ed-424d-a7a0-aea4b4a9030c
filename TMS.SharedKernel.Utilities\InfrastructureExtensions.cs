﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using TMS.SharedKernal.Kafka.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Events.AuditLog;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.Domain.Services.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;
using TMS.SharedKernel.EntityFrameworkCore.Configuration;
using TMS.SharedKernel.EntityFrameworkCore.Interceptors;
using TMS.SharedKernel.EntityFrameworkCore.Services;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for adding repository services to the IServiceCollection.
/// </summary>
[ExcludeFromCodeCoverage]
public static class InfrastructureExtensions
{
    /// <summary>
    /// Extension method to add repository services to the IServiceCollection.
    /// </summary>
    /// <param name="services">The IServiceCollection to add services to.</param>
    /// <returns>The updated IServiceCollection.</returns>
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        return services;
    }

    /// <summary>
    /// public static IServiceCollection AddDerivedDbContext<T>(this IServiceCollection services) where T : BaseDbContext
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="services"></param>
    /// <returns></returns>
    [Obsolete("Use AddInfrastructure or AddInfrastructureAudit instead")]
    public static IServiceCollection AddDerivedDbContext<T>(this IServiceCollection services) where T : BaseDbContext
    {
        services.AddScoped<BaseDbContext>(x => x.GetRequiredService<T>());
        return services;
    }

    /// <summary>
    /// AddInfrastructureAudit
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <param name="interceptorKey"></param>
    /// <param name="connectionKey"></param>
    /// <returns></returns>
    public static IServiceCollection AddInfrastructureAudit<T>(this IServiceCollection services,
        IConfiguration configuration, string interceptorKey, string connectionKey) where T : BaseDbContext
    {
        services.AddScoped<BaseDbContext>(x => x.GetRequiredService<T>());

        services.Configure<InterceptorConfiguration>(configuration.GetSection(interceptorKey));

        // Infrastructure services
        services.AddScoped<AuditContextProvider>();

        // Interceptors - registered as scoped for DI
        services.AddScoped<AuditLogInterceptor>();

        services.AddDbContext<T>((sp, options) =>
        {
            var connectionString = configuration.GetConnectionString(connectionKey);
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(5),
                    errorCodesToAdd: null);
            });

            // Add the interceptor here!
            var interceptor = sp.GetRequiredService<AuditLogInterceptor>();
            options.AddInterceptors(interceptor);
        });

        return services;
    }

    /// <summary>
    /// AddKafkaAuditLog
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <param name="kafkaAuditLog"></param>
    /// <param name="kafkaAuditLogEvent"></param>
    public static void AddKafkaAuditLog(this IServiceCollection services, 
        IConfiguration configuration, 
        string kafkaAuditLog = "KafkaFlow", 
        string kafkaAuditLogEvent = "audit-events")
    {
        services.AddKafkaFlowBuilder(kafkaAuditLog)
                .WithBootstrapServers(configuration.GetSection($"{kafkaAuditLog}:BootstrapServers").Value ?? "")
                .AddTopic<AuditLogMessage, NonAuditLogEventHandler>(kafkaAuditLogEvent, $"group-{kafkaAuditLogEvent}")
                .EnableDeadLetter("failed-messages")
                .BuildProducer();
    }

    /// <summary>
    /// AddInfrastructure
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <param name="connectionKey"></param>
    /// <returns></returns>
    public static IServiceCollection AddInfrastructure<T>(this IServiceCollection services,
        IConfiguration configuration, string connectionKey) where T : BaseDbContext
    {
        services.AddScoped<BaseDbContext>(x => x.GetRequiredService<T>());
        services.AddDbContext<T>((sp, options) =>
        {
            var connectionString = configuration.GetConnectionString(connectionKey);
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(5),
                    errorCodesToAdd: null);
            });
        });

        return services;
    }

    /// <summary>
    /// public static IServiceCollection AddCurrentFactorProvider(this IServiceCollection services)
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddCurrentFactorProvider(this IServiceCollection services)
    {
        services.AddSingleton<IQuartzJobContextAccessor, QuartzJobContextAccessor>();
        services.AddScoped<HttpCurrentFactorProvider>();
        services.AddScoped<QuartzCurrentFactorProvider>();

        services.AddScoped<ICurrentFactorProvider>(serviceProvider =>
        {
            var httpContextAccessor = serviceProvider.GetService<IHttpContextAccessor>();
            var quartzContextAccessor = serviceProvider.GetRequiredService<IQuartzJobContextAccessor>();

            if (httpContextAccessor?.HttpContext != null)
            {
                return serviceProvider.GetRequiredService<HttpCurrentFactorProvider>();
            }

            return serviceProvider.GetRequiredService<QuartzCurrentFactorProvider>();
        });

        return services;
    }

    /// <summary>
    /// public static IServiceCollection AddDatabaseInitializer(this IServiceCollection services)
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddDatabaseInitializer(this IServiceCollection services)
    {
        services.AddScoped<IDatabaseInitializer, DatabaseInitializer>();
        return services;
    }
}
