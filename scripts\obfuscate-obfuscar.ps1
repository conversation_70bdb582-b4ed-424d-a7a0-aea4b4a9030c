# Obfuscate a single library DLL using Obfuscar (.NET 9 compatible)
# Usage: .\scripts\obfuscate-obfuscar.ps1 -ProjectPath "TMS.SharedKernal.Caching" -ProjectName "TMS.SharedKernal.Caching"

param(
    [Parameter(Mandatory=$true)]
    [string]$ProjectPath,

    [Parameter(Mandatory=$true)]
    [string]$ProjectName,

    [string]$Configuration = "Release",
    [string]$TargetFramework = "net9.0"
)

Write-Host "=== Library Obfuscation using Obfuscar ===" -ForegroundColor Cyan
Write-Host "Project: $ProjectName" -ForegroundColor White
Write-Host ""

# Step 1: Check if Obfuscar.GlobalTool is installed
Write-Host "[1/6] Checking Obfuscar installation..." -ForegroundColor Green
$obfuscarInstalled = dotnet tool list -g | Select-String "obfuscar.globaltool"
if (-not $obfuscarInstalled) {
    Write-Host "Obfuscar not found. Installing..." -ForegroundColor Yellow
    dotnet tool install --global Obfuscar.GlobalTool
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install Obfuscar!" -ForegroundColor Red
        exit 1
    }
    Write-Host "Obfuscar installed successfully!" -ForegroundColor Green
} else {
    Write-Host "Obfuscar is already installed." -ForegroundColor Green
}

# Step 2: Clean the project
Write-Host "`n[2/6] Cleaning project..." -ForegroundColor Green
$csprojPath = "$ProjectPath\$ProjectName.csproj"
dotnet clean $csprojPath -c $Configuration
if ($LASTEXITCODE -ne 0) {
    Write-Host "Clean failed!" -ForegroundColor Red
    exit 1
}

# Step 3: Build the project in Release mode
Write-Host "`n[3/8] Building project in $Configuration mode..." -ForegroundColor Green
dotnet build $csprojPath -c $Configuration
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

# Step 4: Publish to copy ALL dependencies (including framework assemblies)
Write-Host "`n[4/8] Publishing to gather all dependencies..." -ForegroundColor Green
$publishPath = "$ProjectPath\bin\Publish"
dotnet publish $csprojPath -c $Configuration -o $publishPath --no-build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Publish failed!" -ForegroundColor Red
    exit 1
}

# Step 5: Prepare paths
$binDir = "$ProjectPath\bin\$Configuration\$TargetFramework"
$dllPath = "$binDir\$ProjectName.dll"

if (-not (Test-Path $dllPath)) {
    Write-Host "DLL not found at: $dllPath" -ForegroundColor Red
    exit 1
}

Write-Host "DLL to obfuscate: $dllPath" -ForegroundColor Cyan

# Step 6: Copy ALL dependencies from publish folder to bin folder
Write-Host "`n[5/8] Copying all dependencies to bin folder..." -ForegroundColor Green
$copiedCount = 0
Get-ChildItem -Path $publishPath -Filter "*.dll" | ForEach-Object {
    $destPath = Join-Path $binDir $_.Name
    Copy-Item $_.FullName $destPath -Force
    $copiedCount++
}
Write-Host "  Copied $copiedCount dependencies" -ForegroundColor Gray

# Step 7: Create Obfuscar config for this specific library
Write-Host "`n[6/8] Creating Obfuscar configuration..." -ForegroundColor Green
$obfuscarConfig = ".\obfuscar-temp.xml"

# Update InPath to point to bin directory (where all dependencies are)
# Use absolute path to avoid path resolution issues
$binDirAbsolute = (Resolve-Path $binDir).Path
$baseConfig = Get-Content ".\obfuscar.xml" -Raw
$updatedConfig = $baseConfig -replace '<Var name="InPath" value="\." />', "<Var name=`"InPath`" value=`"$binDirAbsolute`" />"

# Add module entry with absolute path
$dllAbsolutePath = (Resolve-Path $dllPath).Path
$moduleEntry = "  <Module file=`"$dllAbsolutePath`" />"
$finalConfig = $updatedConfig -replace '</Obfuscator>', "$moduleEntry`n</Obfuscator>"

Set-Content -Path $obfuscarConfig -Value $finalConfig
Write-Host "Configuration created: $obfuscarConfig" -ForegroundColor Gray
Write-Host "  InPath: $binDirAbsolute" -ForegroundColor Gray
Write-Host "  Module: $dllAbsolutePath" -ForegroundColor Gray

# Step 8: Run Obfuscar
Write-Host "`n[7/8] Running Obfuscar obfuscation..." -ForegroundColor Green
obfuscar.console $obfuscarConfig
if ($LASTEXITCODE -ne 0) {
    Write-Host "Obfuscation failed!" -ForegroundColor Red
    Remove-Item $obfuscarConfig -ErrorAction SilentlyContinue
    exit 1
}

# Step 9: Replace original DLL with obfuscated version
Write-Host "`n[8/8] Replacing original DLL with obfuscated version..." -ForegroundColor Green
$obfuscatedDll = ".\Obfuscated\$ProjectName.dll"

if (-not (Test-Path $obfuscatedDll)) {
    Write-Host "Obfuscated DLL not found at: $obfuscatedDll" -ForegroundColor Red
    Remove-Item $obfuscarConfig -ErrorAction SilentlyContinue
    exit 1
}

# Backup original DLL
$backupPath = "$binDir\$ProjectName.dll.original"
if (Test-Path $dllPath) {
    Copy-Item $dllPath $backupPath -Force
    Write-Host "Original DLL backed up to: $backupPath" -ForegroundColor Yellow
}

# Replace with obfuscated DLL
Copy-Item $obfuscatedDll $dllPath -Force
Write-Host "Obfuscated DLL copied to: $dllPath" -ForegroundColor Green

# Also copy PDB if exists
$obfuscatedPdb = ".\Obfuscated\$ProjectName.pdb"
if (Test-Path $obfuscatedPdb) {
    Copy-Item $obfuscatedPdb "$binDir\$ProjectName.pdb" -Force
    Write-Host "Obfuscated PDB copied" -ForegroundColor Gray
}

# Cleanup
Write-Host "`nCleaning up temporary files..." -ForegroundColor Green
if ($env:DEBUG_OBFUSCATION -ne "1") {
    Remove-Item $obfuscarConfig -ErrorAction SilentlyContinue
    Remove-Item ".\Obfuscated" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item $publishPath -Recurse -Force -ErrorAction SilentlyContinue
} else {
    Write-Host "DEBUG: Keeping obfuscated files in .\Obfuscated\" -ForegroundColor Yellow
    Write-Host "DEBUG: Keeping config: $obfuscarConfig" -ForegroundColor Yellow
    Write-Host "DEBUG: Keeping publish folder: $publishPath" -ForegroundColor Yellow
}

Write-Host "`n=== Obfuscation Complete ===" -ForegroundColor Cyan
Write-Host "The obfuscated DLL is ready for NuGet packaging" -ForegroundColor Green
Write-Host "Original DLL backed up with .original extension" -ForegroundColor Yellow
