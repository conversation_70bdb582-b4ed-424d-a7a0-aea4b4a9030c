<?xml version="1.0"?>
<Obfuscator>
  <!--
    Obfuscar Configuration for SharedKernal Libraries

    STRATEGY: Obfuscate METHOD BODIES ONLY
    - Keep ALL names unchanged (types, methods, properties, fields)
    - Obfuscate only: strings, control flow, method bodies
    - Result: 100% backward compatible
  -->

  <!-- Variables -->
  <Var name="InPath" value="." />
  <Var name="OutPath" value=".\Obfuscated" />

  <!-- String obfuscation: Hide hardcoded strings in method bodies -->
  <Var name="HideStrings" value="true" />

  <!-- Control flow obfuscation: Make method bodies harder to reverse engineer -->
  <Var name="OptimizeMethods" value="true" />

  <!-- CRITICAL: Do NOT rename anything - keep 100% compatibility -->
  <Var name="RenameProperties" value="false" />
  <Var name="RenameEvents" value="false" />
  <Var name="RenameFields" value="false" />
  <Var name="KeepPublicApi" value="true" />
  <Var name="HidePrivateA<PERSON>" value="false" />

  <!-- Assembly list will be added by script -->

  <!--
    Skip ALL renaming by using a catch-all rule
    This ensures every type/method/property keeps its original name
  -->
  <SkipRename />

</Obfuscator>
