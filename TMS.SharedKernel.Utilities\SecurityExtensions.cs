﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using TMS.SharedKernel.Utilities.Middleware;

namespace TMS.SharedKernel.Utilities;

public static class SecurityExtensions
{
    public static IServiceCollection AddCustomCors(this IServiceCollection services, IConfiguration configuration)
    {
        //var corsSettings = configuration.GetSection(CorsSettings.SectionName).Get<CorsSettings>();

        //if (corsSettings == null)
        //{
        //    throw new InvalidOperationException("CORS configuration is missing");
        //}

        //services.Configure<CorsSettings>(configuration.GetSection(CorsSettings.SectionName));

        services.AddCors(options =>
        {
            options.AddPolicy("A1",
                builder => builder
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials()
                    .SetIsOriginAllowed(_ => true));

            //options.AddPolicy("A1", policy =>
            //{
            //    var isAllowAll = corsSettings.AllowedOrigins.Contains("*");
            //    // Handle origins
            //    if (isAllowAll)
            //    {
            //        policy.AllowAnyOrigin();
            //        policy.SetIsOriginAllowed(_ => true);
            //    }
            //    else
            //    {
            //        policy.WithOrigins(corsSettings.AllowedOrigins);
            //    }

            //    // Handle methods
            //    if (corsSettings.AllowedMethods.Contains("*"))
            //    {
            //        policy.AllowAnyMethod();
            //    }
            //    else
            //    {
            //        policy.WithMethods(corsSettings.AllowedMethods);
            //    }

            //    // Handle headers
            //    if (corsSettings.AllowedHeaders.Contains("*"))
            //    {
            //        policy.AllowAnyHeader();
            //    }
            //    else
            //    {
            //        policy.WithHeaders(corsSettings.AllowedHeaders);
            //    }

            //    // Handle credentials
            //    if (corsSettings.AllowCredentials && !isAllowAll)
            //    {
            //        // it works WithOrigins only
            //        policy.AllowCredentials();
            //    }

            //    // Handle exposed headers
            //    if (corsSettings.ExposedHeaders.Any())
            //    {
            //        policy.WithExposedHeaders(corsSettings.ExposedHeaders);
            //    }

            //    // Handle preflight max age
            //    if (corsSettings.PreflightMaxAge > 0)
            //    {
            //        policy.SetPreflightMaxAge(TimeSpan.FromSeconds(corsSettings.PreflightMaxAge));
            //    }
            //});
        });

        return services;
    }

    public static IApplicationBuilder UseSecureHeaders(this IApplicationBuilder app)
    {
        app.Use(async (context, next) =>
        {
            context.Response.Headers["Server"] = "TMS";
            context.Response.Headers["X-Powered-By"] = "TMS";
            context.Response.Headers["X-Content-Type-Options"] = "nosniff";
            context.Response.Headers["X-Frame-Options"] = "DENY";
            context.Response.Headers["X-XSS-Protection"] = "1; mode=block";
            context.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
            context.Response.Headers["Content-Security-Policy"] = "default-src 'self'";

            await next();
        });

        return app;
    }

    /// <summary>
    /// Adds middleware to convert access_token query parameter to Authorization Bearer header
    /// This should be called before UseAuthentication() in the middleware pipeline
    /// </summary>
    public static IApplicationBuilder UseQueryStringAuthentication(this IApplicationBuilder app)
    {
        return app.UseMiddleware<QueryStringAuthenticationMiddleware>();
    }
}

public class CorsSettings
{
    public const string SectionName = "Cors";

    public string[] AllowedOrigins { get; set; } = Array.Empty<string>();
    public string[] AllowedMethods { get; set; } = Array.Empty<string>();
    public string[] AllowedHeaders { get; set; } = Array.Empty<string>();
    public bool AllowCredentials { get; set; } = false;
    public string[] ExposedHeaders { get; set; } = Array.Empty<string>();
    public int PreflightMaxAge { get; set; } = 7200;
}
