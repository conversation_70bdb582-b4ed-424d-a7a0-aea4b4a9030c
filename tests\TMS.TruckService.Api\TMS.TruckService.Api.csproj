﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <RestoreForce>true</RestoreForce>
    <RestoreNoCache>true</RestoreNoCache>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="9.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <!--<PackageReference Include="OpenTelemetry" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />-->
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\TMS.SharedKernal.Kafka\TMS.SharedKernal.Kafka.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernal.RabbitMq\TMS.SharedKernal.RabbitMq.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernal.SmoothRedis\TMS.SharedKernal.SmoothRedis.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernel.Authentication\TMS.SharedKernel.Authentication.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernel.Utilities\TMS.SharedKernel.Utilities.csproj" />
    <ProjectReference Include="..\TMS.TruckService.ApiClient\TMS.TruckService.ApiClient.csproj" />
    <ProjectReference Include="..\TMS.TruckService.Application\TMS.TruckService.Application.csproj" />
    <ProjectReference Include="..\TMS.TruckService.Infra\TMS.TruckService.Infra.csproj" />
    <ProjectReference Include="..\TMS.TruckService.Contracts\TMS.TruckService.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Services\" />
  </ItemGroup>

</Project>
