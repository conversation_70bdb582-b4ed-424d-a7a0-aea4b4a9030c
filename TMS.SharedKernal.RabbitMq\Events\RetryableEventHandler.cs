﻿using System.Text.Json;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.RabbitMq.Abstractions;

namespace TMS.SharedKernal.RabbitMq.Events;

public class RetryableEventHandler<T> : IEventHandler<T> where T : class, IEvent
{
    private readonly IEventHandler<T> _innerHandler;
    private readonly ILogger<RetryableEventHandler<T>> _logger;
    private readonly int _maxRetries;
    private readonly TimeSpan _retryDelay;

    public RetryableEventHandler(
        IEventHandler<T> innerHandler,
        ILogger<RetryableEventHandler<T>> logger,
        int maxRetries = 3,
        TimeSpan? retryDelay = null)
    {
        _innerHandler = innerHandler;
        _logger = logger;
        _maxRetries = maxRetries;
        _retryDelay = retryDelay ?? TimeSpan.FromSeconds(1);
    }

    public async Task<bool> HandleAsync(T eventData, CancellationToken cancellationToken = default)
    {
        var attempt = 0;

        while (attempt <= _maxRetries)
        {
            try
            {
                var result = await _innerHandler.HandleAsync(eventData, cancellationToken);
                if (result) return true;

                if (attempt < _maxRetries)
                {
                    _logger.LogWarning("Handler returned false for event {EventId}, attempt {Attempt}/{MaxRetries}",
                        eventData.EventId, attempt + 1, _maxRetries + 1);
                }
            }
            catch (Exception ex) when (attempt < _maxRetries)
            {
                _logger.LogWarning(ex, "Handler failed for event {EventId}, attempt {Attempt}/{MaxRetries}",
                    eventData.EventId, attempt + 1, _maxRetries + 1);
            }

            attempt++;

            if (attempt <= _maxRetries)
            {
                await Task.Delay(_retryDelay, cancellationToken);
            }
        }

        _logger.LogError("All retry attempts failed for event {EventId}", eventData.EventId);
        return false;
    }
}
