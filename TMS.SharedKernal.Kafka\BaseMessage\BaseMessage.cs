﻿namespace TMS.SharedKernal.Kafka;

using TMS.SharedKernal.Kafka.Abstractions;

public abstract class BaseMessage : IMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class DeadLetterMessage : BaseMessage
{
    public string OriginalTopic { get; set; }
    public string OriginalMessage { get; set; }
    public string Error { get; set; }
    public int RetryCount { get; set; }
    public DateTime OriginalTimestamp { get; set; }
}
