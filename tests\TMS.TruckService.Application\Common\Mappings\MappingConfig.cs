﻿using Mapster;
using TMS.TruckService.Contracts.Orders;
using TMS.TruckService.Contracts.Todos;
using TMS.TruckService.Domain.Entities;

namespace TMS.TruckService.Application.Common.Mappings;

public static class MappingConfig
{
    public static void RegisterMappings()
    {
        // Todo Entity to TodoResponse
        TypeAdapterConfig<Todo, TodoResponse>
            .NewConfig()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Title, src => src.Title)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.IsCompleted, src => src.IsCompleted)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.CompletedAt, src => src.CompletedAt)
            .Map(dest => dest.UpdatedAt, src => src.UpdatedAt)
            .Map(dest => dest.CreatedBy, src => src.CreatedBy)
            .Map(dest => dest.UpdatedBy, src => src.UpdatedBy);

        // CreateTodoRequest to Todo Entity
        TypeAdapterConfig<CreateTodoRequest, Todo>
            .NewConfig()
            .Map(dest => dest.Title, src => src.Title)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.IsCompleted, src => false)
            .Map(dest => dest.CreatedAt, src => DateTime.UtcNow)
            .Map(dest => dest.UpdatedAt, src => DateTime.UtcNow)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.CompletedAt);

        // UpdateTodoRequest to Todo Entity (for updates)
        TypeAdapterConfig<UpdateTodoRequest, Todo>
            .NewConfig()
            .Map(dest => dest.Title, src => src.Title)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.IsCompleted, src => src.IsCompleted)
            .Map(dest => dest.UpdatedAt, src => DateTime.UtcNow)
            .Map(dest => dest.CompletedAt, src => src.IsCompleted ? DateTime.UtcNow : (DateTime?)null)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.CreatedAt);
    }
}
