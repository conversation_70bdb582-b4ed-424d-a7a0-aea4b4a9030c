﻿using Microsoft.Extensions.Configuration;
using TMS.SharedKernel.Authentication.Helpers;

namespace TMS.SharedKernel.Authentication;

/// <summary>
/// Delegating handler that adds an API key as a query parameter to all HTTP requests.
/// Usage Example:
///
/// services.AddHttpClient("MyClient")
///     .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
///         sp.GetRequiredService&lt;IConfiguration&gt;(),
///         configurationKey: "MyService:ApiKey",
///         queryParameterName: "api_key"
///     ));
/// </summary>
public class ApiKeyAuthenticationHandler : DelegatingHandler
{
    private readonly string _apiKey;
    private readonly string _queryParameterName;

    /// <summary>
    /// Initializes a new instance of the ApiKeyAuthenticationHandler.
    /// </summary>
    /// <param name="configuration">Configuration instance to read the API key from</param>
    /// <param name="configurationKey">The configuration key path to read the API key (e.g., "MyService:ApiKey")</param>
    /// <param name="queryParameterName">The name of the query parameter to add (e.g., "apikey", "api_key", "key")</param>
    /// <exception cref="InvalidOperationException">Thrown when the API key is not configured</exception>
    public ApiKeyAuthenticationHandler(
        IConfiguration configuration,
        string configurationKey = "ExternalService:ApiKey",
        string queryParameterName = "apikey")
    {
        ArgumentNullException.ThrowIfNull(configuration);
        ArgumentException.ThrowIfNullOrWhiteSpace(configurationKey);
        ArgumentException.ThrowIfNullOrWhiteSpace(queryParameterName);

        _queryParameterName = queryParameterName;
        _apiKey = configuration[configurationKey]
            ?? throw new InvalidOperationException($"API Key is not configured at '{configurationKey}'");
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Add API key as query parameter to all requests
        var uriBuilder = new UriBuilder(request.RequestUri!);
        var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
        query[_queryParameterName] = _apiKey;
        uriBuilder.Query = query.ToString();
        request.RequestUri = uriBuilder.Uri;

        try
        {
            return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
        }
        catch
        {
            return HttpResponseHelper.CreateSafeEmptyResponse();
        }
    }
}
