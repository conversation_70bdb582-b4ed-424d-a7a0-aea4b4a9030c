﻿using TMS.SharedKernal.RabbitMq.Events;

namespace TMS.TruckService.Domain.Events;

public class PaymentProcessedEvent : BaseEvent
{
    public override string EventType => "PaymentProcessed";
    public string PaymentId { get; set; } = string.Empty;
    public string OrderId { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string PaymentMethod { get; set; } = string.Empty;
}
