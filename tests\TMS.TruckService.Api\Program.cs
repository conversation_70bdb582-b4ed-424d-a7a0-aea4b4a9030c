﻿using Microsoft.AspNetCore.Http.Connections;
using Refit;
using TMS.NotificationService.Application.Hubs;
using TMS.SharedKernal.Kafka.Extensions;
using TMS.SharedKernal.RabbitMq.Extensions;
using TMS.SharedKernal.SmoothRedis;
using TMS.SharedKernal.SmoothRedis.Extensions;
using TMS.SharedKernel.Authentication;
using TMS.SharedKernel.Authentication.Handlers;
using TMS.SharedKernel.Utilities;
using TMS.TruckService.ApiClient;
using TMS.TruckService.Application;
using TMS.TruckService.Application.Events;
using TMS.TruckService.Application.Features.OrdersKafka;
using TMS.TruckService.Domain.Events;
using TMS.TruckService.Domain.EventsKafka;
using TMS.TruckService.Infra.Data;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        var otlpEndpoint = builder.Configuration["OpenTelemetry:OtlpEndpoint"] ?? String.Empty;
        var otlpEndpointKey = builder.Configuration["OpenTelemetry:Key"] ?? String.Empty;
        var env = builder.Configuration["environment"] ?? "Development";
        var serviceName = builder.Environment.ApplicationName; // "TMS.SharedKernal.Api";
        var serviceVersion = "v1";

        builder.AddBaseRequired(otlpEndpoint, otlpEndpointKey, env, serviceName, serviceVersion);

        // Add Application and Infrastructure services
        builder.Services.AddApplication();
        builder.Services.AddInfrastructureAudit<ApplicationDbContext>(builder.Configuration,
            "SharedKernelAuditLog", 
            "SharedKernalConnection");
        builder.Services.AddKafkaAuditLog(builder.Configuration);

        // Add inherit DbContext for design time migrations
        //builder.Services.AddDerivedDbContext<ApplicationDbContext>();

        builder.Services.AddScoped<AuthorizationDelegatingHandler>();
        var driversApiUrl = builder.Configuration.GetSection("DriverService:Url").Value;

        builder.Services.AddRefitClient<ITestApi>()
            .ConfigureHttpClient(c => c.BaseAddress = new Uri(driversApiUrl))
            //.AddHttpMessageHandler(() => new DefaultQueryParameterHandler(new Dictionary<string, string>
            //{
            //    { "hasDefault", "true" }
            //}))
            .AddHttpMessageHandler<AuthorizationDelegatingHandler>();

        //you could add Polly here to handle HTTP 429 / HTTP 503 etc
        builder.Services.AddPolicyBasedAuthorization(PermissionDefinition.AllPermissions);

        // Order services in the pipeline
        builder.Services.AddRabbitMq(builder.Configuration, Constants.RabbitMq);
        builder.Services.AddEventHandler<OrderCreatedEvent, OrderCreatedHandler>();
        builder.Services.AddEventConsumer<OrderCreatedEvent>(Constants.RabbitMqCompanyEvents, Constants.RabbitMqQueueOrder);
        builder.Services.AddEventHandler<PaymentProcessedEvent, PaymentProcessedHandler>();
        builder.Services.AddEventConsumer<PaymentProcessedEvent>(Constants.RabbitMqCompanyEvents, Constants.RabbitMqQueuePayment);

        //builder.Services.AddSmoothRedis("localhost:6379,password=your_secure_password");
        builder.Services.AddSmoothRedis(builder.Configuration);

        builder.Services.AddKafkaFlowBuilder(Constants.KafkaFlow)
                        .WithBootstrapServers(builder.Configuration.GetSection("KafkaFlow:BootstrapServers").Value ?? "")
                        //.WithSaslAuthentication("kafka-user", "kafka-password", "PLAIN")
                        .AddTopic<OrderCreatedEventKafka, OrderCreatedEventKafkaHandler>(Constants.KafkaOrderEvent, $"group-{Constants.KafkaOrderEvent}", autoCreate: true)
                        //.AddTopic<PaymentProcessedEventKafka, PaymentProcessedEventKafkaHandler>(Constants.KafkaPaymentEvent, $"group-{Constants.KafkaPaymentEvent}")
                        .EnableDeadLetter("failed-messages")
                        .Build();

        builder.Services.AddKafkaFlowBuilder(Constants.KafkaFlow02)
                        .WithBootstrapServers(builder.Configuration.GetSection("KafkaFlow02:BootstrapServers").Value ?? "")
                        //.WithSaslAuthentication("kafka-user", "kafka-password", "PLAIN")
                        //.AddTopic<OrderCreatedEventKafka, OrderCreatedEventKafkaHandler>(Constants.KafkaOrderEvent, $"group-{Constants.KafkaOrderEvent}")
                        .AddTopic<PaymentProcessedEventKafka, PaymentProcessedEventKafkaHandler>(Constants.KafkaPaymentEvent, $"group-{Constants.KafkaPaymentEvent}", autoCreate: true)
                        .EnableDeadLetter("failed-messages")
                        .Build();

        builder.Services.AddSignalR();

        //// Add gRPC services with TMS base configuration
        //builder.Services.AddBaseGrpc(options =>
        //{
        //    // Optional: Customize gRPC options
        //    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
        //});

        //// OPTIONAL: Add gRPC client for calling other microservices
        //// This demonstrates how to call your own gRPC service (normally you'd call a different service)
        //// Uncomment the following lines to enable gRPC client in DI:
        
        //builder.Services.AddOrderServiceClient(options =>
        //{
        //    options.ServerAddress = builder.Configuration["GrpcClients:OrderService:Address"] ?? "https://localhost:7081";
        //});
        

        var connString = builder.Configuration.GetConnectionString("SharedKernalConnection") ?? "";

        builder.Services.AddClusteredQuartz(
            connString,
            schedulerName: $"{serviceName}-scheduler");

        var app = builder.Build();

        // Auto-create database
        app.EnsureDatabaseCreated<ApplicationDbContext>("1");
        var schemaOperations = new List<(string TableName, string Indicator)>
        {
            ("code_sequence", "Default"),
            ("qrtz_job_details", "Quartz"),
            ("", "01")
        };
        app.EnsureAllDatabaseSchemasCreated(connectionString: connString, schemaOperations);

        //app.EnsureDatabaseCreated<ApplicationDbContext>("1");
        //app.EnsureSchemasCreated(connString: connString,
        //    tableName: "code_sequence");
        //app.EnsureSchemasMigrated(connString: connString,
        //    indicatorName: "01");

        app.UseBaseRequired(env, serviceName, serviceVersion);

        // Map gRPC services
        //app.MapGrpcService<GrpcOrderService>();

        // Enable gRPC reflection in development (useful for testing with tools like grpcurl)
        //app.UseBaseGrpcReflection();

        app.MapHub<NotificationHub>("/notificationhub", options =>
        {
            options.Transports = HttpTransportType.WebSockets | HttpTransportType.LongPolling; // Allow all transports
        });

        app.Use(async (context, next) =>
        {
            var itestapi = context.RequestServices.GetService<ITestApi>();
            var kaka = await itestapi.GetEmployeesByIdsAsync(new List<Guid>() { Guid.Empty });

            var redis = context.RequestServices.GetService<ISmoothRedis>();
            await redis.Cache.SetAsync("test01", "111111111111");

            var startTime = DateTime.UtcNow;
            Console.WriteLine($"01 Handling request: {context.Request.Method} {context.Request.Path}");

            // Call next middleware in pipeline
            await next();

            var duration = DateTime.UtcNow - startTime;
            Console.WriteLine($"01 Finished request in {duration.TotalMilliseconds}ms. Status: {context.Response.StatusCode}");
        });

        app.Use(async (context, next) =>
        {
            var startTime = DateTime.UtcNow;
            Console.WriteLine($"02 Handling request: {context.Request.Method} {context.Request.Path}");

            // Call next middleware in pipeline
            await next();

            var duration = DateTime.UtcNow - startTime;
            Console.WriteLine($"02 Finished request in {duration.TotalMilliseconds}ms. Status: {context.Response.StatusCode}");
        });

        app.Run();
    }

    
}
