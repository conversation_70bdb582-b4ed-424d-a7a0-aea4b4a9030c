# DatabaseInitializer - Variable Replacement Guide

## Overview
The DatabaseInitializer now supports passing variables to SQL scripts using a simple placeholder replacement mechanism.

## How to Use

### 1. Define Placeholders in SQL Files
In your SQL files (e.g., `SchemasDefault.sql`), use the `${VARIABLE_NAME}` syntax for placeholders:

```sql
-- Example: SchemasDefault.sql
CREATE SCHEMA IF NOT EXISTS ${SCHEMA_NAME};

CREATE TABLE ${SCHEMA_NAME}.users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    tenant_id VARCHAR(50) DEFAULT '${TENANT_ID}',
    partition_key VARCHAR(50) DEFAULT '${PARTITION_KEY}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE ${SCHEMA_NAME}.orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(100) NOT NULL,
    user_id INTEGER REFERENCES ${SCHEMA_NAME}.users(id),
    status VARCHAR(50) DEFAULT '${DEFAULT_STATUS}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes with dynamic naming
CREATE INDEX idx_${SCHEMA_NAME}_users_tenant
    ON ${SCHEMA_NAME}.users(tenant_id);
```

### 2. Pass Variables to DatabaseInitializer

#### Option A: Using EnsureSchemasAsync (Single Operation)
```csharp
var parameters = new Dictionary<string, string>
{
    { "SCHEMA_NAME", "public" },
    { "TENANT_ID", "tenant_001" },
    { "PARTITION_KEY", "2024" },
    { "DEFAULT_STATUS", "pending" }
};

await databaseInitializer.EnsureSchemasAsync(
    connectionString,
    tableName: "users",
    indicator: "Default",
    parameters: parameters
);
```

#### Option B: Using EnsureAllDatabaseSchemasAsync (Multiple Operations)
```csharp
var parameters = new Dictionary<string, string>
{
    { "SCHEMA_NAME", "public" },
    { "TENANT_ID", "tenant_001" },
    { "PARTITION_KEY", "2024" },
    { "DEFAULT_STATUS", "pending" }
};

var schemaOperations = new List<(string TableName, string Indicator)>
{
    ("users", "Default"),
    ("orders", "Default"),
    ("products", "Default")
};

await databaseInitializer.EnsureAllDatabaseSchemasAsync(
    connectionString,
    schemaOperations,
    parameters: parameters,
    additionalOperations: new Action[]
    {
        () => dbContext.Database.EnsureCreated()
    }
);
```

### 3. Real-World Example
```csharp
// Program.cs or Startup configuration
var builder = WebApplication.CreateBuilder(args);

// Get configuration values
var schemaName = builder.Configuration["Database:SchemaName"] ?? "public";
var tenantId = builder.Configuration["TenantId"] ?? "default";
var environment = builder.Environment.EnvironmentName;

// Setup parameters
var dbParameters = new Dictionary<string, string>
{
    { "SCHEMA_NAME", schemaName },
    { "TENANT_ID", tenantId },
    { "PARTITION_KEY", DateTime.UtcNow.Year.ToString() },
    { "DEFAULT_STATUS", "pending" },
    { "ENVIRONMENT", environment }
};

// Initialize database with parameters
var databaseInitializer = app.Services.GetRequiredService<IDatabaseInitializer>();
await databaseInitializer.EnsureSchemasAsync(
    connectionString,
    "users",
    "Default",
    dbParameters
);
```

## Features

### Placeholder Format
- **Syntax**: `${VARIABLE_NAME}`
- **Case Sensitive**: Yes, `${SCHEMA_NAME}` is different from `${schema_name}`
- **Allowed Characters**: Letters, numbers, underscores (standard identifier rules)

### Logging
The DatabaseInitializer logs placeholder replacements at the Debug level:
```
Replaced placeholder ${SCHEMA_NAME} with value public
Replaced placeholder ${TENANT_ID} with value tenant_001
```

### Backward Compatibility
If you don't pass the `parameters` dictionary, the SQL scripts will run as-is without any replacements:

```csharp
// Old code still works
await databaseInitializer.EnsureSchemasAsync(
    connectionString,
    "users",
    "Default"
);
```

## Best Practices

1. **Use Descriptive Names**: Choose clear variable names like `SCHEMA_NAME` instead of `S1`
2. **Document Variables**: Add comments in SQL files explaining required variables
3. **Provide Defaults**: Consider providing sensible default values in your code
4. **Validate Values**: Validate parameter values before passing to avoid SQL injection
5. **Environment-Specific Values**: Use configuration files for environment-specific parameters

## Security Notes

⚠️ **Important**: This method uses simple string replacement. While convenient, be cautious:
- **Sanitize Input**: Always validate and sanitize parameter values
- **Avoid User Input**: Don't pass raw user input directly as parameters
- **Use for Schema Names**: Best suited for schema names, table prefixes, and configuration values
- **Not for WHERE Clauses**: For dynamic WHERE clauses, use SQL parameters instead

## Example SQL File with Documentation
```sql
-- SchemasDefault.sql
-- Required Variables:
--   ${SCHEMA_NAME}    - Target schema name (e.g., 'public', 'tenant_001')
--   ${TENANT_ID}      - Default tenant identifier
--   ${PARTITION_KEY}  - Partition key for data organization
--   ${DEFAULT_STATUS} - Default status for new records

CREATE SCHEMA IF NOT EXISTS ${SCHEMA_NAME};

CREATE TABLE ${SCHEMA_NAME}.users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    tenant_id VARCHAR(50) DEFAULT '${TENANT_ID}',
    partition_key VARCHAR(50) DEFAULT '${PARTITION_KEY}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
