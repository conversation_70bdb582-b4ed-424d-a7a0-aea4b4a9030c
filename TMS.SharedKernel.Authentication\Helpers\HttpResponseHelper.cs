using System.Net;
using System.Text;

namespace TMS.SharedKernel.Authentication.Helpers;

/// <summary>
/// Helper class for creating HTTP responses in delegating handlers
/// </summary>
internal static class HttpResponseHelper
{
    /// <summary>
    /// Creates a safe empty response with HTTP 200 OK and JSON null to prevent exceptions in calling code.
    /// Returns valid JSON "null" which Refit can deserialize to any nullable type without throwing exceptions.
    ///
    /// This works for all Refit return types:
    /// - Nullable objects: Task&lt;UserDto?&gt; → returns null
    /// - Nullable collections: Task&lt;List&lt;UserDto&gt;?&gt; → returns null
    /// - IApiResponse&lt;T&gt;: Task&lt;IApiResponse&lt;UserDto&gt;&gt; → returns response with null content
    ///
    /// Note: Non-nullable types (Task&lt;UserDto&gt; without ?) may still cause issues at the calling code level,
    /// but this prevents JsonReaderException during deserialization.
    /// </summary>
    public static HttpResponseMessage CreateSafeEmptyResponse()
    {
        return new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent("null", Encoding.UTF8, "application/json"),
            RequestMessage = new HttpRequestMessage() // Prevents NullReferenceException
        };
    }
}
