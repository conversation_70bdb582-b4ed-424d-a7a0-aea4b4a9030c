﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using TMS.SharedKernel.Domain.Constants;
using TMS.SharedKernel.Domain.Extentions;

namespace TMS.SharedKernel.Authentication;

/// <summary>
/// Permission handler using will be following
/// [Authorize(Policy = nameof(PermissionEnum.ABCXYZ))]
/// </summary>
public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IServiceProvider _sp;
    private readonly ILogger<PermissionHandler> _logger;
    private readonly IConfiguration _configuration;

    private const string M2M = "X-Service-Auth";
    private const string EXTERNAL_TOKEN_HEADER = "X-External-Auth";
    private const string AUTHORIZATION_HEADER = "Authorization";

    /// <summary>
    /// PermissionHandler
    /// </summary>
    /// <param name="httpContextAccessor"></param>
    /// <param name="sp"></param>
    /// <param name="logger"></param>
    /// <param name="configuration"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public PermissionHandler(
        IHttpContextAccessor httpContextAccessor,
        IServiceProvider sp,
        ILogger<PermissionHandler> logger, 
        IConfiguration configuration)
    {
        _httpContextAccessor = httpContextAccessor;
        _sp = sp;
        _logger = logger;
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    /// <summary>
    /// Handle checking permission requirement
    /// </summary>
    /// <param name="context">AuthorizationHandlerContext</param>
    /// <param name="requirement">PermissionRequirement</param>
    /// <returns>Task</returns>
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
    {
        do
        {
            var authHeader = context.Resource as HttpContext;
            var authorization = authHeader?.Request.Headers[AUTHORIZATION_HEADER].FirstOrDefault();
            var externalAuth = authHeader?.Request.Headers[EXTERNAL_TOKEN_HEADER].FirstOrDefault();

            // debugging purpose
            // externalAuth = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMiIsIm5hbWUiOiJQTVMiLCJwZXJtaXNzaW9ucyI6WyJwbXMucHJpY2luZy5yZXF1ZXN0Y2FsY3VsYXRpb24iLCJwbXMud2ViaG9vay5wb2xpY3kiXX0.gL88AhZ3XpibdHDmbiOFjiECVvB-DQyqT7HHpwT2J_Y";

            // validate the token from User
            // Check if the authorization header is present and starts with "Bearer "
            if (authorization != null && authorization.StartsWith("Bearer "))
            {
                var token = authorization.Substring("Bearer ".Length).Trim();
                var tokenHandler = new JwtSecurityTokenHandler();

                // 1. Retrieve JWT configuration from IConfiguration
                // Ensure these keys exist in your appsettings.json
                var jwtKey = _configuration["Identity:Key"] ?? throw new InvalidOperationException("JWT Key is not configured.");
                var jwtIssuer = _configuration["Identity:Issuer"] ?? throw new InvalidOperationException("JWT Issuer is not configured.");
                var jwtAudience = _configuration["Identity:Audience"] ?? throw new InvalidOperationException("JWT Audience is not configured.");

                // 2. Define token validation parameters
                // These parameters dictate the rules for validating the token.
                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true, // Ensure the token was signed by the correct key
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey)),
                    ValidateIssuer = true,           // Validate the token's issuer
                    ValidIssuer = jwtIssuer,
                    ValidateAudience = true,         // Validate the token's audience
                    ValidAudience = jwtAudience,
                    ValidateLifetime = true,         // Validate the token's expiry
                    ClockSkew = TimeSpan.Zero        // No leeway for expiration (important for security)
                };

                // 3. Validate the token and get the ClaimsPrincipal
                SecurityToken validatedToken;
                ClaimsPrincipal principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out validatedToken);

                // 4. Extract the User ID claim
                // Commonly, the User ID is stored in the 'nameid' (ClaimTypes.NameIdentifier) or 'sub' claim.
                var userId = principal.FindFirst("employeeId")?.Value // Standard claim for unique identifier
                             ?? principal.FindFirst("sub")?.Value;    // Common JWT 'subject' claim

                var userName = principal.FindFirst("fullName")?.Value ?? "";

                Boolean.TryParse(principal.FindFirst("isAdmin")?.Value, out var isAdmin);
                var roleNames = isAdmin ? "admin" : principal.FindFirst("roleNames")?.Value ?? "";

                if (_httpContextAccessor.HttpContext == null)
                {
                    _logger.LogWarning("HttpContext is null. Cannot set User in HttpContext.");
                    break;
                }

                if (string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("User ID claim not found in token.");
                    break;
                }

                var permissionClaims = principal.FindAll("permissions");
                var isValidPermission = permissionClaims.Any(x => x.Value.Contains(requirement.Permission));
                // TODO
                //if (!isValidPermission)
                //{
                //    _logger.LogWarning("Permission denied for api access.");
                //    break;
                //}

                _httpContextAccessor.HttpContext.User = principal;
                _httpContextAccessor.HttpContext.Request.Headers.Remove(DomainConstants.UserId);
                _httpContextAccessor.HttpContext.Request.Headers.TryAdd(DomainConstants.UserId, userId);
                _httpContextAccessor.HttpContext.Request.Headers.Remove(DomainConstants.UserName);
                _httpContextAccessor.HttpContext.Request.Headers.TryAdd(DomainConstants.UserName, HeaderEncodingHelper.EncodeBase64(userName));
                _httpContextAccessor.HttpContext.Request.Headers.Remove(DomainConstants.Roles);
                _httpContextAccessor.HttpContext.Request.Headers.TryAdd(DomainConstants.Roles, roleNames);
            }
            else
            {
                // External token validation
                if (!string.IsNullOrEmpty(externalAuth))
                {
                    var token = externalAuth.Trim();
                    var tokenHandler = new JwtSecurityTokenHandler();

                    // 1. Retrieve JWT configuration from IConfiguration
                    // Ensure these keys exist in your appsettings.json
                    var env = _configuration["environment"];
                    var key = _configuration["PrivacyHash"] ?? "";
                    var combinedKey = $"{env}.{key}.b4ad6192948d13d35cdc527fc7b0b078da53096a";

                    // 2. Define token validation parameters
                    // These parameters dictate the rules for validating the token.
                    var tokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateAudience = false,
                        ValidateIssuer = false,
                        ValidateIssuerSigningKey = true, // Ensure the token was signed by the correct key
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(combinedKey)),
                        ValidateLifetime = false,        // Validate the token's expiry
                        ClockSkew = TimeSpan.Zero        // No leeway for expiration (important for security)
                    };

                    // 3. Validate the token and get the ClaimsPrincipal
                    SecurityToken validatedToken;
                    ClaimsPrincipal principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out validatedToken);

                    // 4. Extract the User ID claim
                    // Commonly, the User ID is stored in the 'nameid' (ClaimTypes.NameIdentifier) or 'sub' claim.
                    var userId = principal.FindFirst("id")?.Value;    // Common JWT 'subject' claim
                    var userName = principal.FindFirst("name")?.Value;
                    var permissionClaims = principal.FindAll("permissions");

                    // It's a JSON array, parse it
                    var isValidPermission = permissionClaims.Any(x => x.Value.Contains(requirement.Permission.ToString()));
                    if (!isValidPermission)
                    {
                        _logger.LogWarning("Permission denied for api access.");
                        break;
                    }

                    if (_httpContextAccessor.HttpContext == null)
                    {
                        _logger.LogWarning("HttpContext is null. Cannot set User in HttpContext.");
                        break;
                    }

                    if (string.IsNullOrEmpty(userId))
                    {
                        _logger.LogWarning("User ID claim not found in token.");
                        break;
                    }

                    _httpContextAccessor.HttpContext.User = principal;
                    _httpContextAccessor.HttpContext.Request.Headers.Remove(DomainConstants.UserId);
                    _httpContextAccessor.HttpContext.Request.Headers.TryAdd(DomainConstants.UserId, userId);
                    _httpContextAccessor.HttpContext.Request.Headers.Remove(DomainConstants.UserName);
                    _httpContextAccessor.HttpContext.Request.Headers.TryAdd(DomainConstants.UserName, HeaderEncodingHelper.EncodeBase64(userName));
                }
                else
                {
                    // validate service to service with internal token
                    var serviceToken = authHeader?.Request.Headers[M2M].FirstOrDefault();
                    var token = _configuration.GetValue<string>("DefaultKey");

                    if (!string.Equals(serviceToken, token))
                    {
                        _logger.LogWarning("Permission denied for api access.");
                        break;
                    }
                }
            }

            context.Succeed(requirement);
            return;
        } while (false);

        // Explicitly mark authorization as failed
        context.Fail(new AuthorizationFailureReason(this, "Permission denied for api access."));
        return;
    }

    #region -- Private Zone --

    private async Task<bool> CanAccessResourceAsync(Guid userId, Guid caseFileId, string roleNames, Guid? todoId)
    {
        // TODO
        return true;
    }

    private async Task<bool> CanExecuteResourceAsync(Guid userId, Guid caseFileId, string roleNames, Guid? todoId)
    {
        // TODO
        return true;
    }

    #endregion
}
