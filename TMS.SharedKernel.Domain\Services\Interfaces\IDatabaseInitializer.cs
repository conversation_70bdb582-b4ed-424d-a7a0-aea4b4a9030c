﻿namespace TMS.SharedKernel.Domain.Services.Interfaces;

/// <summary>
/// IDatabaseInitializer
/// </summary>
public interface IDatabaseInitializer
{
    /// <summary>
    /// Original method for backward compatibility
    /// </summary>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    Task EnsureSchemasAsync(string connectionString, string tableName, string indicator = "Default", Dictionary<string, string> parameters = null);

    /// <summary>
    /// New method that handles all schema operations in a single transaction
    /// </summary>
    /// <param name="parameters">Dictionary of variables to replace in SQL script. Use ${KEY} format in SQL files.</param>
    Task EnsureAllDatabaseSchemasAsync(string connectionString,
        List<(string TableName, string Indicator)> schemaOperations,
        Dictionary<string, string> parameters = null,
        params Action[] additionalOperations);
}
