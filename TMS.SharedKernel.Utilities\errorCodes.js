/**
 * Common error codes and messages for front-end applications
 * Mirrors the C# CommonErrorCodes, CommonMessages, HttpMessages, and ValidationMessages
 */
const ErrorCodes = {
  // Generic CRUD error codes
  ENTITY_NOT_FOUND: 'ENTITY_NOT_FOUND',
  ENTITY_ALREADY_EXISTS: 'ENTITY_ALREADY_EXISTS',
  ENTITY_CREATION_FAILED: 'ENTITY_CREATION_FAILED',
  ENTITY_UPDATE_FAILED: 'ENTITY_UPDATE_FAILED',
  ENTITY_DELETION_FAILED: 'ENTITY_DELETION_FAILED',

  // Common validation error codes
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT: 'INVALID_FORMAT',
  INVALID_VALUE: 'INVALID_VALUE',
  DUPLICATE_VALUE: 'DUPLICATE_VALUE',
  VALUE_OUT_OF_RANGE: 'VALUE_OUT_OF_RANGE',

  // Business rule error codes
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',

  // Authentication and authorization error codes
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',

  // System error codes
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',

  // Domain-specific error codes
  DOMAIN_VALIDATION_FAILED: 'DOMAIN_VALIDATION_FAILED',
  ENTITY_VALIDATION_FAILED: 'ENTITY_VALIDATION_FAILED',
  AGGREGATE_VALIDATION_FAILED: 'AGGREGATE_VALIDATION_FAILED',

  // HTTP Status Codes
  HTTP_BAD_REQUEST: 400,
  HTTP_UNAUTHORIZED: 401,
  HTTP_FORBIDDEN: 403,
  HTTP_NOT_FOUND: 404,
  HTTP_METHOD_NOT_ALLOWED: 405,
  HTTP_CONFLICT: 409,
  HTTP_UNPROCESSABLE_ENTITY: 422,
  HTTP_TOO_MANY_REQUESTS: 429,
  HTTP_INTERNAL_SERVER_ERROR: 500,
  HTTP_BAD_GATEWAY: 502,
  HTTP_SERVICE_UNAVAILABLE: 503,
  HTTP_GATEWAY_TIMEOUT: 504,

  // Validation-specific error codes (derived from ValidationMessages)
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_PHONE_NUMBER: 'INVALID_PHONE_NUMBER',
  INVALID_URL: 'INVALID_URL',
  INVALID_TYPE: 'INVALID_TYPE',
  MIN_LENGTH: 'MIN_LENGTH',
  MAX_LENGTH: 'MAX_LENGTH',
  EXACT_LENGTH: 'EXACT_LENGTH',
  RANGE: 'RANGE',
  MIN_VALUE: 'MIN_VALUE',
  MAX_VALUE: 'MAX_VALUE',
  INVALID_DATE: 'INVALID_DATE',
  FUTURE_DATE: 'FUTURE_DATE',
  PAST_DATE: 'PAST_DATE',
  DATE_RANGE: 'DATE_RANGE',
  EMPTY_COLLECTION: 'EMPTY_COLLECTION',
  MAX_ITEMS: 'MAX_ITEMS',
  MIN_ITEMS: 'MIN_ITEMS',
  MUST_BE_UNIQUE: 'MUST_BE_UNIQUE',
  INVALID_STATE: 'INVALID_STATE',
  DEPENDENCY_EXISTS: 'DEPENDENCY_EXISTS'
};

/**
 * Error messages corresponding to the error codes
 * Available in English (en) and Vietnamese (vn)
 */
const ErrorMessages = {
  en: {
    // Generic CRUD messages
    ENTITY_NOT_FOUND: '{0} with ID {1} not found.',
    ENTITY_ALREADY_EXISTS: '{0} with ID {1} already exists.',
    ENTITY_CREATION_FAILED: '{0} creation failed.',
    ENTITY_UPDATE_FAILED: '{0} update failed.',
    ENTITY_DELETION_FAILED: '{0} deletion failed.',

    // Common validation messages
    REQUIRED_FIELD_MISSING: '{0} is required.',
    INVALID_FORMAT: 'Invalid format for {0}.',
    INVALID_VALUE: 'Invalid value for {0}: {1}',
    DUPLICATE_VALUE: '{0} must be unique.',
    VALUE_OUT_OF_RANGE: '{0} must be between {1} and {2}.',

    // Business rule messages
    BUSINESS_RULE_VIOLATION: 'Business rule violation: {0}',
    INSUFFICIENT_PERMISSIONS: 'Insufficient permissions to perform this operation.',
    OPERATION_NOT_ALLOWED: 'Operation not allowed.',

    // Authentication and authorization messages
    UNAUTHORIZED: 'Authentication required.',
    FORBIDDEN: 'Access denied.',
    TOKEN_EXPIRED: 'Token has expired.',
    TOKEN_INVALID: 'Token is invalid.',

    // System messages
    INTERNAL_SERVER_ERROR: 'An internal server error occurred.',
    EXTERNAL_SERVICE_ERROR: 'External service error: {0}',
    DATABASE_ERROR: 'Database error occurred.',
    TIMEOUT_ERROR: 'Request timed out.',

    // Domain-specific messages
    DOMAIN_VALIDATION_FAILED: 'Domain validation failed.',
    ENTITY_VALIDATION_FAILED: 'Entity validation failed.',
    AGGREGATE_VALIDATION_FAILED: 'Aggregate validation failed.',

    // HTTP Status Messages
    HTTP_BAD_REQUEST: 'Bad request. Please check your input.',
    HTTP_UNAUTHORIZED: 'Authentication required.',
    HTTP_FORBIDDEN: 'Access denied.',
    HTTP_NOT_FOUND: 'The requested resource was not found.',
    HTTP_METHOD_NOT_ALLOWED: 'HTTP method not allowed.',
    HTTP_CONFLICT: 'Resource already exists or conflicts with current state.',
    HTTP_UNPROCESSABLE_ENTITY: 'Request contains invalid data.',
    HTTP_TOO_MANY_REQUESTS: 'Rate limit exceeded. Please try again later.',
    HTTP_INTERNAL_SERVER_ERROR: 'An internal server error occurred.',
    HTTP_BAD_GATEWAY: 'Bad gateway response from upstream service.',
    HTTP_SERVICE_UNAVAILABLE: 'Service temporarily unavailable.',
    HTTP_GATEWAY_TIMEOUT: 'Gateway timeout from upstream service.',

    // Validation-specific messages
    INVALID_EMAIL: 'Please enter a valid email address for {0}.',
    INVALID_PHONE_NUMBER: 'Please enter a valid phone number for {0}.',
    INVALID_URL: 'Please enter a valid URL for {0}.',
    INVALID_TYPE: '{0} must be of type {1}.',
    MIN_LENGTH: '{0} must be at least {1} characters long.',
    MAX_LENGTH: '{0} must not exceed {1} characters.',
    EXACT_LENGTH: '{0} must be exactly {1} characters long.',
    RANGE: '{0} must be between {1} and {2}.',
    MIN_VALUE: '{0} must be at least {1}.',
    MAX_VALUE: '{0} must not exceed {1}.',
    INVALID_DATE: 'Please enter a valid date for {0}.',
    FUTURE_DATE: '{0} must be a future date.',
    PAST_DATE: '{0} must be a past date.',
    DATE_RANGE: '{0} must be between {1} and {2}.',
    EMPTY_COLLECTION: '{0} cannot be empty.',
    MAX_ITEMS: '{0} cannot contain more than {1} items.',
    MIN_ITEMS: '{0} must contain at least {1} items.',
    MUST_BE_UNIQUE: '{0} must be unique.',
    INVALID_STATE: '{0} is in an invalid state.',
    DEPENDENCY_EXISTS: 'Cannot delete {0} because it has associated {1}.'
  },
  vn: {
    // Generic CRUD messages
    ENTITY_NOT_FOUND: '{0} với ID {1} không tìm thấy.',
    ENTITY_ALREADY_EXISTS: '{0} với ID {1} đã tồn tại.',
    ENTITY_CREATION_FAILED: 'Tạo {0} thất bại.',
    ENTITY_UPDATE_FAILED: 'Cập nhật {0} thất bại.',
    ENTITY_DELETION_FAILED: 'Xóa {0} thất bại.',

    // Common validation messages
    REQUIRED_FIELD_MISSING: '{0} là bắt buộc.',
    INVALID_FORMAT: 'Định dạng không hợp lệ cho {0}.',
    INVALID_VALUE: 'Giá trị không hợp lệ cho {0}: {1}',
    DUPLICATE_VALUE: '{0} phải là duy nhất.',
    VALUE_OUT_OF_RANGE: '{0} phải nằm trong khoảng {1} và {2}.',

    // Business rule messages
    BUSINESS_RULE_VIOLATION: 'Vi phạm quy tắc nghiệp vụ: {0}',
    INSUFFICIENT_PERMISSIONS: 'Không đủ quyền để thực hiện thao tác này.',
    OPERATION_NOT_ALLOWED: 'Thao tác không được phép.',

    // Authentication and authorization messages
    UNAUTHORIZED: 'Yêu cầu xác thực.',
    FORBIDDEN: 'Truy cập bị từ chối.',
    TOKEN_EXPIRED: 'Token đã hết hạn.',
    TOKEN_INVALID: 'Token không hợp lệ.',

    // System messages
    INTERNAL_SERVER_ERROR: 'Đã xảy ra lỗi máy chủ nội bộ.',
    EXTERNAL_SERVICE_ERROR: 'Lỗi dịch vụ bên ngoài: {0}',
    DATABASE_ERROR: 'Đã xảy ra lỗi cơ sở dữ liệu.',
    TIMEOUT_ERROR: 'Yêu cầu đã hết thời gian chờ.',

    // Domain-specific messages
    DOMAIN_VALIDATION_FAILED: 'Xác thực miền thất bại.',
    ENTITY_VALIDATION_FAILED: 'Xác thực thực thể thất bại.',
    AGGREGATE_VALIDATION_FAILED: 'Xác thực tổng hợp thất bại.',

    // HTTP Status Messages
    HTTP_BAD_REQUEST: 'Yêu cầu không hợp lệ. Vui lòng kiểm tra đầu vào của bạn.',
    HTTP_UNAUTHORIZED: 'Yêu cầu xác thực.',
    HTTP_FORBIDDEN: 'Truy cập bị từ chối.',
    HTTP_NOT_FOUND: 'Không tìm thấy tài nguyên được yêu cầu.',
    HTTP_METHOD_NOT_ALLOWED: 'Phương thức HTTP không được phép.',
    HTTP_CONFLICT: 'Tài nguyên đã tồn tại hoặc xung đột với trạng thái hiện tại.',
    HTTP_UNPROCESSABLE_ENTITY: 'Yêu cầu chứa dữ liệu không hợp lệ.',
    HTTP_TOO_MANY_REQUESTS: 'Vượt quá giới hạn tốc độ. Vui lòng thử lại sau.',
    HTTP_INTERNAL_SERVER_ERROR: 'Đã xảy ra lỗi máy chủ nội bộ.',
    HTTP_BAD_GATEWAY: 'Phản hồi cổng xấu từ dịch vụ upstream.',
    HTTP_SERVICE_UNAVAILABLE: 'Dịch vụ tạm thời không khả dụng.',
    HTTP_GATEWAY_TIMEOUT: 'Hết thời gian chờ cổng từ dịch vụ upstream.',

    // Validation-specific messages
    INVALID_EMAIL: 'Vui lòng nhập địa chỉ email hợp lệ cho {0}.',
    INVALID_PHONE_NUMBER: 'Vui lòng nhập số điện thoại hợp lệ cho {0}.',
    INVALID_URL: 'Vui lòng nhập URL hợp lệ cho {0}.',
    INVALID_TYPE: '{0} phải có kiểu {1}.',
    MIN_LENGTH: '{0} phải có ít nhất {1} ký tự.',
    MAX_LENGTH: '{0} không được vượt quá {1} ký tự.',
    EXACT_LENGTH: '{0} phải có đúng {1} ký tự.',
    RANGE: '{0} phải nằm trong khoảng {1} và {2}.',
    MIN_VALUE: '{0} phải ít nhất là {1}.',
    MAX_VALUE: '{0} không được vượt quá {1}.',
    INVALID_DATE: 'Vui lòng nhập ngày hợp lệ cho {0}.',
    FUTURE_DATE: '{0} phải là ngày trong tương lai.',
    PAST_DATE: '{0} phải là ngày trong quá khứ.',
    DATE_RANGE: '{0} phải nằm trong khoảng {1} và {2}.',
    EMPTY_COLLECTION: '{0} không thể trống.',
    MAX_ITEMS: '{0} không thể chứa nhiều hơn {1} mục.',
    MIN_ITEMS: '{0} phải chứa ít nhất {1} mục.',
    MUST_BE_UNIQUE: '{0} phải là duy nhất.',
    INVALID_STATE: '{0} ở trạng thái không hợp lệ.',
    DEPENDENCY_EXISTS: 'Không thể xóa {0} vì nó có liên quan đến {1}.'
  }
};

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ErrorCodes, ErrorMessages };
} else if (typeof window !== 'undefined') {
  window.ErrorCodes = ErrorCodes;
  window.ErrorMessages = ErrorMessages;
}