using TMS.SharedKernel.Domain.Entities;
using Xunit;

namespace TMS.SharedKernel.Domain.Tests;

public class SequentialGuidGeneratorTests
{
    // Mock enum for testing
    private enum TestSource
    {
        PMS = 0,
        HRM = 1,
        TMS = 2
    }

    [Fact]
    public void CreateDeterministicGuid_SameInputs_ReturnsSameGuid()
    {
        // Arrange
        var code = "VLG";
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guid1 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId);
        var guid2 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId);

        // Assert
        Assert.Equal(guid1, guid2);
    }

    [Fact]
    public void CreateDeterministicGuid_DifferentCodes_ReturnsDifferentGuids()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guidVLG = SequentialGuidGenerator.CreateDeterministicGuid("VLG", source, typeId);
        var guidHAN = SequentialGuidGenerator.CreateDeterministicGuid("HAN", source, typeId);

        // Assert
        Assert.NotEqual(guidVLG, guidHAN);
    }

    [Fact]
    public void CreateDeterministicGuid_DifferentSources_ReturnsDifferentGuids()
    {
        // Arrange
        var code = "VLG";
        var typeId = 1;

        // Act
        var guidPMS = SequentialGuidGenerator.CreateDeterministicGuid(code, (int)TestSource.PMS, typeId);
        var guidHRM = SequentialGuidGenerator.CreateDeterministicGuid(code, (int)TestSource.HRM, typeId);

        // Assert
        Assert.NotEqual(guidPMS, guidHRM);
    }

    [Fact]
    public void CreateDeterministicGuid_DifferentTypes_ReturnsDifferentGuids()
    {
        // Arrange
        var code = "VLG";
        var source = (int)TestSource.PMS;

        // Act
        var guid1 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, 1);
        var guid2 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, 2);

        // Assert
        Assert.NotEqual(guid1, guid2);
    }

    [Fact]
    public void CreateDeterministicGuid_SortsAlphabetically_BySameSourceAndType()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;
        var codes = new[] { "VLG", "ABH", "Q08", "HAN", "ZZZ" };

        // Act
        var guids = codes
            .Select(code => new
            {
                Code = code,
                Guid = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId)
            })
            .ToList();

        var sortedByGuid = guids.OrderBy(x => x.Guid.ToString()).Select(x => x.Code).ToList();
        var sortedByCode = codes.OrderBy(x => x).ToList();

        // Assert
        Assert.Equal(sortedByCode, sortedByGuid);
    }

    [Fact]
    public void CreateDeterministicGuid_SortsBySource_First()
    {
        // Arrange
        var code = "VLG";
        var typeId = 1;

        // Act
        var guidPMS = SequentialGuidGenerator.CreateDeterministicGuid(code, (int)TestSource.PMS, typeId);
        var guidHRM = SequentialGuidGenerator.CreateDeterministicGuid(code, (int)TestSource.HRM, typeId);
        var guidTMS = SequentialGuidGenerator.CreateDeterministicGuid(code, (int)TestSource.TMS, typeId);

        // Assert
        Assert.True(guidPMS.ToString().CompareTo(guidHRM.ToString()) < 0);
        Assert.True(guidHRM.ToString().CompareTo(guidTMS.ToString()) < 0);
    }

    [Fact]
    public void CreateDeterministicGuid_SortsByType_Second()
    {
        // Arrange
        var code = "VLG";
        var source = (int)TestSource.PMS;

        // Act
        var guid1 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, 1);
        var guid2 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, 2);
        var guid10 = SequentialGuidGenerator.CreateDeterministicGuid(code, source, 10);

        // Assert
        Assert.True(guid1.ToString().CompareTo(guid2.ToString()) < 0);
        Assert.True(guid2.ToString().CompareTo(guid10.ToString()) < 0);
    }

    [Fact]
    public void CreateDeterministicGuid_SortsByCode_Third()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guidABH = SequentialGuidGenerator.CreateDeterministicGuid("ABH", source, typeId);
        var guidQ08 = SequentialGuidGenerator.CreateDeterministicGuid("Q08", source, typeId);
        var guidVLG = SequentialGuidGenerator.CreateDeterministicGuid("VLG", source, typeId);

        // Assert - ABH < Q08 < VLG alphabetically
        Assert.True(guidABH.ToString().CompareTo(guidQ08.ToString()) < 0);
        Assert.True(guidQ08.ToString().CompareTo(guidVLG.ToString()) < 0);
    }

    [Fact]
    public void CreateDeterministicGuid_HandlesNumericCodes()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guid001 = SequentialGuidGenerator.CreateDeterministicGuid("001", source, typeId);
        var guid010 = SequentialGuidGenerator.CreateDeterministicGuid("010", source, typeId);
        var guid100 = SequentialGuidGenerator.CreateDeterministicGuid("100", source, typeId);

        // Assert
        Assert.True(guid001.ToString().CompareTo(guid010.ToString()) < 0);
        Assert.True(guid010.ToString().CompareTo(guid100.ToString()) < 0);
    }

    [Fact]
    public void CreateDeterministicGuid_HandlesAlphanumericCodes()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guidVLG001 = SequentialGuidGenerator.CreateDeterministicGuid("VLG001", source, typeId);
        var guidVLG010 = SequentialGuidGenerator.CreateDeterministicGuid("VLG010", source, typeId);
        var guidVLG100 = SequentialGuidGenerator.CreateDeterministicGuid("VLG100", source, typeId);

        // Assert
        Assert.True(guidVLG001.ToString().CompareTo(guidVLG010.ToString()) < 0);
        Assert.True(guidVLG010.ToString().CompareTo(guidVLG100.ToString()) < 0);
    }

    [Fact]
    public void CreateDeterministicGuid_HandlesEmptyCode()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guid1 = SequentialGuidGenerator.CreateDeterministicGuid("", source, typeId);
        var guid2 = SequentialGuidGenerator.CreateDeterministicGuid(null, source, typeId);

        // Assert
        Assert.Equal(guid1, guid2);
    }

    [Fact]
    public void CreateDeterministicGuid_HandlesCodesWithSpecialCharacters()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guid1 = SequentialGuidGenerator.CreateDeterministicGuid("VLG-001", source, typeId);
        var guid2 = SequentialGuidGenerator.CreateDeterministicGuid("VLG001", source, typeId);

        // Assert - special characters are removed, so both should be equal
        Assert.Equal(guid1, guid2);
    }

    [Fact]
    public void CreateDeterministicGuid_HandlesLowerCase()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 1;

        // Act
        var guid1 = SequentialGuidGenerator.CreateDeterministicGuid("vlg", source, typeId);
        var guid2 = SequentialGuidGenerator.CreateDeterministicGuid("VLG", source, typeId);

        // Assert - case-insensitive
        Assert.Equal(guid1, guid2);
    }

    [Fact]
    public void CreateDeterministicGuid_FormatStartsWithSourceAndType()
    {
        // Arrange
        var code = "VLG";
        var source = (int)TestSource.PMS; // 0
        var typeId = 6;

        // Act
        var guid = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId);
        var guidString = guid.ToString();

        // Assert - should start with 00000006 (source=0, type=6)
        Assert.True(guidString.StartsWith("00000006"));
    }

    [Fact]
    public void CreateDeterministicGuid_RealWorldScenario_MultipleSources()
    {
        // Arrange - simulating real data
        var testData = new[]
        {
            new { Code = "VLG", Source = (int)TestSource.PMS, Type = 6 },
            new { Code = "HAN", Source = (int)TestSource.PMS, Type = 6 },
            new { Code = "VLG", Source = (int)TestSource.HRM, Type = 7 },
            new { Code = "ABH", Source = (int)TestSource.PMS, Type = 6 },
            new { Code = "Q08", Source = (int)TestSource.PMS, Type = 6 },
        };

        // Act
        var guids = testData
            .Select(d => new
            {
                d.Code,
                d.Source,
                d.Type,
                Guid = SequentialGuidGenerator.CreateDeterministicGuid(d.Code, d.Source, d.Type)
            })
            .OrderBy(x => x.Guid.ToString())
            .ToList();

        // Assert - should be ordered by Source -> Type -> Code
        Assert.Equal("ABH", guids[0].Code); // PMS, Type 6, ABH (alphabetically first)
        Assert.Equal("HAN", guids[1].Code); // PMS, Type 6, HAN
        Assert.Equal("Q08", guids[2].Code); // PMS, Type 6, Q08
        Assert.Equal("VLG", guids[3].Code); // PMS, Type 6, VLG
        Assert.Equal("VLG", guids[4].Code); // HRM, Type 7, VLG (different source/type)
    }

    [Fact]
    public void CreateDeterministicGuid_CompleteRealWorldCodeList_SortsAlphabetically()
    {
        // Arrange - Complete list of real post office codes
        var codes = new[]
        {
            "111", "112", "113", "11B", "132", "133", "134", "13B", "14A", "212", "21B", "22A", "232", "233", "234", "23B", "24A", "311", "312", "331", "332", "333", "334", "33B", "34A", "411",
            "ABH", "ADD", "ADG", "AGG", "AKE", "ALC", "ALO", "AMH", "APG", "APU", "ASN", "ATI", "AVG", "AYA",
            "B33", "BAR", "BBG", "BBH", "BCN", "BCT", "BCU", "BDA", "BDE", "BDG", "BDN", "BDP", "BGD", "BGG", "BHA", "BHG", "BHN", "BHO", "BIG", "BKN", "BKS", "BLM", "BLU", "BNC", "BND", "BNH", "BOC", "BOE", "BOM", "BSD", "BSN", "BTE", "BTP", "BTT", "BUN",
            "CBG", "CCI", "CDC", "CGC", "CHA", "CHI", "CIH", "CJT", "CKE", "CKN", "CLH", "CLI", "CLO", "CLY", "CMU", "CMY", "CNB", "CNN", "CPA", "CPH", "CRG", "CRH", "CSD", "CTG", "CTH", "CUC", "CUG", "CUI", "CUS",
            "D11", "D12", "D32", "D33", "DAH", "DAN", "DBO", "DCA", "DCU", "DDO", "DGU", "DHA", "DHE", "DHG", "DHI", "DIL", "DKD", "DKI", "DKM", "DKO", "DLC", "DLG", "DLH", "DLT", "DMD", "DMH", "DML", "DNA", "DNC", "DND", "DNI", "DNM", "DPO", "DPU", "DQN", "DSN", "DTG", "DTH", "DTN", "DTP", "DTU", "DUH", "DUT", "DVG", "DXI", "DYN",
            "EHO", "EKR", "ETN",
            "FAD",
            "GNA", "GQO", "GTY", "GVN",
            "HAA", "HAD", "HAG", "HAN", "HBG", "HBH", "HCG", "HDC", "HDCD", "HDG", "HDQT", "HGA", "HGH", "HGI", "HHA", "HHO", "HHU", "HIA", "HKH", "HLG", "HLH", "HMN", "HMT", "HNN", "HNU", "HOC", "HPA", "HPH", "HPP", "HPT", "HPU", "HQN", "HRD", "HRG", "HTH", "HTN", "HTU", "HUE", "HUG", "HXH", "HYN",
            "IOS", "ITD",
            "K1A", "K2A", "K3A", "K3B", "K4A", "KAH", "KBG", "KBH", "KBN", "KCU", "KDH", "KGG", "KHH", "KHM", "KLG", "KLY", "KMH", "KMI", "KMN", "KNG", "KTB", "KTC", "KTE", "KTG", "KTM", "KXG",
            "LAN", "LBN", "LCI", "LCN", "LCU", "LED", "LEN", "LGD", "LGI", "LGN", "LKG", "LKH", "LMA", "LMO", "LNM", "LNN", "LPH", "LQN", "LSN", "LTH", "LTI", "LTY",
            "MCI", "MCU", "MDC", "MDH", "MDK", "MKD", "MLH", "MSN", "MTI",
            "N1A", "N2A", "N3A", "N3B", "N4A", "NAH", "NBD", "NBE", "NBH", "NBI", "NCG", "NCH", "NCL", "NDH", "NHE", "NHG", "NHI", "NHN", "NKH", "NKM", "NKU", "NLC", "NLG", "NMN", "NNA", "NNN", "NOH", "NON", "NPG", "NQN", "NTG", "NTH", "NTI",
            "PAN", "PBH", "PCH", "PCT", "PCU", "PCY", "PDD", "PDN", "PGO", "PLC", "PLD", "PLG", "PLU", "PLY", "PMY", "PNH", "PNI", "PNN", "POG", "PON", "PQC", "PRG", "PSD", "PTH", "PTR", "PTT", "PUH", "PUO", "PUR", "PUX",
            "Q08", "Q11", "QCD", "QGG", "QGP", "QHP", "QLU", "QLV", "QNH", "QNI", "QNN", "QOI", "QPU", "QYN",
            "RND",
            "SBD", "SDC", "SHA", "SHH", "SLA", "SMY", "SNY", "SSN", "STG",
            "TAA", "TAG", "TAH", "TBA", "TBG", "TBH", "TBN", "TCG", "TCN", "TCU", "TDN", "TDO", "TDP", "TDU", "TEN", "TGA", "TGG", "TGH", "TGN", "TGO", "THA", "THH", "THI", "THO", "THS", "THX", "THY", "TIA", "TKE", "TKH", "TKY", "TLG", "TLH", "TMD", "TMG", "TMN", "TMT", "TNA", "TNC", "TNH", "TNI", "TNN", "TNP", "TNU", "TOA", "TOK", "TOT", "TPG", "TQG", "TTG", "TTH", "TTI", "TTY", "TUH", "TUY", "TVG", "TVH", "TYA", "TYG", "TYN", "TYS",
            "UBI", "UHA",
            "VBN", "VBO", "VCU", "VDN", "VHH", "VHT", "VIH", "VLC", "VLG", "VLH", "VNH", "VNI", "VTG", "VTI", "VYN",
            "XLC", "XMC", "XPG", "XTG",
            "YBI", "YDG", "YDH", "YLC", "YMO", "YTE", "YTH", "YYN"
        };

        var source = (int)TestSource.PMS;
        var typeId = 6;

        // Act - Generate GUIDs for all codes
        var guidList = codes
            .Select(code => new
            {
                Code = code,
                Guid = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId)
            })
            .ToList();

        // Sort by GUID
        var sortedByGuid = guidList.OrderBy(x => x.Guid.ToString()).Select(x => x.Code).ToList();

        // Sort codes alphabetically
        var sortedByCode = codes.OrderBy(x => x).ToList();

        // Assert - GUIDs should sort in the same order as alphabetical code sorting
        Assert.Equal(sortedByCode.Count, sortedByGuid.Count);

        for (int i = 0; i < sortedByCode.Count; i++)
        {
            Assert.True(sortedByCode[i] == sortedByGuid[i],
                $"Mismatch at position {i}: Expected '{sortedByCode[i]}' but got '{sortedByGuid[i]}'");
        }
    }

    [Fact]
    public void CreateDeterministicGuid_SpecificCodeExamples_VerifyGuidFormat()
    {
        // Arrange
        var source = (int)TestSource.PMS; // 0
        var typeId = 6;
        var testCases = new[]
        {
            new { Code = "VLG", ExpectedStart = "00000006-564c" }, // V=0x56, L=0x4C, G=0x47
            new { Code = "Q08", ExpectedStart = "00000006-5130" }, // Q=0x51, 0=0x30, 8=0x38
            new { Code = "ABH", ExpectedStart = "00000006-4142" }, // A=0x41, B=0x42, H=0x48
            new { Code = "111", ExpectedStart = "00000006-3131" }, // 1=0x31, 1=0x31, 1=0x31
        };

        foreach (var testCase in testCases)
        {
            // Act
            var guid = SequentialGuidGenerator.CreateDeterministicGuid(testCase.Code, source, typeId);
            var guidString = guid.ToString();

            // Assert
            Assert.True(guidString.StartsWith(testCase.ExpectedStart),
                $"GUID for code '{testCase.Code}' should start with '{testCase.ExpectedStart}' but got '{guidString}'");
        }
    }

    [Fact]
    public void CreateDeterministicGuid_NumericCodesWithLeadingZeros_SortNumerically()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 16;
        var codes = new[] { "111", "112", "113", "132", "133", "134", "212", "232", "233", "234", "311", "312", "331", "332", "333", "334", "411" };

        // Act
        var guidList = codes
            .Select(code => new
            {
                Code = code,
                Guid = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId)
            })
            .ToList();

        var sortedByGuid = guidList.OrderBy(x => x.Guid.ToString()).Select(x => x.Code).ToList();
        var sortedByCode = codes.OrderBy(x => x).ToList();

        // Assert - numeric strings sort alphabetically (111 < 112 < 113 < 132...)
        Assert.Equal(sortedByCode, sortedByGuid);
    }

    [Fact]
    public void CreateDeterministicGuid_MixedAlphanumeric_SortsCorrectly()
    {
        // Arrange
        var source = (int)TestSource.PMS;
        var typeId = 16;
        // Mixed numeric and alphanumeric codes
        var codes = new[] { "11B", "13B", "14A", "21B", "22A", "23B", "24A", "33B", "34A", "B33", "K1A", "K2A", "K3A", "K3B", "K4A", "N1A", "N2A", "N3A", "N3B", "N4A" };

        // Act
        var guidList = codes
            .Select(code => new
            {
                Code = code,
                Guid = SequentialGuidGenerator.CreateDeterministicGuid(code, source, typeId)
            })
            .ToList();

        var sortedByGuid = guidList.OrderBy(x => x.Guid.ToString()).Select(x => x.Code).ToList();
        var sortedByCode = codes.OrderBy(x => x).ToList();

        // Assert
        Assert.Equal(sortedByCode, sortedByGuid);

        // Verify specific order: numbers come before letters in ASCII
        Assert.True(sortedByGuid.IndexOf("11B") < sortedByGuid.IndexOf("B33"));
        Assert.True(sortedByGuid.IndexOf("K1A") < sortedByGuid.IndexOf("K2A"));
        Assert.True(sortedByGuid.IndexOf("K3A") < sortedByGuid.IndexOf("K3B"));
    }
}
