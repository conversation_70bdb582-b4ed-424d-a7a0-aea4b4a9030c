﻿using System.ComponentModel.DataAnnotations;
using TMS.SharedKernel.Domain.Entities.Interfaces;

namespace TMS.SharedKernel.Domain.Entities;

/// <summary>
/// Represents an auditable entity that supports concurrency control.
/// </summary>
public abstract class AuditableConcurrencyControlledEntity : AuditableEntity, IConcurrencyControlled
{
    /// <inheritdoc />
    [Timestamp] public byte[] Version { get; set; } = [];
}

/// <summary>
/// Represents an auditable, concurrency-controlled entity with a customizable primary key.
/// </summary>
public abstract class AuditableConcurrencyControlledEntity<TKey> : AuditableEntity<TKey>, IConcurrencyControlled
    where TKey : IComparable, IComparable<TKey>, IEquatable<TKey>
{
    /// <inheritdoc />
    [Timestamp] public byte[] Version { get; set; } = [];
}
