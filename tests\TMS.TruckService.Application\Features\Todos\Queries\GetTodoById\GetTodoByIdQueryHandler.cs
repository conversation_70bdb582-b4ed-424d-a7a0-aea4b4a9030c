﻿using MapsterMapper;
using MediatR;
using TMS.SharedKernel.Domain;
using TMS.TruckService.Contracts.Todos;
using TMS.TruckService.Domain.Entities;

namespace TMS.TruckService.Application.Features.Todos.Queries.GetTodoById;

public class GetTodoByIdQueryHandler : IRequestHandler<GetTodoByIdQuery, TodoResponse?>
{
    private readonly IBaseRepository<Todo> _todoRepository;
    private readonly IMapper _mapper;

    public GetTodoByIdQueryHandler(IBaseRepository<Todo> todoRepository, IMapper mapper)
    {
        _todoRepository = todoRepository;
        _mapper = mapper;
    }

    public async Task<TodoResponse?> Handle(GetTodoByIdQuery request, CancellationToken cancellationToken)
    {
        var todo = await _todoRepository.GetByIdAsync(request.Id, cancellationToken);
        return todo != null ? _mapper.Map<TodoResponse>(todo) : null;
    }
}

