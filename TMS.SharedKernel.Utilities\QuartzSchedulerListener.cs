using Microsoft.Extensions.Logging;
using Npgsql;
using Quartz;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Global Quartz scheduler listener that handles errors gracefully to prevent application crashes
/// </summary>
public class QuartzSchedulerListener : ISchedulerListener
{
    private readonly ILogger<QuartzSchedulerListener> _logger;

    /// <summary>
    /// QuartzSchedulerListener
    /// </summary>
    /// <param name="_logger"></param>
    public QuartzSchedulerListener(ILogger<QuartzSchedulerListener> _logger)
    {
        this._logger = _logger;
    }

    /// <inheritdoc />
    public Task JobScheduled(ITrigger trigger, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Job scheduled: {JobKey}", trigger.JobKey);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobUnscheduled(TriggerKey triggerKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Job unscheduled: {TriggerKey}", triggerKey);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task TriggerFinalized(ITrigger trigger, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task TriggerPaused(TriggerKey triggerKey, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task TriggersPaused(string? triggerGroup, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task TriggerResumed(TriggerKey triggerKey, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task TriggersResumed(string? triggerGroup, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobAdded(IJobDetail jobDetail, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Job added: {JobKey}", jobDetail.Key);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobDeleted(JobKey jobKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Job deleted: {JobKey}", jobKey);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobPaused(JobKey jobKey, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobsPaused(string jobGroup, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobResumed(JobKey jobKey, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobsResumed(string jobGroup, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobInterrupted(JobKey jobKey, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Job interrupted: {JobKey}", jobKey);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulerError(string msg, SchedulerException cause, CancellationToken cancellationToken = default)
    {
        // This is the critical method that prevents application crashes
        var isDbConnectionError = cause.InnerException is NpgsqlException ||
                                   cause.InnerException?.InnerException is NpgsqlException ||
                                   cause.Message.Contains("database", StringComparison.OrdinalIgnoreCase) ||
                                   cause.Message.Contains("connection", StringComparison.OrdinalIgnoreCase);

        if (isDbConnectionError)
        {
            _logger.LogWarning(cause,
                "Quartz scheduler database connection error (possibly due to database recreation). " +
                "This is usually harmless if the database is being recreated. " +
                "Quartz will retry automatically. Error: {Message}", msg);
        }
        else
        {
            _logger.LogError(cause, "Quartz scheduler error: {Message}", msg);
        }

        // DO NOT throw - this prevents application crash
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulerInStandbyMode(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Quartz scheduler in standby mode");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulerStarted(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Quartz scheduler started successfully");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulerStarting(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Quartz scheduler starting...");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulerShutdown(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Quartz scheduler shutdown");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulerShuttingdown(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Quartz scheduler shutting down...");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SchedulingDataCleared(CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Quartz scheduling data cleared");
        return Task.CompletedTask;
    }
}
