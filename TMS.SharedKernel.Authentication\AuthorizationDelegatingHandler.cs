using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using TMS.SharedKernel.Authentication.Helpers;
using TMS.SharedKernel.Domain.Constants;
using TMS.SharedKernel.Domain.Extentions;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.SharedKernel.Authentication.Handlers;

/// <summary>
/// Delegating handler that adds authorization and context headers to outgoing HTTP requests.
/// Returns safe fallback responses on failures to prevent exceptions in calling code.
/// </summary>
public class AuthorizationDelegatingHandler : DelegatingHandler
{
    private const string M2MHeader = "X-Service-Auth";

    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly ILogger<AuthorizationDelegatingHandler> _logger;
    private readonly string? _defaultKey;

    public AuthorizationDelegatingHandler(
        ICurrentFactorProvider currentFactorProvider,
        IConfiguration configuration,
        ILogger<AuthorizationDelegatingHandler> logger)
    {
        _currentFactorProvider = currentFactorProvider;
        _logger = logger;

        // Cache the default key to avoid repeated configuration lookups
        _defaultKey = configuration?.GetValue<string>("DefaultKey");
    }

    /// <inheritdoc />
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            AddAuthenticationHeaders(request);
            AddContextHeaders(request);

            return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "HTTP request failed to {RequestUri}. Returning empty response.", request.RequestUri);
            return HttpResponseHelper.CreateSafeEmptyResponse();
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogWarning(ex, "Request to {RequestUri} was cancelled or timed out. Returning empty response.", request.RequestUri);
            return HttpResponseHelper.CreateSafeEmptyResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during request to {RequestUri}. Returning empty response.", request.RequestUri);
            return HttpResponseHelper.CreateSafeEmptyResponse();
        }
    }

    private void AddAuthenticationHeaders(HttpRequestMessage request)
    {
        if (!string.IsNullOrEmpty(_defaultKey))
        {
            request.Headers.TryAddWithoutValidation(M2MHeader, _defaultKey);
        }
    }

    private void AddContextHeaders(HttpRequestMessage request)
    {
        // Early return if provider is null
        if (_currentFactorProvider == null)
            return;

        // Cache frequently accessed values to avoid multiple property calls
        var currentFactorId = _currentFactorProvider.CurrentFactorId;
        var companyId = _currentFactorProvider.CompanyId;
        var departmentId = _currentFactorProvider.DepartmentId;

        request.Headers.TryAddWithoutValidation(DomainConstants.UserId, currentFactorId.ToString());
        request.Headers.TryAddWithoutValidation(DomainConstants.UserName,
            HeaderEncodingHelper.EncodeBase64(_currentFactorProvider.CurrentFactorName ?? string.Empty));
        request.Headers.TryAddWithoutValidation(DomainConstants.CompanyId, companyId.ToString());
        request.Headers.TryAddWithoutValidation(DomainConstants.DepartmentId, departmentId.ToString());

        var roles = _currentFactorProvider.Roles?.ToString();
        if (!string.IsNullOrEmpty(roles))
        {
            request.Headers.TryAddWithoutValidation(DomainConstants.Roles, roles);
        }
    }
}
