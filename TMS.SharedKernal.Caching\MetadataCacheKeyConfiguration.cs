namespace TMS.SharedKernal.Caching;

/// <summary>
/// Configuration for Redis cache keys for a specific entity type
/// </summary>
public class MetadataCacheKeyConfiguration
{
    /// <summary>
    /// The entity type this configuration applies to
    /// </summary>
    public Type EntityType { get; set; } = null!;

    /// <summary>
    /// Redis key prefix template. Use {prefix} placeholder for dynamic prefixes.
    /// Example: "metadata:companyid:entity-name" where {prefix} can be replaced with companyId
    /// </summary>
    public string RedisKeyTemplate { get; set; } = null!;

    /// <summary>
    /// Whether this cache key requires a prefix (e.g., companyId)
    /// </summary>
    public bool RequiresPrefix { get; set; }
}
