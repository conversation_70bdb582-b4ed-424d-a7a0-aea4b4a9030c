﻿using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Security;
using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.SharedKernel.Constants;

namespace TMS.SharedKernel.Utilities.Filters;

/// <summary>
/// Global exception handler that implements the <see cref="IExceptionHandler"/> interface.
/// Provides secure, environment-aware exception handling with structured problem details.
/// </summary>
/// <param name="logger">The logger instance to log exception details.</param>
/// <param name="environment">The hosting environment to determine response detail level.</param>
[ExcludeFromCodeCoverage]
public sealed class GlobalExceptionHandler(
    ILogger<GlobalExceptionHandler> logger,
    IHostEnvironment environment) : IExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly IHostEnvironment _environment = environment ?? throw new ArgumentNullException(nameof(environment));

    private const string UNEXPECTED_EXCEPTION_MESSAGE = "An unexpected error occurred while processing your request.";
    private const string PROBLEM_DETAILS_CONTENT_TYPE = "application/problem+json";

    // Static JsonSerializerOptions for better performance
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    /// <summary>
    /// Tries to handle the given exception and writes an appropriate response to the HTTP context.
    /// </summary>
    /// <param name="httpContext">The HTTP context to write the response to.</param>
    /// <param name="exception">The exception to handle.</param>
    /// <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the exception was handled.</returns>
    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(httpContext);
        ArgumentNullException.ThrowIfNull(exception);

        var exceptionContext = CreateExceptionContext(httpContext, exception);
        LogException(exceptionContext);

        // Set the HTTP response status code to match the problem details
        httpContext.Response.StatusCode = (int)exceptionContext.StatusCode;

        var problemDetails = CreateProblemDetails(exceptionContext);
        await WriteProblemDetailsResponse(httpContext, problemDetails, cancellationToken);

        return true;
    }

    /// <summary>
    /// Creates an exception context with relevant information for logging and response generation.
    /// </summary>
    private ExceptionContext CreateExceptionContext(HttpContext httpContext, Exception exception)
    {
        var (statusCode, title, userMessage, logLevel) = MapException(exception);
        var errorCode = GetErrorCode(exception);

        return new ExceptionContext
        {
            Exception = exception,
            HttpContext = httpContext,
            StatusCode = statusCode,
            Title = title,
            UserMessage = userMessage,
            LogLevel = logLevel,
            ErrorCode = errorCode,
            TraceId = httpContext.TraceIdentifier,
            RequestPath = httpContext.Request.Path,
            UserId = GetUserId(httpContext),
            IsProduction = _environment.IsProduction()
        };
    }

    /// <summary>
    /// Logs the exception with appropriate context and log level.
    /// </summary>
    private void LogException(ExceptionContext context)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object?>
        {
            ["TraceId"] = context.TraceId,
            ["UserId"] = context.UserId,
            ["RequestPath"] = context.RequestPath,
            ["StatusCode"] = (int)context.StatusCode,
            ["ExceptionType"] = context.Exception.GetType().Name
        });

        var message = "Exception occurred during request processing: {ExceptionType} - {Message}";

        _logger.Log(context.LogLevel, context.Exception, message,
            context.Exception.GetType().Name, context.Exception.Message);
    }

    /// <summary>
    /// Creates a problem details response object according to RFC 7807.
    /// </summary>
    private object CreateProblemDetails(ExceptionContext context)
    {
        //var detail = context.IsProduction ? context.UserMessage :
        //    context.Exception.Message ?? context.UserMessage;
        var detail = context.Exception.Message ?? context.UserMessage;
        var type = GetProblemType(context.StatusCode);

        var baseProblemDetails = new
        {
            type,
            title = context.Title,
            status = (int)context.StatusCode,
            detail,
            instance = context.RequestPath.ToString(),
            traceId = context.TraceId,
            errorCode = context.ErrorCode
        };

        // Handle different validation exception types
        if (context.Exception is FluentValidation.ValidationException fluentValidationException)
        {
            return CreateFluentValidationProblemDetails(baseProblemDetails, fluentValidationException, context);
        }

        if (context.Exception is DomainValidationException domainValidationException)
        {
            return CreateDomainValidationProblemDetails(baseProblemDetails, domainValidationException, context);
        }

        // Add stack trace in development environment for other exceptions
        if (!context.IsProduction && context.Exception.StackTrace is not null)
        {
            return new
            {
                baseProblemDetails.type,
                baseProblemDetails.title,
                baseProblemDetails.status,
                baseProblemDetails.detail,
                baseProblemDetails.instance,
                baseProblemDetails.traceId,
                baseProblemDetails.errorCode,
                stackTrace = context.Exception.StackTrace
            };
        }

        return baseProblemDetails;
    }

    /// <summary>
    /// Creates problem details for FluentValidation exceptions.
    /// </summary>
    private object CreateFluentValidationProblemDetails(
        dynamic baseProblemDetails,
        FluentValidation.ValidationException validationException,
        ExceptionContext context)
    {
        var errors = validationException.Errors
            .GroupBy(x => x.PropertyName)
            .ToDictionary(
                g => ToCamelCase(g.Key),
                g => g.Select(x => x.ErrorMessage).ToArray()
            );

        var problemDetails = new
        {
            type = baseProblemDetails.type,
            title = baseProblemDetails.title,
            status = baseProblemDetails.status,
            detail = baseProblemDetails.detail,
            instance = baseProblemDetails.instance,
            traceId = baseProblemDetails.traceId,
            errorCode = baseProblemDetails.errorCode,
            errors,
            validationType = "FluentValidation"
        };

        if (!context.IsProduction && validationException.StackTrace is not null)
        {
            return new
            {
                problemDetails.type,
                problemDetails.title,
                problemDetails.status,
                problemDetails.detail,
                problemDetails.instance,
                problemDetails.traceId,
                problemDetails.errorCode,
                problemDetails.errors,
                problemDetails.validationType,
                stackTrace = validationException.StackTrace
            };
        }

        return problemDetails;
    }

    /// <summary>
    /// Creates problem details for domain validation exceptions.
    /// </summary>
    private object CreateDomainValidationProblemDetails(
        dynamic baseProblemDetails,
        DomainValidationException domainValidationException,
        ExceptionContext context)
    {
        var errors = domainValidationException.ValidationErrors
            .ToDictionary(
                kvp => ToCamelCase(kvp.Key),
                kvp => kvp
            );

        var problemDetails = new
        {
            type = baseProblemDetails.type,
            title = baseProblemDetails.title,
            status = baseProblemDetails.status,
            detail = baseProblemDetails.detail,
            instance = baseProblemDetails.instance,
            traceId = baseProblemDetails.traceId,
            errorCode = baseProblemDetails.errorCode,
            errors,
            validationType = "Domain"
        };

        // Add specific properties based on exception type
        if (domainValidationException is EntityValidationException entityException)
        {
            var entityProblemDetails = new
            {
                problemDetails.type,
                problemDetails.title,
                problemDetails.status,
                problemDetails.detail,
                problemDetails.instance,
                problemDetails.traceId,
                problemDetails.errorCode,
                problemDetails.errors,
                problemDetails.validationType,
                entityType = entityException.EntityType
            };

            if (!context.IsProduction && domainValidationException.StackTrace is not null)
            {
                return new
                {
                    entityProblemDetails.type,
                    entityProblemDetails.title,
                    entityProblemDetails.status,
                    entityProblemDetails.detail,
                    entityProblemDetails.instance,
                    entityProblemDetails.traceId,
                    entityProblemDetails.errorCode,
                    entityProblemDetails.errors,
                    entityProblemDetails.validationType,
                    entityProblemDetails.entityType,
                    stackTrace = domainValidationException.StackTrace
                };
            }

            return entityProblemDetails;
        }

        if (domainValidationException is AggregateValidationException aggregateException)
        {
            var aggregateProblemDetails = new
            {
                problemDetails.type,
                problemDetails.title,
                problemDetails.status,
                problemDetails.detail,
                problemDetails.instance,
                problemDetails.traceId,
                problemDetails.errorCode,
                problemDetails.errors,
                problemDetails.validationType,
                aggregateType = aggregateException.AggregateType,
                aggregateId = aggregateException.AggregateId
            };

            if (!context.IsProduction && domainValidationException.StackTrace is not null)
            {
                return new
                {
                    aggregateProblemDetails.type,
                    aggregateProblemDetails.title,
                    aggregateProblemDetails.status,
                    aggregateProblemDetails.detail,
                    aggregateProblemDetails.instance,
                    aggregateProblemDetails.traceId,
                    aggregateProblemDetails.errorCode,
                    aggregateProblemDetails.errors,
                    aggregateProblemDetails.validationType,
                    aggregateProblemDetails.aggregateType,
                    aggregateProblemDetails.aggregateId,
                    stackTrace = domainValidationException.StackTrace
                };
            }

            return aggregateProblemDetails;
        }

        // Default domain validation exception
        if (!context.IsProduction && domainValidationException.StackTrace is not null)
        {
            return new
            {
                problemDetails.type,
                problemDetails.title,
                problemDetails.status,
                problemDetails.detail,
                problemDetails.instance,
                problemDetails.traceId,
                problemDetails.errorCode,
                problemDetails.errors,
                problemDetails.validationType,
                stackTrace = domainValidationException.StackTrace
            };
        }

        return problemDetails;
    }

    /// <summary>
    /// Writes the problem details response to the HTTP context.
    /// </summary>
    private static async Task WriteProblemDetailsResponse(
        HttpContext httpContext,
        object problemDetails,
        CancellationToken cancellationToken)
    {
        httpContext.Response.ContentType = PROBLEM_DETAILS_CONTENT_TYPE;

        // Apply security headers to exception responses
        httpContext.Response.Headers["Server"] = "TMS";
        httpContext.Response.Headers["X-Powered-By"] = "TMS";
        httpContext.Response.Headers["X-Content-Type-Options"] = "nosniff";
        httpContext.Response.Headers["X-Frame-Options"] = "DENY";
        httpContext.Response.Headers["X-XSS-Protection"] = "1; mode=block";
        httpContext.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
        httpContext.Response.Headers["Content-Security-Policy"] = "default-src 'self'";

        var json = JsonSerializer.Serialize(problemDetails, JsonOptions);
        await httpContext.Response.WriteAsync(json, cancellationToken);
    }

    /// <summary>
    /// Maps exceptions to appropriate HTTP status codes, titles, user messages, and log levels.
    /// </summary>
    private static (HttpStatusCode StatusCode, string Title, string UserMessage, LogLevel LogLevel) MapException(Exception exception)
    {
        return exception switch
        {
            // FluentValidation exceptions
            FluentValidation.ValidationException =>
                (HttpStatusCode.BadRequest, "Validation Failed", "One or more validation errors occurred.", LogLevel.Warning),

            // Domain validation exceptions
            BusinessRuleValidationException =>
                (HttpStatusCode.BadRequest, "Business Rule Violation", "The operation violates one or more business rules.", LogLevel.Warning),

            EntityValidationException =>
                (HttpStatusCode.BadRequest, "Entity Validation Failed", "The entity data is invalid.", LogLevel.Warning),

            AggregateValidationException =>
                (HttpStatusCode.BadRequest, "Aggregate Validation Failed", "The aggregate is in an invalid state.", LogLevel.Warning),

            DomainValidationException =>
                (HttpStatusCode.BadRequest, "Domain Validation Failed", "One or more domain validation errors occurred.", LogLevel.Warning),

            SecurityTokenMalformedException =>
                (HttpStatusCode.Unauthorized, "Token Validation Failed", "Authentication token validation failed.", LogLevel.Warning),

            // System validation exceptions
            ArgumentOutOfRangeException =>
                (HttpStatusCode.BadRequest, "Bad Request", "One or more parameters are out of the valid range.", LogLevel.Warning),

            ArgumentNullException =>
                (HttpStatusCode.BadRequest, "Bad Request", "Required information is missing.", LogLevel.Warning),

            ArgumentException =>
                (HttpStatusCode.BadRequest, "Bad Request", "The request contains invalid parameters.", LogLevel.Warning),

            // Security exceptions
            SecurityTokenExpiredException =>
                (HttpStatusCode.Unauthorized, "Token Expired", "Your session has expired. Please log in again.", LogLevel.Information),

            SecurityTokenValidationException =>
                (HttpStatusCode.Unauthorized, "Token Validation Failed", "Authentication token validation failed.", LogLevel.Warning),

            SecurityException =>
                (HttpStatusCode.Unauthorized, "Security Error", "A security error occurred. Please check your credentials.", LogLevel.Warning),

            // Operation exceptions
            InvalidOperationException invOpEx when invOpEx.Message.Contains("No authenticationScheme was specified") =>
                (HttpStatusCode.Unauthorized, "Unauthorized", "Authentication is required to access this resource.", LogLevel.Warning),

            InvalidOperationException =>
                (HttpStatusCode.BadRequest, "Invalid Operation", "The requested operation cannot be performed.", LogLevel.Warning),

            UnauthorizedAccessException =>
                (HttpStatusCode.Unauthorized, "Unauthorized", "Access denied. Please check your credentials.", LogLevel.Warning),

            KeyNotFoundException =>
                (HttpStatusCode.NotFound, "Not Found", "The requested resource was not found.", LogLevel.Information),

            NotImplementedException =>
                (HttpStatusCode.NotImplemented, "Not Implemented", "This feature is not yet implemented.", LogLevel.Error),

            // Timeout exceptions
            TimeoutException =>
                (HttpStatusCode.RequestTimeout, "Request Timeout", "The request took too long to process.", LogLevel.Warning),

            TaskCanceledException =>
                (HttpStatusCode.RequestTimeout, "Request Timeout", "The request was cancelled or timed out.", LogLevel.Warning),

            // Payload size exceptions
            Microsoft.AspNetCore.Http.BadHttpRequestException badHttpEx when badHttpEx.StatusCode == 413 =>
                ((HttpStatusCode)413, "Payload Too Large", "The request payload exceeds the maximum allowed size.", LogLevel.Warning),

            Microsoft.AspNetCore.Http.BadHttpRequestException badHttpEx when badHttpEx.Message.Contains("request body too large", StringComparison.OrdinalIgnoreCase) =>
                ((HttpStatusCode)413, "Payload Too Large", "The request payload exceeds the maximum allowed size.", LogLevel.Warning),

            IOException ioEx when ioEx.Message.Contains("request body", StringComparison.OrdinalIgnoreCase) =>
                ((HttpStatusCode)413, "Payload Too Large", "The request payload exceeds the maximum allowed size.", LogLevel.Warning),

            // HTTP exceptions
            HttpRequestException httpEx when httpEx.Data.Contains("StatusCode") =>
                ((HttpStatusCode)(httpEx.Data["StatusCode"] ?? HttpStatusCode.BadGateway),
                 "External Service Error", "An external service is currently unavailable.", LogLevel.Error),

            // Default
            _ => (HttpStatusCode.InternalServerError, "Internal Server Error", UNEXPECTED_EXCEPTION_MESSAGE, LogLevel.Error)
        };
    }

    /// <summary>
    /// Gets the error code for the given exception type.
    /// </summary>
    private static string GetErrorCode(Exception exception)
    {
        // Check if it's a domain validation exception with a custom error code
        if (exception is DomainValidationException domainException && !string.IsNullOrEmpty(domainException.ErrorCode))
        {
            return domainException.ErrorCode;
        }

        return exception switch
        {
            // FluentValidation exceptions
            FluentValidation.ValidationException => CommonErrorCodes.INVALID_VALUE,

            // Domain validation exceptions (fallback when no custom error code is provided)
            BusinessRuleValidationException => CommonErrorCodes.BUSINESS_RULE_VIOLATION,
            EntityValidationException => CommonErrorCodes.ENTITY_VALIDATION_FAILED,
            AggregateValidationException => CommonErrorCodes.AGGREGATE_VALIDATION_FAILED,
            DomainValidationException => CommonErrorCodes.DOMAIN_VALIDATION_FAILED,

            // System validation exceptions
            SecurityTokenMalformedException => CommonErrorCodes.TOKEN_INVALID,

            ArgumentOutOfRangeException => CommonErrorCodes.VALUE_OUT_OF_RANGE,
            ArgumentNullException => CommonErrorCodes.REQUIRED_FIELD_MISSING,
            ArgumentException => CommonErrorCodes.INVALID_VALUE,

            // Security exceptions
            SecurityTokenExpiredException => CommonErrorCodes.TOKEN_EXPIRED,
            SecurityTokenValidationException => CommonErrorCodes.TOKEN_INVALID,
            SecurityException => CommonErrorCodes.UNAUTHORIZED,

            // Operation exceptions
            InvalidOperationException invOpEx when invOpEx.Message.Contains("No authenticationScheme was specified") => CommonErrorCodes.UNAUTHORIZED,
            InvalidOperationException => CommonErrorCodes.OPERATION_NOT_ALLOWED,
            UnauthorizedAccessException => CommonErrorCodes.FORBIDDEN,

            KeyNotFoundException => CommonErrorCodes.ENTITY_NOT_FOUND,
            NotImplementedException => CommonErrorCodes.INTERNAL_SERVER_ERROR,

            // Timeout exceptions
            TimeoutException => CommonErrorCodes.TIMEOUT_ERROR,
            TaskCanceledException => CommonErrorCodes.TIMEOUT_ERROR,

            // Payload size exceptions
            Microsoft.AspNetCore.Http.BadHttpRequestException badHttpEx when badHttpEx.StatusCode == 413 => CommonErrorCodes.INVALID_VALUE,
            Microsoft.AspNetCore.Http.BadHttpRequestException badHttpEx when badHttpEx.Message.Contains("request body too large", StringComparison.OrdinalIgnoreCase) => CommonErrorCodes.INVALID_VALUE,
            IOException ioEx when ioEx.Message.Contains("request body", StringComparison.OrdinalIgnoreCase) => CommonErrorCodes.INVALID_VALUE,

            // HTTP exceptions
            HttpRequestException => CommonErrorCodes.EXTERNAL_SERVICE_ERROR,

            // Default
            _ => CommonErrorCodes.INTERNAL_SERVER_ERROR
        };
    }

    /// <summary>
    /// Gets the problem type URI based on the HTTP status code.
    /// </summary>
    private static string GetProblemType(HttpStatusCode statusCode)
    {
        var statusCodeInt = (int)statusCode;
        return statusCodeInt switch
        {
            400 => "https://tools.ietf.org/html/rfc7231#section-6.5.1",
            401 => "https://tools.ietf.org/html/rfc7235#section-3.1",
            404 => "https://tools.ietf.org/html/rfc7231#section-6.5.4",
            408 => "https://tools.ietf.org/html/rfc7231#section-6.5.7",
            413 => "https://tools.ietf.org/html/rfc7231#section-6.5.11",
            500 => "https://tools.ietf.org/html/rfc7231#section-6.6.1",
            501 => "https://tools.ietf.org/html/rfc7231#section-6.6.2",
            _ => "https://tools.ietf.org/html/rfc7231#section-6.6.1"
        };
    }

    /// <summary>
    /// Extracts user ID from the HTTP context if available.
    /// </summary>
    private static string? GetUserId(HttpContext httpContext)
    {
        var userId = httpContext.User?.FindFirst("employeeId")?.Value // Standard claim for unique identifier
                  ?? httpContext.User?.FindFirst("sub")?.Value;       // Common JWT 'subject' claim
        return userId;
    }

    /// <summary>
    /// Converts property names to camelCase for consistent JSON formatting.
    /// </summary>
    private static string ToCamelCase(string input)
    {
        if (string.IsNullOrEmpty(input) || char.IsLower(input[0]))
            return input;

        return char.ToLowerInvariant(input[0]) + input[1..];
    }

    /// <summary>
    /// Context object containing exception and request information.
    /// </summary>
    private sealed record ExceptionContext
    {
        public required Exception Exception { get; init; }
        public required HttpContext HttpContext { get; init; }
        public required HttpStatusCode StatusCode { get; init; }
        public required string Title { get; init; }
        public required string UserMessage { get; init; }
        public required LogLevel LogLevel { get; init; }
        public required string ErrorCode { get; init; }
        public required string TraceId { get; init; }
        public required PathString RequestPath { get; init; }
        public required string? UserId { get; init; }
        public required bool IsProduction { get; init; }
    }
}
