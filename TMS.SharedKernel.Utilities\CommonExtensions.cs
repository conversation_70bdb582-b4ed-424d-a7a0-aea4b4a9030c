﻿using System.Diagnostics.CodeAnalysis;
using System.IO.Compression;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Npgsql;
using TMS.SharedKernel.Domain.Services.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for logging.
/// </summary>
[ExcludeFromCodeCoverage]
public static class CommonExtensions
{
    /// <summary>
    /// AddCompression
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddCompression(this IServiceCollection services)
    {
        services.AddResponseCompression(options =>
        {
            options.EnableForHttps = true;
            options.Providers.Add<BrotliCompressionProvider>();
            options.Providers.Add<GzipCompressionProvider>();
        });

        services.Configure<BrotliCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.Fastest;
        });

        services.Configure<GzipCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.SmallestSize;
        });

        return services;
    }

    public static IServiceCollection AddRouteControllers(this IServiceCollection services)
    {
        services.AddControllers();
        services.AddRouting(options => options.LowercaseUrls = true);

        return services;
    }

    public static void UseBaseRequired(this WebApplication app, string env, string serviceName, string serviceVersion)
    {
        // Configure the HTTP request pipeline
        var isDev = app.Environment.IsDevelopment() || string.Equals(env, "development", StringComparison.OrdinalIgnoreCase);
        if (isDev)
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint($"/swagger/{serviceVersion}/swagger.json", $"{serviceName} {serviceVersion}");
                c.RoutePrefix = string.Empty;
            });
        }

        app.UseQueryStringAuthentication();

        app.UseExceptionHandler();
        app.UseHttpsRedirection();
        app.UseCors("A1");
        app.UseResponseCompression();

        app.UseSecureHeaders();
        
        app.UseAuthentication();
        app.UseAuthorization();

        app.MapControllers();
    }

    public static void AddBaseRequired(this WebApplicationBuilder builder, string otlpEndpoint, string otlpEndpointKey, 
        string env, string serviceName, string serviceVersion, long maxRequestBodySize = 10 * 1024 * 1024)
    {
        builder.AddCentralLogs(otlpEndpoint, key: otlpEndpointKey, env, serviceName,
                    serviceVersion, protocol: Serilog.Sinks.OpenTelemetry.OtlpProtocol.HttpProtobuf);

        // Add services to the container
        builder.Services.AddRouteControllers();

        // Add FluentValidation
        builder.Services.AddFluentValidationAutoValidation()
                        .AddFluentValidationClientsideAdapters();

        // Adding swagger extensions
        builder.Services
            .AddApiVersioningAndExplorer()
            .AddEndpointsApiExplorer();

        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc(serviceVersion, new()
            {
                Title = serviceName,
                Version = serviceVersion,
                Description = $"A comprehensive API for {serviceName} with validation and mapping"
            });
        });

        // Add exception handling//
        builder.Services.AddExceptionHandler();
        // Add repositories
        builder.Services.AddRepositories();
        builder.Services.AddCurrentFactorProvider();

        builder.Services.AddCompression();

        builder.Services.AddCustomCors(builder.Configuration);

        builder.Services.AddDatabaseInitializer();

        builder.AddMaxRequestBodySize(maxRequestBodySize);
    }

    /// <summary>
    /// AddMaxRequestBodySize
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="maxRequestBodySize"></param>
    public static void AddMaxRequestBodySize(this WebApplicationBuilder builder, long maxRequestBodySize = 10 * 1024 * 1024)
    {
        // 1. Kestrel (default server)
        builder.WebHost.ConfigureKestrel(options =>
        {
            options.Limits.MaxRequestBodySize = maxRequestBodySize;
        });

        // 2. IIS In-Process (optional - only if deploying to IIS)
        //builder.Services.Configure<IISServerOptions>(options =>
        //{
        //    options.MaxRequestBodySize = maxRequestBodySize;
        //});

        // 3. Form uploads (optional - adds additional validation for multipart forms)
        builder.Services.Configure<FormOptions>(options =>
        {
            options.MultipartBodyLengthLimit = maxRequestBodySize;
        });
    }
}
