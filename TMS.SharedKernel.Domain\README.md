# TMS.SharedKernel.Domain

Core contracts and interfaces for TMS SharedKernel projects, providing common abstractions for domain-driven design patterns.

## Features

- **Entity Base Classes**: Foundation classes for domain entities
- **Audit Tracking**: Built-in created/modified date and user tracking
- **Concurrency Control**: Optimistic concurrency with version tokens
- **Domain Events**: Event-driven architecture support with MediatR
- **Repository Patterns**: Generic repository and unit of work interfaces
- **Provider Interfaces**: Current user/factor provider abstractions
- **Field Length Constants**: Standardized field lengths across applications

## Core Components

### Entity Base Classes

```csharp
// Basic entity with int ID
public class Product : EntityBase
{
    public string Name { get; set; }
    public decimal Price { get; set; }
}

// Entity with custom key type
public class User : EntityBase<Guid>
{
    public string Email { get; set; }
    public string FirstName { get; set; }
}

// Auditable entity with tracking
public class Order : AuditableEntity
{
    public string OrderNumber { get; set; }
    public decimal Total { get; set; }
    // CreatedDate, UpdatedDate, CreatedBy, UpdatedBy automatically included
}

// Concurrency-controlled entity
public class Inventory : ConcurrencyControlledEntity
{
    public int Quantity { get; set; }
    // Version property automatically included for optimistic concurrency
}

// Combined auditable and concurrency-controlled
public class Customer : AuditableConcurrencyControlledEntity
{
    public string Name { get; set; }
    public string Email { get; set; }
    // Includes audit fields AND version control
}
```

### Domain Events

```csharp
// Define domain event
public class OrderCreatedEvent : DomainEventBase
{
    public int OrderId { get; }
    public decimal Amount { get; }
    
    public OrderCreatedEvent(int orderId, decimal amount)
    {
        OrderId = orderId;
        Amount = amount;
    }
}

// Raise event in entity
public class Order : AuditableEntity
{
    public void CompleteOrder()
    {
        Status = OrderStatus.Completed;
        RegisterDomainEvent(new OrderCompletedEvent(Id, Total));
    }
}
```

### Field Length Constants

```csharp
using TMS.SharedKernel.Domain.Constants;

public class User : AuditableEntity
{
    [MaxLength(FieldLengths.MediumText)]  // 100 characters
    public string FirstName { get; set; }
    
    [MaxLength(FieldLengths.MediumText)]  // 100 characters
    public string LastName { get; set; }
    
    [MaxLength(FieldLengths.LongText)]    // 512 characters
    public string Notes { get; set; }
}
```

### Current Factor Provider

```csharp
// Implement current user provider
public class HttpCurrentFactorProvider : ICurrentFactorProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    
    public Guid CurrentFactorId => GetCurrentUserId();
    public string[] Roles => GetCurrentUserRoles();
    
    public bool IsServiceUser() => 
        _httpContextAccessor.HttpContext?.User?.Identity?.AuthenticationType == "Service";
}
```

## Available Interfaces

### Entity Interfaces
- `IEntity<TKey>` - Basic entity with ID
- `IAuditable` - Audit date and factor tracking
- `IConcurrencyControlled` - Optimistic concurrency control
- `ICreatedDateTracking` - Created date only
- `IUpdatedDateTracking` - Updated date only
- `IFactorTracking<T>` - Created/updated by tracking

### Provider Interfaces
- `ICurrentFactorProvider` - Current user/factor information
- `IOverridableCurrentFactorProvider` - Override current factor (for background jobs)

## Constants

### Field Lengths
- `ShortText` = 32 (usernames, codes)
- `MediumText` = 100 (names, emails)
- `LongText` = 512 (comments, notes)
- `VeryLongText` = 4096 (detailed descriptions)
- `DecimalPrecision` = 20 (total digits)
- `DecimalScale` = 2 (decimal places)

## Usage in Your Project

```csharp
// Install the package
// dotnet add package TMS.SharedKernel.Domain

// Define your entities
public class BlogPost : AuditableConcurrencyControlledEntity
{
    [MaxLength(FieldLengths.MediumText)]
    public string Title { get; set; } = string.Empty;
    
    [MaxLength(FieldLengths.VeryLongText)]
    public string Content { get; set; } = string.Empty;
    
    public void Publish()
    {
        IsPublished = true;
        PublishedDate = DateTime.UtcNow;
        RegisterDomainEvent(new BlogPostPublishedEvent(Id, Title));
    }
}

// Handle domain events
public class BlogPostPublishedHandler : INotificationHandler<BlogPostPublishedEvent>
{
    public async Task Handle(BlogPostPublishedEvent notification, CancellationToken cancellationToken)
    {
        // Send notifications, update search index, etc.
    }
}
```

## Migration Notes

### Obsolete Components
- `EventMessageBase` - Use `TMS.SharedKernel.MessageService` package instead
- `IEventMessage` - Use new interfaces in `TMS.SharedKernel.MessageService`
- `ICurrentFactorProvider<TKey>` - Use generic version with proper constraints

## Best Practices

1. **Entity Design**: Inherit from appropriate base classes based on your needs
2. **Domain Events**: Use for cross-aggregate communication and side effects
3. **Audit Tracking**: Leverage built-in audit fields for compliance and debugging
4. **Concurrency**: Use version tokens for entities that require optimistic concurrency
5. **Field Lengths**: Use constants for consistent field sizing across your domain

## Dependencies

- **MediatR** - For domain event handling
- **Microsoft.AspNetCore.App** - For web application integration