﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using TMS.SharedKernel.Domain;

namespace TMS.SharedKernel.EntityFrameworkCore;

/// <inheritdoc/>
public sealed class UnitOfWork : IUnitOfWork, IDisposable
{
    private readonly BaseDbContext _dbContext;
    private IDbContextTransaction? _transaction;
    private bool _disposed;

    public UnitOfWork(BaseDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    /// <inheritdoc/>
    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        if (_transaction is not null)
            throw new InvalidOperationException("A transaction is already in progress.");
            
        _transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
    }

    /// <inheritdoc/>
    public async Task<IDbContextTransaction> BeginTransactionWithContextAsync(CancellationToken cancellationToken = default)
    {
        await BeginTransactionAsync(cancellationToken);
        return _transaction!;
    }

    /// <inheritdoc/>
    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ThrowIfNoTransaction();

        try
        {
            await _transaction!.CommitAsync(cancellationToken);
        }
        catch
        {
            await RollbackTransactionAsync(cancellationToken);
            throw;
        }
        finally
        {
            await DisposeTransactionAsync();
        }
    }

    /// <inheritdoc/>
    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ThrowIfNoTransaction();

        try
        {
            await _transaction!.RollbackAsync(cancellationToken);
        }
        finally
        {
            await DisposeTransactionAsync();
        }
    }

    /// <inheritdoc/>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        return await _dbContext.SaveChangesAsync(cancellationToken);
    }

    /// <inheritdoc/>
    public async Task ExecuteTransactionAsync(Action operation, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(operation);

        var strategy = _dbContext.Database.CreateExecutionStrategy();
        await strategy.ExecuteAsync(async () =>
        {
            await using var transaction = await BeginTransactionWithContextAsync(cancellationToken);
            try
            {
                operation();
                await SaveChangesAsync(cancellationToken);
                await CommitTransactionAsync(cancellationToken);
            }
            catch
            {
                // Transaction will be disposed automatically, triggering rollback
                throw;
            }
        });
    }

    /// <inheritdoc/>
    public async Task<TOutput> ExecuteTransactionAsync<TOutput>(
        Func<Task<TOutput>> operation,
        CancellationToken cancellationToken = default) where TOutput : notnull
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(operation);

        var strategy = _dbContext.Database.CreateExecutionStrategy();
        return await strategy.ExecuteAsync(async () =>
        {
            await using var transaction = await BeginTransactionWithContextAsync(cancellationToken);
            try
            {
                var result = await operation();
                await SaveChangesAsync(cancellationToken);
                await CommitTransactionAsync(cancellationToken);
                return result;
            }
            catch
            {
                // Transaction will be disposed automatically, triggering rollback
                throw;
            }
        });
    }

    /// <inheritdoc/>
    public async Task ExecuteTransactionAsync(
        Func<Task> operation, 
        CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(operation);

        var strategy = _dbContext.Database.CreateExecutionStrategy();
        await strategy.ExecuteAsync(async () =>
        {
            await using var transaction = await BeginTransactionWithContextAsync(cancellationToken);
            try
            {
                await operation();
                await SaveChangesAsync(cancellationToken);
                await CommitTransactionAsync(cancellationToken);
            }
            catch
            {
                // Transaction will be disposed automatically, triggering rollback
                throw;
            }
        });
    }

    private void ThrowIfDisposed()
    {
        ObjectDisposedException.ThrowIf(_disposed, this);
    }

    private void ThrowIfNoTransaction()
    {
        if (_transaction is null)
            throw new InvalidOperationException("No transaction is in progress.");
    }

    private async Task DisposeTransactionAsync()
    {
        if (_transaction is not null)
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _transaction?.Dispose();
            _disposed = true;
        }
    }
}
