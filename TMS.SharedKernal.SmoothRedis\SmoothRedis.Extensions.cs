﻿namespace TMS.SharedKernal.SmoothRedis.Extensions;

using global::Microsoft.Extensions.Configuration;
using global::Microsoft.Extensions.DependencyInjection;
using TMS.SharedKernal.SmoothRedis;
using StackExchange.Redis;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Add SmoothRedis with a simple connection string
    /// </summary>
    public static IServiceCollection AddSmoothRedis(this IServiceCollection services, string connectionString, int database = 0, SerializerType serializerType = SerializerType.Newtonsoft)
    {
        services.AddSingleton<ISmoothRedis>(provider =>
            SmoothRedisClient.ConnectAsync(connectionString, database, serializerType).GetAwaiter().GetResult()
            );

        return services;
    }

    /// <summary>
    /// Add SmoothRedis from IConfiguration (reads from Redis section or ConnectionStrings:Redis)
    /// Also registers IConnectionMultiplexer for low-level Redis operations
    /// </summary>
    public static IServiceCollection AddSmoothRedis(this IServiceCollection services, IConfiguration configuration)
    {
        // Try to read from Redis configuration section first
        var redisSection = configuration.GetSection("Redis");
        ConfigurationOptions configOptions;
        int database = 0;

        if (redisSection.Exists())
        {
            var redisConfig = redisSection.Get<RedisConfiguration>();
            if (redisConfig != null)
            {
                configOptions = redisConfig.ToConfigurationOptions();
                database = redisConfig.Database;
            }
            else
            {
                // Fallback to connection string
                var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
                database = configuration.GetValue<int>("Redis:Database", 0);
                configOptions = ConfigurationOptions.Parse(connectionString);
            }
        }
        else
        {
            // Fallback to connection string
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
            database = configuration.GetValue<int>("Redis:Database", 0);
            configOptions = ConfigurationOptions.Parse(connectionString);
        }

        configOptions.AllowAdmin = true; // Enable admin commands if needed

        // Register IConnectionMultiplexer as singleton (for jobs that need low-level access)
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            return ConnectionMultiplexer.Connect(configOptions);
        });

        // Register ISmoothRedis using the same ConnectionMultiplexer
        services.AddSingleton<ISmoothRedis>(provider =>
        {
            var connection = provider.GetRequiredService<IConnectionMultiplexer>();
            return SmoothRedisClient.ConnectAsync(configOptions, database).GetAwaiter().GetResult();
        });

        return services;
    }

    /// <summary>
    /// Add SmoothRedis with RedisConfiguration object
    /// </summary>
    public static IServiceCollection AddSmoothRedis(this IServiceCollection services, RedisConfiguration config)
    {
        services.AddSingleton<ISmoothRedis>(provider =>
            SmoothRedisClient.ConnectAsync(config).GetAwaiter().GetResult());

        return services;
    }

    /// <summary>
    /// Add SmoothRedis with StackExchange.Redis ConfigurationOptions
    /// </summary>
    public static IServiceCollection AddSmoothRedis(this IServiceCollection services, ConfigurationOptions configOptions, int database = 0, SerializerType serializerType = SerializerType.Newtonsoft)
    {
        services.AddSingleton<ISmoothRedis>(provider =>
            SmoothRedisClient.ConnectAsync(configOptions, database, serializerType).GetAwaiter().GetResult());

        return services;
    }

    /// <summary>
    /// Add SmoothRedis with retry logic and health checks
    /// </summary>
    public static IServiceCollection AddSmoothRedisWithRetry(this IServiceCollection services,
        string connectionString,
        int database = 0,
        int maxRetries = 3,
        TimeSpan? initialDelay = null,
        SerializerType serializerType = SerializerType.Newtonsoft)
    {
        var delay = initialDelay ?? TimeSpan.FromSeconds(1);

        services.AddSingleton<ISmoothRedis>(provider =>
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    var redis = SmoothRedisClient.ConnectAsync(connectionString, database, serializerType).GetAwaiter().GetResult();

                    // Verify connection with health check
                    if (redis.HealthCheckAsync().GetAwaiter().GetResult())
                    {
                        return redis;
                    }

                    //await redis.DisposeAsync();
                }
                catch (Exception ex) when (i < maxRetries - 1)
                {
                    Console.WriteLine($"Redis connection attempt {i + 1} failed: {ex.Message}. Retrying in {delay}...");
                    Task.Delay(delay).GetAwaiter().GetResult();
                    delay = TimeSpan.FromMilliseconds(delay.TotalMilliseconds * 1.5); // Exponential backoff
                }
            }

            throw new InvalidOperationException($"Failed to connect to Redis after {maxRetries} attempts");
        });

        return services;
    }

    /// <summary>
    /// Add SmoothRedis with comprehensive configuration from IConfiguration with retry logic
    /// </summary>
    public static IServiceCollection AddSmoothRedisWithRetry(this IServiceCollection services, IConfiguration configuration)
    {
        var maxRetries = configuration.GetValue<int>("Redis:MaxRetries", 3);
        var initialDelay = TimeSpan.FromMilliseconds(configuration.GetValue<int>("Redis:RetryDelayMs", 1000));

        services.AddSingleton<ISmoothRedis>(provider =>
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    // Try to read from Redis configuration section first
                    var redisSection = configuration.GetSection("Redis");
                    ISmoothRedis redis;

                    if (redisSection.Exists())
                    {
                        var redisConfig = redisSection.Get<RedisConfiguration>();
                        if (redisConfig != null)
                        {
                            redis = SmoothRedisClient.ConnectAsync(redisConfig).GetAwaiter().GetResult();
                        }
                        else
                        {
                            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
                            var database = configuration.GetValue<int>("Redis:Database", 0);
                            redis = SmoothRedisClient.ConnectAsync(connectionString, database).GetAwaiter().GetResult();
                        }
                    }
                    else
                    {
                        var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
                        redis = SmoothRedisClient.ConnectAsync(connectionString).GetAwaiter().GetResult();
                    }

                    // Verify connection with health check
                    if (redis.HealthCheckAsync().GetAwaiter().GetResult())
                    {
                        return redis;
                    }

                    //await redis.DisposeAsync();
                }
                catch (Exception ex) when (i < maxRetries - 1)
                {
                    Console.WriteLine($"Redis connection attempt {i + 1} failed: {ex.Message}. Retrying in {initialDelay}...");
                    Task.Delay(initialDelay).GetAwaiter().GetResult();
                    initialDelay = TimeSpan.FromMilliseconds(initialDelay.TotalMilliseconds * 1.5); // Exponential backoff
                }
            }

            throw new InvalidOperationException($"Failed to connect to Redis after {maxRetries} attempts");
        });

        return services;
    }
}

public static class CacheExtensions
{
    public static async Task<T> GetOrSetAsync<T>(this ICacheBuilder cache, string key,
        Func<T> factory, TimeSpan? expiry = null) where T : class
    {
        return await cache.GetOrSetAsync(key, () => Task.FromResult(factory()), expiry);
    }

    public static async Task<string> GetOrSetAsync(this ICacheBuilder cache, string key,
        Func<string> factory, TimeSpan? expiry = null)
    {
        return await cache.GetOrSetAsync(key, () => Task.FromResult(factory()), expiry);
    }
}

public static class CollectionExtensions
{
    public static async Task<List<T>> AddAndTrimAsync<T>(this IListBuilder<T> list,
        T item, int maxLength) where T : class
    {
        await list.PushAsync(item);
        return await list.TrimAndGetAsync(maxLength);
    }

    public static async Task<bool> AddUniqueAsync<T>(this ISetBuilder<T> set, T item) where T : class
    {
        return await set.AddAsync(item);
    }
}
