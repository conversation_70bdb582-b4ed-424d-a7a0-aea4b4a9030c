using Confluent.Kafka;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.Kafka.Configuration;
using TMS.SharedKernal.Kafka.Services;
using Xunit;

namespace TMS.SharedKernal.Kafka.Tests;

/// <summary>
/// Integration tests that simulate real-world consumer scenarios
/// </summary>
public class KafkaConsumerIntegrationTests
{
    [Fact]
    public async Task ConsumerFlow_SuccessfulProcessing_NoRetries()
    {
        // Arrange
        var scenario = new ConsumerTestScenario()
            .WithMessage(new TestMessage { Id = "msg-1", Data = "Success" })
            .WithSuccessfulHandler();

        // Act
        var result = await scenario.ExecuteAsync();

        // Assert
        Assert.True(result.ShouldCommit);
        Assert.Equal(0, result.RetryCount);
        scenario.VerifyHandlerCalledOnce();
        scenario.VerifyNoRetryTopicCalls();
        scenario.VerifyNoDLQCalls();
    }

    [Fact]
    public async Task ConsumerFlow_FirstFailure_SendsToRetryTopic()
    {
        // Arrange
        var scenario = new ConsumerTestScenario()
            .WithMessage(new TestMessage { Id = "msg-1", Data = "Fail" })
            .WithFailingHandler()
            .WithRetryCount(0); // First attempt

        // Act
        var result = await scenario.ExecuteAsync();

        // Assert
        Assert.True(result.ShouldCommit); // Should commit original message
        scenario.VerifyRetryTopicCalled();
        scenario.VerifyNoDLQCalls();
    }

    [Fact]
    public async Task ConsumerFlow_SecondFailure_SendsToRetryTopicAgain()
    {
        // Arrange
        var scenario = new ConsumerTestScenario()
            .WithMessage(new TestMessage { Id = "msg-1", Data = "Fail" })
            .WithFailingHandler()
            .WithRetryCount(1); // Second attempt

        // Act
        var result = await scenario.ExecuteAsync();

        // Assert
        Assert.True(result.ShouldCommit);
        scenario.VerifyRetryTopicCalled();
        scenario.VerifyNoDLQCalls();
    }

    [Fact]
    public async Task ConsumerFlow_ThirdFailure_StillSendsToRetryTopic()
    {
        // Arrange
        var scenario = new ConsumerTestScenario()
            .WithMessage(new TestMessage { Id = "msg-1", Data = "Fail" })
            .WithFailingHandler()
            .WithRetryCount(2); // Third attempt

        // Act
        var result = await scenario.ExecuteAsync();

        // Assert
        Assert.True(result.ShouldCommit);
        scenario.VerifyRetryTopicCalled();
        scenario.VerifyNoDLQCalls();
    }

    [Fact]
    public async Task ConsumerFlow_MaxRetriesReached_SendsToDLQ()
    {
        // Arrange
        var scenario = new ConsumerTestScenario()
            .WithMessage(new TestMessage { Id = "msg-1", Data = "Poison" })
            .WithFailingHandler()
            .WithRetryCount(3); // Max retries = 3

        // Act
        var result = await scenario.ExecuteAsync();

        // Assert
        Assert.True(result.ShouldCommit); // Should commit after DLQ
        scenario.VerifyNoRetryTopicCalls();
        scenario.VerifyDLQCalled();
    }

    [Fact]
    public async Task ConsumerFlow_PoisonMessage_EventuallyLandsInDLQ()
    {
        // Simulate a poison message going through all retry attempts

        // Attempt 1 (retry count = 0)
        var scenario1 = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(0);
        var result1 = await scenario1.ExecuteAsync();
        Assert.True(result1.ShouldCommit);
        scenario1.VerifyRetryTopicCalled();

        // Attempt 2 (retry count = 1)
        var scenario2 = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(1);
        var result2 = await scenario2.ExecuteAsync();
        Assert.True(result2.ShouldCommit);
        scenario2.VerifyRetryTopicCalled();

        // Attempt 3 (retry count = 2)
        var scenario3 = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(2);
        var result3 = await scenario3.ExecuteAsync();
        Assert.True(result3.ShouldCommit);
        scenario3.VerifyRetryTopicCalled();

        // Attempt 4 (retry count = 3) - Goes to DLQ
        var scenario4 = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(3);
        var result4 = await scenario4.ExecuteAsync();
        Assert.True(result4.ShouldCommit);
        scenario4.VerifyDLQCalled();
        scenario4.VerifyNoRetryTopicCalls();
    }

    [Fact]
    public async Task ConsumerFlow_DLQDisabled_ThrowsException()
    {
        // Arrange
        var scenario = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithDLQDisabled()
            .WithRetryCount(0);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            async () => await scenario.ExecuteAsync());
    }

    [Fact]
    public async Task ConsumerFlow_SuccessAfterRetries_NoMoreRetries()
    {
        // Simulate a transient failure that succeeds on retry

        // First attempt fails
        var scenario1 = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(0);
        await scenario1.ExecuteAsync();
        scenario1.VerifyRetryTopicCalled();

        // Second attempt succeeds
        var scenario2 = new ConsumerTestScenario()
            .WithSuccessfulHandler()
            .WithRetryCount(1);
        var result = await scenario2.ExecuteAsync();

        Assert.True(result.ShouldCommit);
        scenario2.VerifyHandlerCalledOnce();
        scenario2.VerifyNoRetryTopicCalls();
        scenario2.VerifyNoDLQCalls();
    }

    [Fact]
    public void RetryDelay_IncreasesExponentially()
    {
        // Test the exponential backoff strategy
        var delays = new Dictionary<int, int>
        {
            { 0, 1000 },    // 1 second
            { 1, 2000 },    // 2 seconds
            { 2, 4000 },    // 4 seconds
            { 3, 8000 },    // 8 seconds
            { 4, 16000 },   // 16 seconds
            { 5, 30000 },   // 30 seconds (capped)
            { 10, 30000 },  // Still capped at 30 seconds
        };

        var consumer = new ConsumerTestScenario().GetConsumer();

        foreach (var kvp in delays)
        {
            var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", kvp.Key);
            Assert.Equal(kvp.Value, delay);
        }
    }

    [Fact]
    public async Task DLQMessage_ContainsAllMetadata()
    {
        // Arrange
        DeadLetterMessage? capturedDLQMessage = null;
        var scenario = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(3)
            .WithDLQMessageCapture(msg => capturedDLQMessage = msg);

        // Act
        await scenario.ExecuteAsync();

        // Assert
        Assert.NotNull(capturedDLQMessage);
        Assert.Equal("test-topic", capturedDLQMessage.OriginalTopic);
        Assert.Equal(3, capturedDLQMessage.RetryCount);
        Assert.Contains("Processing failed", capturedDLQMessage.Error);
        Assert.NotEqual(default, capturedDLQMessage.OriginalTimestamp);
    }

    [Fact]
    public async Task RetryMessage_ContainsIncrementedRetryCount()
    {
        // Arrange
        Message<string, string>? capturedRetryMessage = null;
        var scenario = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(1)
            .WithRetryMessageCapture(msg => capturedRetryMessage = msg);

        // Act
        await scenario.ExecuteAsync();

        // Assert
        Assert.NotNull(capturedRetryMessage);

        var retryHeader = capturedRetryMessage.Headers.FirstOrDefault(h => h.Key == "x-retry-count");
        Assert.NotNull(retryHeader);

        var retryCount = BitConverter.ToInt32(retryHeader.GetValueBytes(), 0);
        Assert.Equal(2, retryCount); // Should be incremented from 1 to 2
    }

    [Fact]
    public async Task RetryMessage_ContainsOriginalTopicHeader()
    {
        // Arrange
        Message<string, string>? capturedRetryMessage = null;
        var scenario = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(0)
            .WithRetryMessageCapture(msg => capturedRetryMessage = msg);

        // Act
        await scenario.ExecuteAsync();

        // Assert
        Assert.NotNull(capturedRetryMessage);

        var originalTopicHeader = capturedRetryMessage.Headers
            .FirstOrDefault(h => h.Key == "x-original-topic");
        Assert.NotNull(originalTopicHeader);

        var originalTopic = System.Text.Encoding.UTF8.GetString(originalTopicHeader.GetValueBytes());
        Assert.Equal("test-topic", originalTopic);
    }

    [Fact]
    public async Task RetryMessage_ContainsFailedAtTimestamp()
    {
        // Arrange
        Message<string, string>? capturedRetryMessage = null;
        var scenario = new ConsumerTestScenario()
            .WithFailingHandler()
            .WithRetryCount(0)
            .WithRetryMessageCapture(msg => capturedRetryMessage = msg);

        var beforeExecution = DateTime.UtcNow;

        // Act
        await scenario.ExecuteAsync();

        var afterExecution = DateTime.UtcNow;

        // Assert
        Assert.NotNull(capturedRetryMessage);

        var failedAtHeader = capturedRetryMessage.Headers
            .FirstOrDefault(h => h.Key == "x-failed-at");
        Assert.NotNull(failedAtHeader);

        var failedAtString = System.Text.Encoding.UTF8.GetString(failedAtHeader.GetValueBytes());
        var failedAt = DateTime.Parse(failedAtString);

        Assert.True(failedAt >= beforeExecution && failedAt <= afterExecution);
    }

    private T InvokePrivateMethod<T>(object obj, string methodName, params object[] parameters)
    {
        var method = obj.GetType().GetMethod(methodName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method == null)
            throw new InvalidOperationException($"Method {methodName} not found");

        return (T)method.Invoke(obj, parameters)!;
    }

    #region Test Helpers

    public class ConsumerTestScenario
    {
        private readonly Mock<IServiceProvider> _serviceProviderMock = new();
        private readonly Mock<IMessageSerializer> _serializerMock = new();
        private readonly Mock<ILogger<KafkaMessageConsumer>> _loggerMock = new();
        private readonly Mock<IMessageHandler<TestMessage>> _handlerMock = new();
        private readonly Mock<IProducer<string, string>> _kafkaProducerMock = new();
        private readonly Mock<IMessageProducer> _messageProducerMock = new();

        private readonly KafkaFlowOptions _options;
        private readonly TopicConfiguration _topicConfig;
        private TestMessage _message = new() { Id = "test-1", Data = "test" };
        private int _retryCount = 0;
        private Action<DeadLetterMessage>? _dlqMessageCapture;
        private Action<Message<string, string>>? _retryMessageCapture;

        public ConsumerTestScenario()
        {
            _options = new KafkaFlowOptions
            {
                BootstrapServers = "localhost:9092",
                DeadLetter = new DeadLetterOptions
                {
                    TopicName = "test-dlq",
                    Enabled = true,
                    MaxRetries = 3
                }
            };

            _topicConfig = new TopicConfiguration
            {
                TopicName = "test-topic",
                ConsumerGroup = "test-group",
                MessageType = typeof(TestMessage),
                HandlerType = typeof(TestMessageHandler)
            };

            SetupMocks();
        }

        public ConsumerTestScenario WithMessage(TestMessage message)
        {
            _message = message;
            _serializerMock.Setup(s => s.Deserialize(It.IsAny<string>(), typeof(TestMessage)))
                .Returns(message);
            return this;
        }

        public ConsumerTestScenario WithSuccessfulHandler()
        {
            _handlerMock.Setup(h => h.HandleAsync(
                    It.IsAny<TestMessage>(),
                    It.IsAny<MessageContext>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);
            return this;
        }

        public ConsumerTestScenario WithFailingHandler()
        {
            _handlerMock.Setup(h => h.HandleAsync(
                    It.IsAny<TestMessage>(),
                    It.IsAny<MessageContext>(),
                    It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Processing failed"));
            return this;
        }

        public ConsumerTestScenario WithRetryCount(int count)
        {
            _retryCount = count;
            return this;
        }

        public ConsumerTestScenario WithDLQDisabled()
        {
            _options.DeadLetter.Enabled = false;
            return this;
        }

        public ConsumerTestScenario WithDLQMessageCapture(Action<DeadLetterMessage> capture)
        {
            _dlqMessageCapture = capture;
            return this;
        }

        public ConsumerTestScenario WithRetryMessageCapture(Action<Message<string, string>> capture)
        {
            _retryMessageCapture = capture;
            return this;
        }

        public KafkaMessageConsumer GetConsumer()
        {
            return new KafkaMessageConsumer(
                _serviceProviderMock.Object,
                _options,
                _serializerMock.Object,
                _loggerMock.Object,
                "test-service");
        }

        public async Task<ProcessResult> ExecuteAsync()
        {
            var consumer = GetConsumer();
            var result = CreateConsumeResult();

            var shouldCommit = await InvokePrivateMethodAsync<bool>(
                consumer,
                "ProcessMessage",
                _topicConfig,
                result,
                CancellationToken.None);

            return new ProcessResult
            {
                ShouldCommit = shouldCommit,
                RetryCount = _retryCount
            };
        }

        public void VerifyHandlerCalledOnce()
        {
            _handlerMock.Verify(h => h.HandleAsync(
                It.IsAny<TestMessage>(),
                It.IsAny<MessageContext>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        public void VerifyRetryTopicCalled()
        {
            _kafkaProducerMock.Verify(p => p.ProduceAsync(
                It.Is<string>(t => t.Contains(".retry")),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        public void VerifyNoRetryTopicCalls()
        {
            _kafkaProducerMock.Verify(p => p.ProduceAsync(
                It.Is<string>(t => t.Contains(".retry")),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        public void VerifyDLQCalled()
        {
            _messageProducerMock.Verify(p => p.ProduceAsync(
                "test-dlq",
                It.IsAny<DeadLetterMessage>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        public void VerifyNoDLQCalls()
        {
            _messageProducerMock.Verify(p => p.ProduceAsync(
                It.IsAny<string>(),
                It.IsAny<DeadLetterMessage>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        private void SetupMocks()
        {
            // Setup handler
            var scopeMock = new Mock<IServiceScope>();
            var serviceScopeMock = new Mock<IServiceProvider>();

            serviceScopeMock.Setup(s => s.GetService(_topicConfig.HandlerType))
                .Returns(_handlerMock.Object);

            // Setup Kafka producer
            serviceScopeMock.Setup(s => s.GetKeyedService<IProducer<string, string>>(It.IsAny<string>()))
                .Returns(_kafkaProducerMock.Object);

            _kafkaProducerMock.Setup(p => p.ProduceAsync(
                    It.IsAny<string>(),
                    It.IsAny<Message<string, string>>(),
                    It.IsAny<CancellationToken>()))
                .Callback<string, Message<string, string>, CancellationToken>((_, msg, _) =>
                {
                    _retryMessageCapture?.Invoke(msg);
                })
                .ReturnsAsync((DeliveryResult<string, string>)null!);

            // Setup message producer for DLQ
            serviceScopeMock.Setup(s => s.GetKeyedService<IMessageProducer>(It.IsAny<string>()))
                .Returns(_messageProducerMock.Object);

            _messageProducerMock.Setup(p => p.ProduceAsync(
                    It.IsAny<string>(),
                    It.IsAny<DeadLetterMessage>(),
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Callback<string, DeadLetterMessage, string, CancellationToken>((_, msg, _, _) =>
                {
                    _dlqMessageCapture?.Invoke(msg);
                })
                .Returns(Task.CompletedTask);

            scopeMock.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);
            _serviceProviderMock.Setup(s => s.CreateScope()).Returns(scopeMock.Object);

            _serializerMock.Setup(s => s.Deserialize(It.IsAny<string>(), typeof(TestMessage)))
                .Returns(_message);
        }

        private ConsumeResult<string, string> CreateConsumeResult()
        {
            var headers = _retryCount > 0
                ? new Headers { { "x-retry-count", BitConverter.GetBytes(_retryCount) } }
                : null;

            var message = new Message<string, string>
            {
                Key = "test-key",
                Value = System.Text.Json.JsonSerializer.Serialize(_message),
                Timestamp = new Timestamp(DateTime.UtcNow),
                Headers = headers
            };

            return new ConsumeResult<string, string>
            {
                Message = message,
                Topic = _topicConfig.TopicName,
                Partition = new Partition(0),
                Offset = new Offset(100 + _retryCount),
                TopicPartitionOffset = new TopicPartitionOffset(_topicConfig.TopicName, 0, 100 + _retryCount)
            };
        }

        private async Task<T> InvokePrivateMethodAsync<T>(object obj, string methodName, params object[] parameters)
        {
            var method = obj.GetType().GetMethod(methodName,
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
                throw new InvalidOperationException($"Method {methodName} not found");

            var result = method.Invoke(obj, parameters);

            if (result is Task<T> taskResult)
                return await taskResult;

            return default(T)!;
        }
    }

    public class ProcessResult
    {
        public bool ShouldCommit { get; set; }
        public int RetryCount { get; set; }
    }

    public class TestMessage : BaseMessage
    {
        public string Data { get; set; } = string.Empty;
    }

    public class TestMessageHandler : IMessageHandler<TestMessage>
    {
        public Task HandleAsync(TestMessage message, MessageContext context, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
    }

    #endregion
}
