﻿using System.ComponentModel;
using Microsoft.OpenApi.Extensions;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for working with enums.
/// </summary>
public static class EnumExtensions
{
    /// <summary>
    /// Retrieve the description attribute of the specified enum value.
    /// </summary>
    /// <param name="enumValue">The enum value to get the description for.</param>
    /// <returns>The description of the enum value, or the enum value as a string if no description is found.</returns>
    public static string GetDescription(this Enum enumValue)
    {
        var attribute = enumValue.GetAttributeOfType<DescriptionAttribute>();
        return attribute == null ? enumValue.ToString() : attribute.Description;
    }

    public static List<EnumValueObject> ToValueObjectList<TEnum>() where TEnum : Enum
    {
        return Enum.GetValues(typeof(TEnum))
                   .Cast<TEnum>()
                   .Select(e => new EnumValueObject(Convert.ToInt32(e), e.GetDescription()))
                   .ToList();
    }
}

public record EnumValueObject
(
    int Code,
    string Name
);
