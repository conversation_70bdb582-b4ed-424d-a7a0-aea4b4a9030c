using Microsoft.EntityFrameworkCore;
using TMS.TruckService.Domain.Entities;
using TMS.TruckService.Domain.Interfaces;
using TMS.TruckService.Infra.Data;

namespace TMS.TruckService.Infra.Repositories;

public class TodoRepository : ITodoRepository
{
    private readonly ApplicationDbContext _context;

    public TodoRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Todo>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Todos
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Todo?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.Todos
            .FirstOrDefaultAsync(t => t.Id == id, cancellationToken);
    }

    public async Task<Todo> AddAsync(Todo todo, CancellationToken cancellationToken = default)
    {
        await _context.Todos.AddAsync(todo, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return todo;
    }

    public async Task UpdateAsync(Todo todo, CancellationToken cancellationToken = default)
    {
        _context.Todos.Update(todo);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var todo = await GetByIdAsync(id, cancellationToken);
        if (todo != null)
        {
            _context.Todos.Remove(todo);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }
}