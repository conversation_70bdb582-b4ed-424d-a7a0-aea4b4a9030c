﻿using System.Linq.Expressions;
using System.Threading;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Npgsql;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Entities.Interfaces;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
namespace TMS.SharedKernel.EntityFrameworkCore;

/// <summary>
/// Generic repository implementation for Entity Framework Core.
/// </summary>
/// <typeparam name="TEntity">The entity type.</typeparam>
public class BaseRepository<TEntity> : IBaseRepository<TEntity> where TEntity : class
{
    protected readonly BaseDbContext _context;
    protected readonly DbSet<TEntity> _dbSet;

    public BaseRepository(BaseDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _dbSet = context.Set<TEntity>();
    }

    public virtual IQueryable<TEntity> GetQueryable(bool asNoTracking = true)
    {
        return asNoTracking ? _dbSet.AsNoTracking() : _dbSet;
    }

    public virtual async Task<TEntity?> GetByIdAsync(object id, CancellationToken cancellationToken = default, bool asNoTracking = true)
    {
        ArgumentNullException.ThrowIfNull(id);

        if (asNoTracking)
        {
            var keyProperties = _context.Model.FindEntityType(typeof(TEntity))?.FindPrimaryKey()?.Properties;
            if (keyProperties == null || keyProperties.Count != 1)
                throw new InvalidOperationException($"Entity {typeof(TEntity).Name} must have a single primary key.");

            var keyProperty = keyProperties[0];
            var parameter = Expression.Parameter(typeof(TEntity), "e");
            var property = Expression.Property(parameter, keyProperty.PropertyInfo!);
            var constant = Expression.Constant(id);
            var equals = Expression.Equal(property, Expression.Convert(constant, property.Type));
            var lambda = Expression.Lambda<Func<TEntity, bool>>(equals, parameter);

            return await _dbSet.AsNoTracking().FirstOrDefaultAsync(lambda, cancellationToken);
        }

        return await _dbSet.FindAsync([id], cancellationToken);
    }

    /// <inheritdoc/>
    public virtual async Task<TEntity?> GetByIdAsync<TKey>(TKey id, CancellationToken cancellationToken = default, bool asNoTracking = true)
        where TKey : notnull
    {
        ArgumentNullException.ThrowIfNull(id);

        if (asNoTracking)
        {
            var keyProperties = _context.Model.FindEntityType(typeof(TEntity))?.FindPrimaryKey()?.Properties;
            if (keyProperties == null || keyProperties.Count != 1)
                throw new InvalidOperationException($"Entity {typeof(TEntity).Name} must have a single primary key.");

            var keyProperty = keyProperties[0];
            var parameter = Expression.Parameter(typeof(TEntity), "e");
            var property = Expression.Property(parameter, keyProperty.PropertyInfo!);
            var constant = Expression.Constant(id, typeof(TKey));
            var equals = Expression.Equal(property, Expression.Convert(constant, property.Type));
            var lambda = Expression.Lambda<Func<TEntity, bool>>(equals, parameter);

            return await _dbSet.AsNoTracking().FirstOrDefaultAsync(lambda, cancellationToken);
        }

        return await _dbSet.FindAsync([id], cancellationToken);
    }

    /// <inheritdoc/>
    public virtual async Task<IReadOnlyList<TEntity>> GetAllAsync(CancellationToken cancellationToken = default, bool asNoTracking = true)
    {
        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;
        return await query.ToListAsync(cancellationToken);
    }

    /// <summary>
    /// var ids = await userRepository.GetIdsAsync(x => x.UserId);
    /// var orderIds = await orderRepository.GetIdsAsync(x => x.OrderNumber);
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <param name="keySelector"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<List<TKey>> GetIdsAsync<TKey>(
        Expression<Func<TEntity, bool>> predicate,
        Expression<Func<TEntity, TKey>> keySelector,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true)
    {
        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;
        return await query
            .Where(predicate)
            .Select(keySelector)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// var results = await repo.GetColumnsAsync(x => new { x.UserId, x.UserName });
    /// or with tuple
    /// var results = await repo.GetColumnsAsync(x => new ValueTuple<int, string>(x.UserId, x.UserName));
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="selector"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<List<TResult>> GetColumnsAsync<TResult>(
        Expression<Func<TEntity, bool>> predicate,
        Expression<Func<TEntity, TResult>> selector,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true)
    {
        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;
        return await query
            .Where(predicate)
            .Select(selector)
            .ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<TEntity>> FindAsync(
        Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true)
    {
        ArgumentNullException.ThrowIfNull(predicate);
        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;
        return await query.Where(predicate).ToListAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<TEntity>> FindWithIncludeAsync(
        Expression<Func<TEntity, bool>> predicate,
        Func<IQueryable<TEntity>, IQueryable<TEntity>>? includeFunc = null,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true)
    {
        ArgumentNullException.ThrowIfNull(predicate);

        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;

        if (includeFunc is not null)
            query = includeFunc(query);

        if (predicate is not null)
            query = query.Where(predicate);

        return await query.ToListAsync(cancellationToken);
    }

    public virtual async Task<TEntity?> FirstOrDefaultAsync(
        Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true)
    {
        ArgumentNullException.ThrowIfNull(predicate);
        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;
        return await query.FirstOrDefaultAsync(predicate, cancellationToken);
    }

    public virtual async Task<bool> ExistsAsync(
        Expression<Func<TEntity, bool>> predicate, 
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(predicate);
        return await _dbSet.AnyAsync(predicate, cancellationToken);
    }

    public virtual async Task<int> CountAsync(
        Expression<Func<TEntity, bool>>? predicate = null, 
        CancellationToken cancellationToken = default)
    {
        return predicate is null 
            ? await _dbSet.CountAsync(cancellationToken)
            : await _dbSet.CountAsync(predicate, cancellationToken);
    }

    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(
        int page,
        int pageSize,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        CancellationToken cancellationToken = default)
    {
        if (page < 1) page = 1;
        if (pageSize > 100 || pageSize < 10) pageSize = 10;

        var query = _dbSet.AsNoTracking();

        if (predicate is not null)
            query = query.Where(predicate);

        var totalCount = await query.CountAsync(cancellationToken);

        if (sortOptions is not null)
        {
            query = query.ApplySorting(sortOptions);
        }

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<TEntity>(items, totalCount, page, pageSize);
    }

    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(
        int page,
        int pageSize,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        Func<IQueryable<TEntity>, IQueryable<TEntity>>? includeFunc = null,
        CancellationToken cancellationToken = default)
    {
        if (page < 1) page = 1;
        if (pageSize > 100 || pageSize < 10) pageSize = 10;

        var query = _dbSet.AsNoTracking();

        if (includeFunc is not null)
            query = includeFunc(query);

        if (predicate is not null)
            query = query.Where(predicate);

        var totalCount = await query.CountAsync(cancellationToken);

        if (sortOptions is not null)
        {
            query = query.ApplySorting(sortOptions);
        }

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return new PagedResult<TEntity>(items, totalCount, page, pageSize);
    }

    public virtual async Task<PagedResult<TResult>> GetPagedAsync<TResult>(
        int page,
        int pageSize,
        Expression<Func<TEntity, TResult>> selector,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        CancellationToken cancellationToken = default)
    {
        if (page < 1) page = 1;
        if (pageSize > 100 || pageSize < 10) pageSize = 10;

        var query = _dbSet.AsNoTracking();

        if (predicate is not null)
            query = query.Where(predicate);

        var totalCount = await query.CountAsync(cancellationToken);

        if (sortOptions is not null)
        {
            query = query.ApplySorting(sortOptions);
        }

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(selector)
            .ToListAsync(cancellationToken);

        return new PagedResult<TResult>(items, totalCount, page, pageSize);
    }

    public virtual async Task<PagedResult<TResult>> GetPagedAsync<TResult>(
        int page,
        int pageSize,
        Expression<Func<TEntity, TResult>> selector,
        Expression<Func<TEntity, bool>>? predicate = null,
        IEnumerable<ISortOption<TEntity>>? sortOptions = null,
        Func<IQueryable<TEntity>, IQueryable<TEntity>>? includeFunc = null,
        CancellationToken cancellationToken = default)
    {
        if (page < 1) page = 1;
        if (pageSize > 100 || pageSize < 10) pageSize = 10;

        var query = _dbSet.AsNoTracking();

        if (includeFunc is not null)
            query = includeFunc(query);

        if (predicate is not null)
            query = query.Where(predicate);

        var totalCount = await query.CountAsync(cancellationToken);

        if (sortOptions is not null)
        {
            query = query.ApplySorting(sortOptions);
        }

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(selector)
            .ToListAsync(cancellationToken);

        return new PagedResult<TResult>(items, totalCount, page, pageSize);
    }

    public virtual async Task<IReadOnlyList<TEntity>> FindWithIncludeAsync(
        Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default,
        bool asNoTracking = true,
        params Expression<Func<TEntity, object>>[] includes)
    {
        ArgumentNullException.ThrowIfNull(predicate);

        var query = asNoTracking ? _dbSet.AsNoTracking() : _dbSet;

        foreach (var include in includes)
        {
            query = query.Include(include);
        }

        return await query.Where(predicate).ToListAsync(cancellationToken);
    }

    public virtual async Task AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entity);
        await _dbSet.AddAsync(entity, cancellationToken);
    }

    public virtual async Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities);
        await _dbSet.AddRangeAsync(entities, cancellationToken);
    }

    public virtual void Update(TEntity entity)
    {
        ArgumentNullException.ThrowIfNull(entity);
        _dbSet.Update(entity);
    }

    public virtual void UpdateRange(IEnumerable<TEntity> entities)
    {
        ArgumentNullException.ThrowIfNull(entities);
        _dbSet.UpdateRange(entities);
    }

    public virtual void Remove(TEntity entity)
    {
        ArgumentNullException.ThrowIfNull(entity);
        _dbSet.Remove(entity);
    }

    public virtual void RemoveRange(IEnumerable<TEntity> entities)
    {
        ArgumentNullException.ThrowIfNull(entities);
        _dbSet.RemoveRange(entities);
    }

    public virtual async Task<bool> RemoveByIdAsync(object id, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(id);

        var entity = await GetByIdAsync(id, cancellationToken, asNoTracking: false);
        if (entity is null)
            return false;

        Remove(entity);
        return true;
    }

    /// <summary>
    /// Execute bulk update using ExecuteUpdateAsync
    /// </summary>
    public virtual async Task<int> ExecuteUpdateAsync(
        Expression<Func<TEntity, bool>> predicate,
        Expression<Func<Microsoft.EntityFrameworkCore.Query.SetPropertyCalls<TEntity>, Microsoft.EntityFrameworkCore.Query.SetPropertyCalls<TEntity>>> setterExpression,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(predicate);
        ArgumentNullException.ThrowIfNull(setterExpression);

        return await _dbSet
            .Where(predicate)
            .ExecuteUpdateAsync(setterExpression, cancellationToken);
    }

    /// <summary>
    /// Upserts a single entity using the entity's configured primary key
    /// </summary>
    public virtual async Task<int> UpsertAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entity);

        var entityType = _context.Model.FindEntityType(typeof(TEntity));

        if (entityType is null)
            throw new InvalidOperationException($"Entity type {typeof(TEntity).Name} not found in DbContext model.");

        var tableName = entityType.GetTableName();
        var schema = entityType.GetSchema() ?? "public";

        var properties = entityType.GetProperties()
            .Where(p => !p.ValueGenerated.HasFlag(ValueGenerated.OnAdd))
            .ToList();

        var keyProperties = entityType.FindPrimaryKey()?.Properties
            ?? throw new InvalidOperationException($"Entity {typeof(TEntity).Name} does not have a primary key configured.");

        var columnNames = string.Join(", ", properties.Select(p => $"\"{p.GetColumnName()}\""));
        var valueParams = string.Join(", ", properties.Select((p, i) => $"@p{i}"));
        var updateSet = string.Join(", ",
            properties.Where(p => !keyProperties.Contains(p))
                .Select(p => $"\"{p.GetColumnName()}\" = EXCLUDED.\"{p.GetColumnName()}\""));

        var conflictColumns = string.Join(", ", keyProperties.Select(p => $"\"{p.GetColumnName()}\""));

        var sql = $@"
            INSERT INTO {schema}.""{tableName}"" ({columnNames})
            VALUES ({valueParams})
            ON CONFLICT ({conflictColumns})
            DO UPDATE SET {updateSet}";

        var parameters = properties.Select((p, i) =>
        {
            var value = p.PropertyInfo?.GetValue(entity);

            // Convert enum to underlying type (int) for Npgsql compatibility
            if (value != null && value.GetType().IsEnum)
            {
                value = Convert.ChangeType(value, Enum.GetUnderlyingType(value.GetType()));
            }

            var param = new NpgsqlParameter($"@p{i}", value ?? DBNull.Value);

            // Set NpgsqlDbType for jsonb columns
            var columnType = p.GetColumnType();
            if (columnType?.ToLower() == "jsonb")
            {
                param.NpgsqlDbType = NpgsqlTypes.NpgsqlDbType.Jsonb;
            }

            return param;
        }).ToArray();

        return await _context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
    }

    /// <summary>
    /// Upserts a single entity using custom key columns specified by user
    /// Usage Examples
    ///
    ///  // Example 1: Update only specific columns on conflict
    ///  await repository.UpsertAsync(
    ///      entity,
    ///      keyColumns: new Expression<Func<MyEntity, object>>[] { x => x.Id },
    ///      updateColumns: new Expression<Func<MyEntity, object>>[] {
    ///          x => x.Name,
    ///          x => x.UpdatedAt
    ///      }
    ///  );
    ///
    ///  // Example 2: Default behavior - update all non-key columns
    ///  await repository.UpsertAsync(
    ///      entity,
    ///      keyColumns: new Expression<Func<MyEntity, object>>[] { x => x.Id }
    ///  );
    /// </summary>
    /// <param name="entity">The entity to upsert</param>
    /// <param name="keyColumns">The columns to use for conflict detection</param>
    /// <param name="updateColumns">The columns to update on conflict. If null, all non-key columns will be updated.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public virtual async Task<int> UpsertAsync(
        TEntity entity,
        Expression<Func<TEntity, object>>[] keyColumns,
        Expression<Func<TEntity, object>>[]? updateColumns = null,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entity);
        ArgumentNullException.ThrowIfNull(keyColumns);

        if (keyColumns.Length == 0)
            throw new ArgumentException("At least one key column must be specified.", nameof(keyColumns));

        var entityType = _context.Model.FindEntityType(typeof(TEntity));

        if (entityType is null)
            throw new InvalidOperationException($"Entity type {typeof(TEntity).Name} not found in DbContext model.");

        var tableName = entityType.GetTableName();
        var schema = entityType.GetSchema() ?? "public";

        // Get ALL properties for validation (don't filter yet)
        var allProperties = entityType.GetProperties().ToList();

        // Get key property names from expressions
        List<string> keyPropertyNames;
        try
        {
            keyPropertyNames = keyColumns.Select(GetPropertyName).ToList();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Failed to extract property names from key column expressions for entity {typeof(TEntity).Name}. " +
                $"Error: {ex.Message}",
                ex);
        }

        // Validate key columns against ALL properties (before filtering)
        var keyProperties = allProperties.Where(p => keyPropertyNames.Contains(p.Name)).ToList();

        if (keyProperties.Count != keyColumns.Length)
        {
            var foundProperties = string.Join(", ", keyProperties.Select(p => p.Name));
            var requestedProperties = string.Join(", ", keyPropertyNames);
            var missingProperties = keyPropertyNames.Except(keyProperties.Select(p => p.Name)).ToList();
            var availableProperties = string.Join(", ", allProperties.Select(p => p.Name));

            throw new InvalidOperationException(
                $"One or more specified key columns were not found in the entity configuration.\n" +
                $"Entity: {typeof(TEntity).Name}\n" +
                $"Requested keys: [{requestedProperties}]\n" +
                $"Found in EF config: [{foundProperties}]\n" +
                $"Missing: [{string.Join(", ", missingProperties)}]\n" +
                $"Available properties in EF config: [{availableProperties}]\n" +
                $"Hint: Check if missing properties are marked with [NotMapped] or Ignore() in EF configuration.");
        }

        // Filter properties for INSERT/UPDATE (exclude auto-generated)
        var insertableProperties = allProperties
            .Where(p => !p.ValueGenerated.HasFlag(ValueGenerated.OnAdd))
            .ToList();

        // Determine which properties to update
        List<IProperty> propertiesToUpdate;
        if (updateColumns is not null && updateColumns.Length > 0)
        {
            var updatePropertyNames = updateColumns.Select(GetPropertyName).ToHashSet();
            propertiesToUpdate = insertableProperties.Where(p => updatePropertyNames.Contains(p.Name)).ToList();

            if (propertiesToUpdate.Count != updateColumns.Length)
                throw new InvalidOperationException("One or more specified update columns were not found in the entity configuration.");
        }
        else
        {
            // Default: update all non-key columns
            propertiesToUpdate = insertableProperties.Where(p => !keyProperties.Contains(p)).ToList();
        }

        if (propertiesToUpdate.Count == 0)
            throw new InvalidOperationException("At least one column must be specified for update.");

        var columnNames = string.Join(", ", insertableProperties.Select(p => $"\"{p.GetColumnName()}\""));
        var valueParams = string.Join(", ", insertableProperties.Select((p, i) => $"@p{i}"));
        var updateSet = string.Join(", ",
            propertiesToUpdate.Select(p => $"\"{p.GetColumnName()}\" = EXCLUDED.\"{p.GetColumnName()}\""));

        var conflictColumns = string.Join(", ", keyProperties.Select(p => $"\"{p.GetColumnName()}\""));

        var sql = $@"
            INSERT INTO {schema}.""{tableName}"" ({columnNames})
            VALUES ({valueParams})
            ON CONFLICT ({conflictColumns})
            DO UPDATE SET {updateSet}";

        var parameters = insertableProperties.Select((p, i) =>
        {
            var value = p.PropertyInfo?.GetValue(entity);

            // Convert enum to underlying type (int) for Npgsql compatibility
            if (value != null && value.GetType().IsEnum)
            {
                value = Convert.ChangeType(value, Enum.GetUnderlyingType(value.GetType()));
            }

            var param = new NpgsqlParameter($"@p{i}", value ?? DBNull.Value);

            // Set NpgsqlDbType for jsonb columns
            var columnType = p.GetColumnType();
            if (columnType?.ToLower() == "jsonb")
            {
                param.NpgsqlDbType = NpgsqlTypes.NpgsqlDbType.Jsonb;
            }

            return param;
        }).ToArray();

        return await _context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
    }

    /// <summary>
    /// Upserts multiple entities using the entity's configured primary key
    /// </summary>
    public virtual async Task<int> UpsertRangeAsync(
        IEnumerable<TEntity> entities,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities);

        var entityList = entities.ToList();
        if (entityList.Count == 0)
            return 0;

        var totalAffected = 0;
        foreach (var entity in entityList)
        {
            totalAffected += await UpsertAsync(entity, cancellationToken);
        }

        return totalAffected;
    }

    /// <summary>
    /// Upserts multiple entities using custom key columns specified by user
    /// </summary>
    /// <param name="entities">The entities to upsert</param>
    /// <param name="keyColumns">The columns to use for conflict detection</param>
    /// <param name="updateColumns">The columns to update on conflict. If null, all non-key columns will be updated.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public virtual async Task<int> UpsertRangeAsync(
        IEnumerable<TEntity> entities,
        Expression<Func<TEntity, object>>[] keyColumns,
        Expression<Func<TEntity, object>>[]? updateColumns = null,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(entities);
        ArgumentNullException.ThrowIfNull(keyColumns);

        var entityList = entities.ToList();
        if (entityList.Count == 0)
            return 0;

        var totalAffected = 0;
        foreach (var entity in entityList)
        {
            totalAffected += await UpsertAsync(entity, keyColumns, updateColumns, cancellationToken);
        }

        return totalAffected;
    }

    /// <summary>
    /// Helper method to extract property name from expression
    /// Handles both Debug and Release mode expression trees
    /// Release mode may add additional Convert/ConvertChecked layers due to compiler optimizations
    /// Special handling for enums which create complex nested conversions
    /// </summary>
    private static string GetPropertyName<TProperty>(Expression<Func<TEntity, TProperty>> expression)
    {
        // Unwrap all outer layers (Convert, ConvertChecked, Quote, etc.)
        Expression? body = expression.Body;

        // Keep unwrapping until we find the actual member
        // This handles:
        // - Simple properties: e => e.Name
        // - Boxed value types: e => (object)e.Id
        // - Enum conversions: e => (object)e.Status (multiple conversion layers)
        // - Release mode optimizations (nested conversions)
        const int maxDepth = 10; // Safety limit to prevent infinite loops
        int depth = 0;

        while (body != null && depth < maxDepth)
        {
            switch (body)
            {
                // Direct property access: e => e.PropertyName
                case MemberExpression memberExpression:
                    return memberExpression.Member.Name;

                // Boxed/converted property: e => (object)e.PropertyName
                // Enum conversions: e => (object)e.EnumProperty (creates multiple layers!)
                // Release mode may add nested conversions: Convert(Convert(Convert(property)))
                case UnaryExpression unaryExpression:
                    body = unaryExpression.Operand;
                    depth++;
                    continue;

                // Should not happen with property expressions, but be defensive
                default:
                    throw new ArgumentException(
                        $"Expression '{expression}' does not refer to a property. " +
                        $"Body type: {body.GetType().Name}. " +
                        $"Expression structure: {expression}. " +
                        $"Unwrapped {depth} layers before failing. " +
                        $"This may indicate the expression is not a simple property accessor.",
                        nameof(expression));
            }
        }

        // Safety check - we should never hit this
        if (depth >= maxDepth)
        {
            throw new ArgumentException(
                $"Expression '{expression}' has too many nested conversion layers (>{maxDepth}). " +
                $"This likely indicates a complex expression that is not a simple property accessor.",
                nameof(expression));
        }

        throw new ArgumentException(
            $"Expression '{expression}' does not refer to a property. Body is null.",
            nameof(expression));
    }
}
