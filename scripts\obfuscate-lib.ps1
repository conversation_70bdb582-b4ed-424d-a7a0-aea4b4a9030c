# Obfuscate a single library DLL for NuGet packaging
# Usage: .\scripts\obfuscate-lib.ps1 -ProjectPath "TMS.SharedKernal.Caching" -ProjectName "TMS.SharedKernal.Caching"

param(
    [Parameter(Mandatory=$true)]
    [string]$ProjectPath,

    [Parameter(Mandatory=$true)]
    [string]$ProjectName,

    [string]$Configuration = "Release",
    [string]$TargetFramework = "net9.0",
    [string]$ConfuserExPath = "..\tools\ConfuserEx\Confuser.CLI.exe",
    [string]$ConfigFile = ".\confuserex-lib.crproj"
)

Write-Host "=== Library Obfuscation for NuGet Package ===" -ForegroundColor Cyan
Write-Host "Project: $ProjectName" -ForegroundColor White
Write-Host ""

# Check if ConfuserEx is installed
if (-not (Test-Path $ConfuserExPath)) {
    Write-Host "ConfuserEx not found at: $ConfuserExPath" -ForegroundColor Red
    Write-Host "Please install ConfuserEx:" -ForegroundColor Yellow
    Write-Host "1. Download from: https://github.com/mkaring/ConfuserEx/releases" -ForegroundColor Yellow
    Write-Host "2. Extract to: ..\tools\ConfuserEx\" -ForegroundColor Yellow
    Write-Host "3. Or install via: dotnet tool install -g ConfuserEx2.CLI" -ForegroundColor Yellow
    exit 1
}

# Step 1: Clean the project
Write-Host "`n[1/5] Cleaning project..." -ForegroundColor Green
$csprojPath = "$ProjectPath\$ProjectName.csproj"
dotnet clean $csprojPath -c $Configuration
if ($LASTEXITCODE -ne 0) {
    Write-Host "Clean failed!" -ForegroundColor Red
    exit 1
}

# Step 2: Build the project in Release mode
Write-Host "`n[2/5] Building project in $Configuration mode..." -ForegroundColor Green
dotnet build $csprojPath -c $Configuration
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

# Step 2b: Publish to get all dependencies in one place
Write-Host "`n[2b/5] Publishing to gather dependencies..." -ForegroundColor Green
$publishPath = "$ProjectPath\bin\Publish"
dotnet publish $csprojPath -c $Configuration -o $publishPath --no-build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Publish failed!" -ForegroundColor Red
    exit 1
}

# Step 3: Prepare paths
$dllPath = "$ProjectPath\bin\$Configuration\$TargetFramework\$ProjectName.dll"
$pdbPath = "$ProjectPath\bin\$Configuration\$TargetFramework\$ProjectName.pdb"
$xmlPath = "$ProjectPath\bin\$Configuration\$TargetFramework\$ProjectName.xml"
$binDir = "$ProjectPath\bin\$Configuration\$TargetFramework"

if (-not (Test-Path $dllPath)) {
    Write-Host "DLL not found at: $dllPath" -ForegroundColor Red
    exit 1
}

Write-Host "DLL to obfuscate: $dllPath" -ForegroundColor Cyan

# Step 3b: Copy all dependencies from publish folder to bin folder
Write-Host "`n[3b/6] Copying dependencies for obfuscation..." -ForegroundColor Green
Get-ChildItem -Path $publishPath -Filter "*.dll" | ForEach-Object {
    $destPath = Join-Path $binDir $_.Name
    if (-not (Test-Path $destPath)) {
        Copy-Item $_.FullName $destPath -Force
        Write-Host "  Copied: $($_.Name)" -ForegroundColor Gray
    }
}

# Step 4: Create temporary config with the specific module
Write-Host "`n[4/7] Creating temporary ConfuserEx config..." -ForegroundColor Green
$tempConfig = ".\confuserex-lib-temp.crproj"
$configContent = Get-Content $ConfigFile -Raw

# Get probe paths for dependency resolution
$binPathWin = "$ProjectPath\bin\$Configuration\$TargetFramework"
$publishPathWin = "$publishPath"
$nugetPathWin = "$env:USERPROFILE\.nuget\packages"

# Unix-style paths for XML
$binPath = $binPathWin -replace '\\', '/'
$publishPathNormalized = $publishPathWin -replace '\\', '/'
$nugetPath = $nugetPathWin -replace '\\', '/'
$modulePath = $dllPath -replace '\\', '/'

# Create a simple config like confuserex-lite.crproj (baseDir is REQUIRED)
$newConfig = @"
<project baseDir="." outputDir="Confused" xmlns="http://confuser.codeplex.com">
  <rule pattern="true" inherit="false">
    <protection id="ctrl flow">
      <argument name="intensity" value="20" />
    </protection>
  </rule>

  <module path="$modulePath" />
</project>
"@

Set-Content -Path $tempConfig -Value $newConfig

Write-Host "  Probe paths configured:" -ForegroundColor Yellow
Write-Host "    - $binPath" -ForegroundColor Gray
Write-Host "    - $publishPathNormalized" -ForegroundColor Gray
Write-Host "    - $nugetPath" -ForegroundColor Gray
Write-Host "  Temporary config saved: $tempConfig" -ForegroundColor Gray

# Step 5: Run ConfuserEx using the project configuration file
Write-Host "`n[5/7] Running ConfuserEx obfuscation..." -ForegroundColor Green
Write-Host "  Config: $tempConfig" -ForegroundColor Gray

# Run with project file (all dependencies are now in the same folder as the DLL)
& $ConfuserExPath -n $tempConfig
if ($LASTEXITCODE -ne 0) {
    Write-Host "Obfuscation failed!" -ForegroundColor Red
    if ($env:DEBUG_OBFUSCATION -ne "1") {
        Remove-Item $tempConfig -ErrorAction SilentlyContinue
    } else {
        Write-Host "  DEBUG: Temp config kept at: $tempConfig" -ForegroundColor Yellow
    }
    exit 1
}

# Step 6: Replace original DLL with obfuscated version
Write-Host "`n[6/7] Replacing original DLL with obfuscated version..." -ForegroundColor Green
$obfuscatedDll = ".\Confused\$ProjectName.dll"

if (-not (Test-Path $obfuscatedDll)) {
    Write-Host "Obfuscated DLL not found at: $obfuscatedDll" -ForegroundColor Red
    Remove-Item $tempConfig -ErrorAction SilentlyContinue
    exit 1
}

# Backup original DLL
$backupPath = "$ProjectPath\bin\$Configuration\$TargetFramework\$ProjectName.dll.original"
Copy-Item $dllPath $backupPath -Force
Write-Host "Original DLL backed up to: $backupPath" -ForegroundColor Yellow

# Replace with obfuscated DLL
Copy-Item $obfuscatedDll $dllPath -Force
Write-Host "Obfuscated DLL copied to: $dllPath" -ForegroundColor Green

# Step 7: Cleanup
Write-Host "`n[7/7] Cleaning up temporary files..." -ForegroundColor Green
if ($env:DEBUG_OBFUSCATION -ne "1") {
    Remove-Item $tempConfig -ErrorAction SilentlyContinue
} else {
    Write-Host "  DEBUG: Keeping temp config: $tempConfig" -ForegroundColor Yellow
}
Remove-Item ".\Confused" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item $publishPath -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "`n=== Obfuscation Complete ===" -ForegroundColor Cyan
Write-Host "The obfuscated DLL is ready for NuGet packaging" -ForegroundColor Green
Write-Host "Original DLL backed up with .original extension" -ForegroundColor Yellow
