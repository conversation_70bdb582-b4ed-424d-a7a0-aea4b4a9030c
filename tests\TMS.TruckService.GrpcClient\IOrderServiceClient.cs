using Grpc.Core;
using TMS.TruckService.Api.Grpc;

namespace TMS.TruckService.GrpcClient;

/// <summary>
/// Interface for OrderService gRPC client operations
/// </summary>
public interface IOrderServiceClient
{
    /// <summary>
    /// Get an order by ID
    /// </summary>
    Task<GetOrderReply> GetOrderAsync(int orderId, CallOptions? options = null);

    /// <summary>
    /// Create a new order
    /// </summary>
    Task<CreateOrderReply> CreateOrderAsync(
        string customerName,
        double amount,
        string? description = null,
        CallOptions? options = null);

    /// <summary>
    /// List orders with pagination
    /// </summary>
    Task<ListOrdersReply> ListOrdersAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? status = null,
        CallOptions? options = null);

    /// <summary>
    /// Update order status
    /// </summary>
    Task<UpdateOrderStatusReply> UpdateOrderStatusAsync(
        int orderId,
        string newStatus,
        string? notes = null,
        CallOptions? options = null);

    /// <summary>
    /// Delete an order
    /// </summary>
    Task<DeleteOrderReply> DeleteOrderAsync(int orderId, CallOptions? options = null);

    /// <summary>
    /// Test server connectivity
    /// </summary>
    Task<bool> TestConnectionAsync();
}
