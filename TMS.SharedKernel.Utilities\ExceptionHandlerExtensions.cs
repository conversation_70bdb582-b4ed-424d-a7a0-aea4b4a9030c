﻿using System.Diagnostics.CodeAnalysis;
using TMS.SharedKernel.Utilities.Filters;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for configuring exception handling in a WebApplicationBuilder.
/// </summary>
[ExcludeFromCodeCoverage]
public static class ExceptionHandlerExtensions
{
    /// <summary>
    /// Add exception handler to the specified WebApplicationBuilder.
    /// </summary>
    /// <param name="builder">The WebApplicationBuilder to add the services to.</param>
    /// <returns>The updated WebApplicationBuilder.</returns>
    public static IServiceCollection AddExceptionHandler(this IServiceCollection builder)
    {
        builder.AddExceptionHandler<GlobalExceptionHandler>();
        builder.AddProblemDetails();

        return builder;
    }
}
