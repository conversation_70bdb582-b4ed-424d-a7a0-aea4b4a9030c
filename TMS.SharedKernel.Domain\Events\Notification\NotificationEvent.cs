﻿using TMS.SharedKernal.RabbitMq.Events;

namespace TMS.SharedKernel.Domain.Events.Notification;

/// <summary>
/// NotificationEvent
/// </summary>
public class NotificationEvent : BaseEvent
{
    public override string EventType => "NotificationCreated";

    public string Title { get; set; }

    public string Message { get; set; }

    public string Data { get; set; }

    public NotificationType Type { get; set; }

    public List<DeliveryChannel>? Channels { get; set; } = null;

    public Guid UserId { get; set; }
}
