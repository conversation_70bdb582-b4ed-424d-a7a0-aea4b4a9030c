﻿using System.ComponentModel.DataAnnotations;
using TMS.SharedKernel.Domain.Entities.Interfaces;

namespace TMS.SharedKernel.Domain.Entities;

/// <summary>
/// Represents an entity that supports concurrency control using
/// database-generated tokens.
/// </summary>
public abstract class ConcurrencyControlledEntity : EntityBase, IConcurrencyControlled
{
    /// <inheritdoc />
    [Timestamp] public byte[] Version { get; set; } = [];
}

/// <summary>
/// Represents a concurrency-controlled entity with a customizable primary key.
/// </summary>
public abstract class ConcurrencyControlledEntity<TKey> : EntityBase<TKey>, IConcurrencyControlled
    where TK<PERSON> : IComparable, IComparable<TKey>, IEquatable<TKey>
{
    /// <inheritdoc />
    [Timestamp] public byte[] Version { get; set; } = [];
}
