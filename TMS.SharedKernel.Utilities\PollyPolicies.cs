﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Polly;
using Polly.Extensions.Http;
using Serilog;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// PollyPolicies
/// </summary>
public static class PollyPolicies
{
    /// <summary>
    /// Configurable circuit breaker policy
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetAdvancedCircuitBreakerPolicy(
        int failureThreshold = 5,
        int samplingDuration = 10,
        int minimumThroughput = 3,
        TimeSpan? durationOfBreak = null,
        ILogger? logger = null)
    {
        var breakDuration = durationOfBreak ?? TimeSpan.FromSeconds(30);

        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
            .AdvancedCircuitBreakerAsync(
                failureThreshold: 0.5, // Break if 50% of requests fail
                samplingDuration: TimeSpan.FromSeconds(samplingDuration),
                minimumThroughput: minimumThroughput, // At least 3 requests in sampling window
                durationOfBreak: breakDuration,
                onBreak: (outcome, duration, context) =>
                {
                    logger?.Error(
                        "CIRCUIT BREAKER OPENED for {Duration}s. Failure threshold exceeded. Reason: {Reason}",
                        duration.TotalSeconds,
                        outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString());
                },
                onReset: (context) =>
                {
                    logger?.Information("CIRCUIT BREAKER RESET - Service recovered");
                },
                onHalfOpen: () =>
                {
                    logger?.Warning("CIRCUIT BREAKER HALF-OPEN - Testing service health");
                });
    }

    /// <summary>
    /// Timeout policy to prevent hanging requests
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetTimeoutPolicy(int timeoutSeconds = 30)
    {
        return Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(timeoutSeconds));
    }

    /// <summary>
    /// Complete resilience policy with timeout, retry, and circuit breaker
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetResiliencePolicy(ILogger? logger = null)
    {
        var timeout = GetTimeoutPolicy(30);
        var retry = GetRetryPolicyWithLogging(logger);
        var circuitBreaker = GetCircuitBreakerPolicyWithLogging(logger);

        // Order matters: Timeout -> Retry -> Circuit Breaker
        return Policy.WrapAsync(timeout, retry, circuitBreaker);
    }

    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicyWithLogging(ILogger? logger)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    logger?.Warning(
                        "Retrying request (attempt {RetryCount}) after {Delay}s",
                        retryCount,
                        timespan.TotalSeconds);
                });
    }

    private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicyWithLogging(ILogger? logger)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromSeconds(30),
                onBreak: (outcome, duration) =>
                {
                    logger?.Error("Circuit breaker opened for {Duration}s", duration.TotalSeconds);
                },
                onReset: () =>
                {
                    logger?.Information("Circuit breaker reset");
                },
                onHalfOpen: () =>
                {
                    logger?.Warning("Circuit breaker testing connection");
                });
    }
}
