﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.SmoothRedis;
using TMS.TruckService.Domain.EventsKafka;

namespace TMS.TruckService.Application.Features.OrdersKafka;

public class PaymentProcessedEventKafkaHandler : IMessageHandler<PaymentProcessedEventKafka>
{
    private readonly ILogger<PaymentProcessedEventKafkaHandler> _logger;
    private readonly ISmoothRedis _redis;

    public PaymentProcessedEventKafkaHandler(ISmoothRedis redis, ILogger<PaymentProcessedEventKafkaHandler> logger)
    {
        _logger = logger;
        _redis = redis;
    }

    public async Task HandleAsync(PaymentProcessedEventKafka message, MessageContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation($"PaymentProcessedEventKafkaHandler Processing order {message.OrderId} for customer {message.CustomerId}");
        var req = await _redis.Cache.GetAsync("pairing:truckId:driverId");
        _logger.LogInformation($"Retrieved pairing from Redis: {req}");

        // Process payment, reserve inventory, etc.
        await Task.Delay(200, cancellationToken);

        _logger.LogInformation($"PaymentProcessedEventKafkaHandler Order {message.OrderId} processing completed");
    }
}
