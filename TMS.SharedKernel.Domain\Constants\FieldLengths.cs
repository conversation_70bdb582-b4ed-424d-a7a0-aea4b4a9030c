﻿namespace TMS.SharedKernel.Domain.Constants;

/// <summary>
/// This constant class defines default values for Entity field lengths to award some level of consistency across all TMS apps. 
/// </summary>
public static class FieldLengths
{
    /// <summary>
    /// Maximum length for short text fields (e.g., usernames, codes).
    /// </summary>
    public const int ShortText = 32;

    /// <summary>
    /// Maximum length for medium text fields (e.g., first name, last name, street, email address, product name).
    /// </summary>
    public const int MediumText = 100;

    /// <summary>
    /// Maximum length for long text fields (e.g., comments, notes).
    /// </summary>
    public const int LongText = 512;

    /// <summary>
    /// Maximum length for very long text fields (e.g., detailed descriptions, extensive notes).
    /// </summary>
    public const int VeryLongText = 4096;

    /// <summary>
    /// Maximum number of digits allowed for a decimal value, including both integer and fractional parts.
    /// </summary>
    public const int DecimalPrecision = 20;

    /// <summary>
    /// Maximum number of digits allowed after the decimal point in a decimal value.
    /// </summary>
    public const int DecimalScale = 2;
}
