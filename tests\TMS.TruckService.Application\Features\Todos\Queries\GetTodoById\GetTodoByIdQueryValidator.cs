using FluentValidation;

namespace TMS.TruckService.Application.Features.Todos.Queries.GetTodoById;

public class GetTodoByIdQueryValidator : AbstractValidator<GetTodoByIdQuery>
{
    public GetTodoByIdQueryValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Todo ID is required")
            .Must(id => id != Guid.Empty)
            .WithMessage("Todo ID must be a valid GUID");
    }
}