﻿using MediatR;

namespace TMS.SharedKernel.Domain.Events;


/// <summary>
/// A base type for domain events. Depends on MediatR INotification.
/// Includes DateOccurred which is set on creation.
/// </summary>
public abstract class DomainEventBase : INotification
{
    /// <summary>
    /// Gets or sets the date and time when the domain event occurred.
    /// Defaults to the current UTC time when the event is created.
    /// </summary>
    public DateTime DateOccurred { get; protected set; } = DateTime.UtcNow;
}
