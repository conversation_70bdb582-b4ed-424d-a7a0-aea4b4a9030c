﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\TMS.SharedKernal.SmoothRedis\TMS.SharedKernal.SmoothRedis.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernel.Constants\TMS.SharedKernel.Constants.csproj" />
    <ProjectReference Include="..\..\TMS.SharedKernel.Domain\TMS.SharedKernel.Domain.csproj" />
    <ProjectReference Include="..\TMS.TruckService.Domain\TMS.TruckService.Domain.csproj" />
    <ProjectReference Include="..\TMS.TruckService.Contracts\TMS.TruckService.Contracts.csproj" />
  </ItemGroup>

</Project>
