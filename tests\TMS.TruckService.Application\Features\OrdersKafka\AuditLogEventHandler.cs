﻿using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.SmoothRedis;
using TMS.SharedKernel.Domain.Events.AuditLog;
using TMS.TruckService.Domain.EventsKafka;

namespace TMS.TruckService.Application.Features.OrdersKafka;

public class AuditLogEventHandler : IMessageHandler<AuditLogMessage>
{
    public Task HandleAsync(AuditLogMessage message, MessageContext context, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}
