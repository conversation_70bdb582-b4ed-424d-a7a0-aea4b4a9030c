﻿using Microsoft.AspNetCore.Mvc.ViewFeatures;
using TMS.SharedKernel.Domain.Constants;
using TMS.SharedKernel.Domain.Entities.Interfaces;
using TMS.SharedKernel.Domain.Enums;

namespace TMS.SharedKernel.Domain.Provider.Interfaces;

/// <summary>
/// Interface for current logging in factor provider
/// </summary>
/// <remarks>
/// <para>Why use term "factor" instead of "user"?</para>
/// <para>Because not only user having ability to interact with system, but also other entities like system, background job, 3rd parties, etc.</para>
/// </remarks>
/// <typeparam name="TKey">Type of ID (primary key) for that factor</typeparam>
public interface ICurrentFactorProvider<out TKey> where TKey : notnull, IComparable, IComparable<TKey>, IEquatable<TKey>
{
    /// <summary>
    /// ID of current factor
    /// </summary>
    T<PERSON>ey CurrentFactorId { get; }

    /// <summary>
    /// CompanyId
    /// </summary>
    TKey CompanyId { get; }

    /// <summary>
    /// DepartmentId
    /// </summary>
    T<PERSON><PERSON> DepartmentId { get; }
}

/// <summary>
/// Interface for current logging in factor provider
/// </summary>
/// <para>Why use term "factor" instead of "user"?</para>
/// <para>Because not only user having ability to interact with system, but also other entities like system, background job, 3rd parties, etc.</para>
/// <typeparam name="TUser">Type of factor</typeparam>
/// <typeparam name="TKey">Type of ID (primary key) for that factor</typeparam>
public interface ICurrentFactorProvider<out TUser, out TKey> : ICurrentFactorProvider<TKey> where TUser : IEntity<TKey> where TKey : notnull, IComparable, IComparable<TKey>, IEquatable<TKey>
{
    /// <summary>
    /// Current factor
    /// </summary>
    TUser CurrentFactor { get; }
}

/// <summary>
/// Interface for current logging in factor provider with ID type is <see cref="Guid"/>
/// </summary>
public interface ICurrentFactorProvider : ICurrentFactorProvider<Guid>
{
    /// <inheritdoc/>
    public ClientType ClientType { get; }

    /// <inheritdoc/>
    public bool IsMobile => ClientType == ClientType.Mobile;

    /// <inheritdoc/>
    public string CurrentFactorName { get; }

    /// <inheritdoc/>
    public string Roles { get; }

    /// <inheritdoc/>
    public bool IsAdmin => String.Equals(Roles, DomainConstants.RoleAdministrator, StringComparison.OrdinalIgnoreCase);

    /// <inheritdoc/>
    public string DepartmentCode { get; }
}
