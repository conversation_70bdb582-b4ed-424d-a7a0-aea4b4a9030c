using Microsoft.Extensions.Logging;
using Quartz;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Global Quartz job listener that handles job execution errors gracefully
/// </summary>
public class QuartzJobListener : IJobListener
{
    private readonly ILogger<QuartzJobListener> _logger;

    /// <summary>
    /// QuartzJobListener
    /// </summary>
    /// <param name="logger"></param>
    public QuartzJobListener(ILogger<QuartzJobListener> logger)
    {
        _logger = logger;
    }

    /// <inheritdoc />
    public string Name => "GlobalQuartzJobListener";

    /// <inheritdoc />
    public Task JobToBeExecuted(IJobExecutionContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Job about to be executed: {JobKey}", context.JobDetail.Key);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobExecutionVetoed(IJobExecutionContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Job execution vetoed: {JobKey}", context.JobDetail.Key);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task JobWasExecuted(IJobExecutionContext context, JobExecutionException? jobException, CancellationToken cancellationToken = default)
    {
        if (jobException != null)
        {
            // Log the error but don't crash the application
            _logger.LogError(jobException,
                "Job {JobKey} threw an exception during execution. " +
                "RefireImmediately: {RefireImmediately}, UnscheduleAllTriggers: {UnscheduleAllTriggers}, UnscheduleFiringTrigger: {UnscheduleFiringTrigger}",
                context.JobDetail.Key,
                jobException.RefireImmediately,
                jobException.UnscheduleAllTriggers,
                jobException.UnscheduleFiringTrigger);

            // If the exception indicates database issues, log additional warning
            if (jobException.InnerException != null)
            {
                var innerMsg = jobException.InnerException.Message;
                if (innerMsg.Contains("database", StringComparison.OrdinalIgnoreCase) ||
                    innerMsg.Contains("connection", StringComparison.OrdinalIgnoreCase) ||
                    innerMsg.Contains("does not exist", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning(
                        "Job {JobKey} failed due to database connectivity issue. " +
                        "This may be caused by database recreation. The job will be retried based on its configuration.",
                        context.JobDetail.Key);
                }
            }
        }
        else
        {
            _logger.LogDebug("Job executed successfully: {JobKey}", context.JobDetail.Key);
        }

        return Task.CompletedTask;
    }
}
