﻿namespace TMS.SharedKernal.Kafka.Abstractions;

public interface IMessage
{
    string Id { get; set; }
    DateTime Timestamp { get; set; }
}

public interface IMessageHandler<in T> where T : class, IMessage
{
    Task HandleAsync(T message, MessageContext context, CancellationToken cancellationToken = default);
}

public interface IMessageProducer
{
    Task ProduceAsync<T>(string topic, T message, string key = null, CancellationToken cancellationToken = default) where T : class, IMessage;
    Task ProduceAsync<T>(T message, string key = null, CancellationToken cancellationToken = default) where T : class, IMessage;
    Task ProduceBatchAsync<T>(string topic, IEnumerable<T> messages, CancellationToken cancellationToken = default) where T : class, IMessage;
}

public interface IMessageConsumer
{
    Task StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
}

public interface ITopicConfiguration
{
    string TopicName { get; }
    string ConsumerGroup { get; }
    Type MessageType { get; }
    Type HandlerType { get; }
}

public interface IDeadLetterHandler
{
    Task HandleAsync(string originalTopic, string originalMessage, string error, MessageContext context);
}

public interface IMessageSerializer
{
    string Serialize<T>(T message) where T : class;
    T Deserialize<T>(string message) where T : class;
    object Deserialize(string message, Type type);
}

// MessageContext moved to Abstractions namespace for better accessibility
public class MessageContext
{
    public string Topic { get; set; }
    public string Key { get; set; }
    public int Partition { get; set; }
    public long Offset { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
}
