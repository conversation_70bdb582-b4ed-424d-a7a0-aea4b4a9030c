﻿using System.Diagnostics.CodeAnalysis;
using Serilog.Core;
using Serilog.Events;
using Serilog.Parsing;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Serilog enricher that automatically redacts sensitive data from log messages
/// </summary>
[ExcludeFromCodeCoverage]
public class SensitiveDataRedactionEnricher : ILogEventEnricher
{
    /// <summary>
    /// Enriches the log event by redacting only access_token in query strings
    /// This is a lightweight enricher focused only on access_token redaction
    /// </summary>
    /// <param name="logEvent">The log event to enrich</param>
    /// <param name="propertyFactory">The property factory</param>
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        if (logEvent == null)
            return;

        // Only redact access_token in the message template
        var messageText = logEvent.MessageTemplate.Text;
        var redactedMessage = RedactAccessTokenOnly(messageText);

        if (redactedMessage != messageText)
        {
            // Add redacted message as a property instead of modifying the immutable LogEvent
            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("RedactedMessage", redactedMessage));
        }

        // Redact access_token in properties
        foreach (var property in logEvent.Properties.ToList())
        {
            if (property.Value is ScalarValue scalar && scalar.Value is string stringValue)
            {
                var redactedValue = RedactAccessTokenOnly(stringValue);
                if (redactedValue != stringValue)
                {
                    logEvent.RemovePropertyIfPresent(property.Key);
                    logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty(property.Key, redactedValue));
                }
            }
        }
    }

    /// <summary>
    /// Redacts only access_token parameter in query strings
    /// </summary>
    private static string RedactAccessTokenOnly(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        // Only redact access_token parameter in query strings
        // Pattern: access_token=<value> where value can contain various characters
        return System.Text.RegularExpressions.Regex.Replace(
            input,
            @"(access_token=)[^&\s]+",
            "$1[REDACTED]",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);
    }


}

/// <summary>
/// Extension methods for adding sensitive data redaction to Serilog
/// </summary>
[ExcludeFromCodeCoverage]
public static class SensitiveDataRedactionEnricherExtensions
{
    /// <summary>
    /// Adds sensitive data redaction enricher to the logger configuration
    /// </summary>
    /// <param name="enrichmentConfiguration">The enrichment configuration</param>
    /// <returns>The logger configuration</returns>
    public static Serilog.LoggerConfiguration WithSensitiveDataRedaction(
        this Serilog.Configuration.LoggerEnrichmentConfiguration enrichmentConfiguration)
    {
        if (enrichmentConfiguration == null)
            throw new ArgumentNullException(nameof(enrichmentConfiguration));

        return enrichmentConfiguration.With<SensitiveDataRedactionEnricher>();
    }
}
