using Confluent.Kafka;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.Kafka.Configuration;
using TMS.SharedKernal.Kafka.Services;
using Xunit;

namespace TMS.SharedKernal.Kafka.Tests;

public class KafkaMessageConsumerTests
{
    private readonly Mock<IServiceProvider> _serviceProviderMock;
    private readonly Mock<IMessageSerializer> _serializerMock;
    private readonly Mock<ILogger<KafkaMessageConsumer>> _loggerMock;
    private readonly KafkaFlowOptions _options;
    private readonly string _serviceKey = "test-service";

    public KafkaMessageConsumerTests()
    {
        _serviceProviderMock = new Mock<IServiceProvider>();
        _serializerMock = new Mock<IMessageSerializer>();
        _loggerMock = new Mock<ILogger<KafkaMessageConsumer>>();

        _options = new KafkaFlowOptions
        {
            BootstrapServers = "localhost:9092",
            Consumer = new ConsumerOptions
            {
                AutoOffsetReset = "earliest",
                EnableAutoCommit = false,
                SessionTimeoutMs = 6000,
                MaxPollIntervalMs = 300000,
                MaxRetries = 3,
                RetryDelayMs = 1000
            },
            DeadLetter = new DeadLetterOptions
            {
                TopicName = "test-dlq",
                ConsumerGroup = "test-dlq-group",
                Enabled = true,
                MaxRetries = 3
            },
            Topics = new List<TopicConfiguration>()
        };
    }

    [Fact]
    public void GetRetryCount_NoHeaders_ReturnsZero()
    {
        // Arrange
        var consumer = CreateConsumer();
        var result = CreateConsumeResult(null);

        // Act
        var retryCount = InvokePrivateMethod<int>(consumer, "GetRetryCount", result);

        // Assert
        Assert.Equal(0, retryCount);
    }

    [Fact]
    public void GetRetryCount_WithRetryHeader_ReturnsCorrectCount()
    {
        // Arrange
        var consumer = CreateConsumer();
        var headers = new Headers
        {
            { "x-retry-count", BitConverter.GetBytes(2) }
        };
        var result = CreateConsumeResult(headers);

        // Act
        var retryCount = InvokePrivateMethod<int>(consumer, "GetRetryCount", result);

        // Assert
        Assert.Equal(2, retryCount);
    }

    [Fact]
    public void GetRetryCount_WithInvalidHeader_ReturnsZero()
    {
        // Arrange
        var consumer = CreateConsumer();
        var headers = new Headers
        {
            { "x-retry-count", new byte[] { 1, 2 } } // Invalid size (not 4 bytes)
        };
        var result = CreateConsumeResult(headers);

        // Act
        var retryCount = InvokePrivateMethod<int>(consumer, "GetRetryCount", result);

        // Assert
        Assert.Equal(0, retryCount);
    }

    [Fact]
    public void CalculateRetryDelay_FirstRetry_Returns1Second()
    {
        // Arrange
        var consumer = CreateConsumer();

        // Act
        var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", 0);

        // Assert
        Assert.Equal(1000, delay);
    }

    [Fact]
    public void CalculateRetryDelay_SecondRetry_Returns2Seconds()
    {
        // Arrange
        var consumer = CreateConsumer();

        // Act
        var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", 1);

        // Assert
        Assert.Equal(2000, delay);
    }

    [Fact]
    public void CalculateRetryDelay_ThirdRetry_Returns4Seconds()
    {
        // Arrange
        var consumer = CreateConsumer();

        // Act
        var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", 2);

        // Assert
        Assert.Equal(4000, delay);
    }

    [Fact]
    public void CalculateRetryDelay_FifthRetry_Returns16Seconds()
    {
        // Arrange
        var consumer = CreateConsumer();

        // Act
        var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", 4);

        // Assert
        Assert.Equal(16000, delay);
    }

    [Fact]
    public void CalculateRetryDelay_MaxRetry_Returns30SecondsMax()
    {
        // Arrange
        var consumer = CreateConsumer();

        // Act - retry count 10 would be 1024 seconds, but should cap at 30
        var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", 10);

        // Assert
        Assert.Equal(30000, delay);
    }

    [Fact]
    public void CalculateRetryDelay_ExponentialBackoff_IsCorrect()
    {
        // Arrange
        var consumer = CreateConsumer();
        var expectedDelays = new Dictionary<int, int>
        {
            { 0, 1000 },   // 1s
            { 1, 2000 },   // 2s
            { 2, 4000 },   // 4s
            { 3, 8000 },   // 8s
            { 4, 16000 },  // 16s
            { 5, 30000 },  // 30s (capped)
            { 6, 30000 },  // 30s (capped)
        };

        // Act & Assert
        foreach (var kvp in expectedDelays)
        {
            var delay = InvokePrivateMethod<int>(consumer, "CalculateRetryDelay", kvp.Key);
            Assert.Equal(kvp.Value, delay);
        }
    }

    [Fact]
    public async Task ProcessMessage_Success_ReturnsTrue()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();
        var result = CreateConsumeResult(null);
        var testMessage = new TestMessage { Id = "test-1", Data = "test data" };

        SetupSuccessfulMessageProcessing(testMessage, topicConfig);

        // Act
        var shouldCommit = await InvokePrivateMethodAsync<bool>(
            consumer,
            "ProcessMessage",
            topicConfig,
            result,
            CancellationToken.None);

        // Assert
        Assert.True(shouldCommit);
    }

    [Fact]
    public async Task ProcessMessage_FailureOnFirstAttempt_SendsToRetryTopic()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();
        var result = CreateConsumeResult(null); // No retry header = first attempt

        SetupFailedMessageProcessing(topicConfig);
        var producerMock = SetupProducerMock();

        // Act
        var shouldCommit = await InvokePrivateMethodAsync<bool>(
            consumer,
            "ProcessMessage",
            topicConfig,
            result,
            CancellationToken.None);

        // Assert
        Assert.True(shouldCommit); // Should still commit to move forward

        // Verify producer was called (message sent to retry topic)
        producerMock.Verify(
            p => p.ProduceAsync(
                It.Is<string>(topic => topic.Contains(".retry")),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessMessage_FailureAtMaxRetries_SendsToDLQ()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();

        // Create result with retry count = 3 (max retries reached)
        var headers = new Headers
        {
            { "x-retry-count", BitConverter.GetBytes(3) }
        };
        var result = CreateConsumeResult(headers);

        SetupFailedMessageProcessing(topicConfig);
        var producerMock = SetupMessageProducerMock();

        // Act
        var shouldCommit = await InvokePrivateMethodAsync<bool>(
            consumer,
            "ProcessMessage",
            topicConfig,
            result,
            CancellationToken.None);

        // Assert
        Assert.True(shouldCommit); // Should commit after sending to DLQ

        // Verify message was sent to DLQ
        producerMock.Verify(
            p => p.ProduceAsync(
                _options.DeadLetter.TopicName,
                It.Is<DeadLetterMessage>(msg =>
                    msg.RetryCount == 3 &&
                    msg.OriginalTopic == topicConfig.TopicName),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessMessage_FailureWithDLQDisabled_ThrowsException()
    {
        // Arrange
        var consumer = CreateConsumer();
        _options.DeadLetter.Enabled = false;

        var topicConfig = CreateTopicConfiguration();
        var result = CreateConsumeResult(null);

        SetupFailedMessageProcessing(topicConfig);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(async () =>
        {
            await InvokePrivateMethodAsync<bool>(
                consumer,
                "ProcessMessage",
                topicConfig,
                result,
                CancellationToken.None);
        });
    }

    [Fact]
    public async Task ProcessMessage_RetryCount1_SendsToRetryTopic()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();

        // Simulate first retry (retry count = 1)
        var headers = new Headers
        {
            { "x-retry-count", BitConverter.GetBytes(1) }
        };
        var result = CreateConsumeResult(headers);

        SetupFailedMessageProcessing(topicConfig);
        var producerMock = SetupProducerMock();

        // Act
        var shouldCommit = await InvokePrivateMethodAsync<bool>(
            consumer,
            "ProcessMessage",
            topicConfig,
            result,
            CancellationToken.None);

        // Assert
        Assert.True(shouldCommit);

        // Verify retry topic was called
        producerMock.Verify(
            p => p.ProduceAsync(
                It.Is<string>(topic => topic.Contains(".retry")),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessMessage_RetryCount2_SendsToRetryTopicAgain()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();

        // Simulate second retry (retry count = 2)
        var headers = new Headers
        {
            { "x-retry-count", BitConverter.GetBytes(2) }
        };
        var result = CreateConsumeResult(headers);

        SetupFailedMessageProcessing(topicConfig);
        var producerMock = SetupProducerMock();

        // Act
        var shouldCommit = await InvokePrivateMethodAsync<bool>(
            consumer,
            "ProcessMessage",
            topicConfig,
            result,
            CancellationToken.None);

        // Assert
        Assert.True(shouldCommit);

        // Still sends to retry (not DLQ yet)
        producerMock.Verify(
            p => p.ProduceAsync(
                It.Is<string>(topic => topic.Contains(".retry")),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task SendToRetryTopic_AddsCorrectHeaders()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();
        var result = CreateConsumeResult(null);
        var producerMock = SetupProducerMock();

        Message<string, string>? capturedMessage = null;
        producerMock.Setup(p => p.ProduceAsync(
                It.IsAny<string>(),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()))
            .Callback<string, Message<string, string>, CancellationToken>((_, msg, _) => capturedMessage = msg)
            .ReturnsAsync((DeliveryResult<string, string>)null!);

        var scopeMock = new Mock<IServiceScope>();
        var serviceScopeMock = new Mock<IServiceProvider>();
        serviceScopeMock.Setup(s => s.GetService(typeof(IProducer<string, string>)))
            .Returns(producerMock.Object);
        scopeMock.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);

        // Act
        await InvokePrivateMethodAsync<Task>(
            consumer,
            "SendToRetryTopic",
            topicConfig.TopicName,
            result,
            0, // currentRetryCount
            scopeMock.Object);

        // Assert
        Assert.NotNull(capturedMessage);
        Assert.Contains(capturedMessage.Headers, h => h.Key == "x-retry-count");
        Assert.Contains(capturedMessage.Headers, h => h.Key == "x-original-topic");
        Assert.Contains(capturedMessage.Headers, h => h.Key == "x-failed-at");

        // Verify retry count was incremented to 1
        var retryHeader = capturedMessage.Headers.FirstOrDefault(h => h.Key == "x-retry-count");
        Assert.NotNull(retryHeader);
        var retryCount = BitConverter.ToInt32(retryHeader.GetValueBytes(), 0);
        Assert.Equal(1, retryCount);
    }

    [Fact]
    public async Task SendToDeadLetter_CreatesCorrectMessage()
    {
        // Arrange
        var consumer = CreateConsumer();
        var topicConfig = CreateTopicConfiguration();
        var result = CreateConsumeResult(null);
        var exception = new InvalidOperationException("Test error");
        var producerMock = SetupMessageProducerMock();

        DeadLetterMessage? capturedMessage = null;
        producerMock.Setup(p => p.ProduceAsync(
                It.IsAny<string>(),
                It.IsAny<DeadLetterMessage>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .Callback<string, DeadLetterMessage, string, CancellationToken>((_, msg, _, _) => capturedMessage = msg)
            .Returns(Task.CompletedTask);

        var scopeMock = new Mock<IServiceScope>();
        var serviceScopeMock = new Mock<IServiceProvider>();
        serviceScopeMock.Setup(s => s.GetKeyedService<IMessageProducer>(_serviceKey))
            .Returns(producerMock.Object);
        scopeMock.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);

        // Act
        await InvokePrivateMethodAsync<Task>(
            consumer,
            "SendToDeadLetter",
            topicConfig.TopicName,
            result,
            exception,
            3, // retryCount
            scopeMock.Object);

        // Assert
        Assert.NotNull(capturedMessage);
        Assert.Equal(topicConfig.TopicName, capturedMessage.OriginalTopic);
        Assert.Equal(3, capturedMessage.RetryCount);
        Assert.Contains("Test error", capturedMessage.Error);
        Assert.Equal("test-value", capturedMessage.OriginalMessage);
    }

    [Fact]
    public void DLQOptions_MaxRetries_DefaultIs3()
    {
        // Assert
        Assert.Equal(3, _options.DeadLetter.MaxRetries);
    }

    [Fact]
    public void DLQOptions_Enabled_DefaultIsTrue()
    {
        // Assert
        Assert.True(_options.DeadLetter.Enabled);
    }

    #region Helper Methods

    private KafkaMessageConsumer CreateConsumer()
    {
        return new KafkaMessageConsumer(
            _serviceProviderMock.Object,
            _options,
            _serializerMock.Object,
            _loggerMock.Object,
            _serviceKey);
    }

    private TopicConfiguration CreateTopicConfiguration()
    {
        return new TopicConfiguration
        {
            TopicName = "test-topic",
            ConsumerGroup = "test-group",
            MessageType = typeof(TestMessage),
            HandlerType = typeof(TestMessageHandler)
        };
    }

    private ConsumeResult<string, string> CreateConsumeResult(Headers? headers)
    {
        var message = new Message<string, string>
        {
            Key = "test-key",
            Value = "test-value",
            Timestamp = new Timestamp(DateTime.UtcNow),
            Headers = headers
        };

        return new ConsumeResult<string, string>
        {
            Message = message,
            Topic = "test-topic",
            Partition = new Partition(0),
            Offset = new Offset(123),
            TopicPartitionOffset = new TopicPartitionOffset("test-topic", 0, 123)
        };
    }

    private void SetupSuccessfulMessageProcessing(TestMessage message, TopicConfiguration config)
    {
        var scopeMock = new Mock<IServiceScope>();
        var handlerMock = new Mock<IMessageHandler<TestMessage>>();

        handlerMock.Setup(h => h.HandleAsync(
                It.IsAny<TestMessage>(),
                It.IsAny<MessageContext>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var serviceScopeMock = new Mock<IServiceProvider>();
        serviceScopeMock.Setup(s => s.GetService(config.HandlerType))
            .Returns(handlerMock.Object);

        scopeMock.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);

        _serviceProviderMock.Setup(s => s.CreateScope()).Returns(scopeMock.Object);
        _serializerMock.Setup(s => s.Deserialize(It.IsAny<string>(), config.MessageType))
            .Returns(message);
    }

    private void SetupFailedMessageProcessing(TopicConfiguration config)
    {
        var scopeMock = new Mock<IServiceScope>();
        var handlerMock = new Mock<IMessageHandler<TestMessage>>();

        handlerMock.Setup(h => h.HandleAsync(
                It.IsAny<TestMessage>(),
                It.IsAny<MessageContext>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Processing failed"));

        var serviceScopeMock = new Mock<IServiceProvider>();
        serviceScopeMock.Setup(s => s.GetService(config.HandlerType))
            .Returns(handlerMock.Object);

        scopeMock.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);

        _serviceProviderMock.Setup(s => s.CreateScope()).Returns(scopeMock.Object);
        _serializerMock.Setup(s => s.Deserialize(It.IsAny<string>(), config.MessageType))
            .Returns(new TestMessage { Id = "test-1", Data = "test data" });
    }

    private Mock<IProducer<string, string>> SetupProducerMock()
    {
        var producerMock = new Mock<IProducer<string, string>>();
        producerMock.Setup(p => p.ProduceAsync(
                It.IsAny<string>(),
                It.IsAny<Message<string, string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((DeliveryResult<string, string>)null!);

        var scopeMock = _serviceProviderMock.Setup(s => s.CreateScope()).Returns(() =>
        {
            var scope = new Mock<IServiceScope>();
            var serviceScopeMock = new Mock<IServiceProvider>();
            serviceScopeMock.Setup(s => s.GetKeyedService<IProducer<string, string>>(_serviceKey))
                .Returns(producerMock.Object);
            scope.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);
            return scope.Object;
        });

        return producerMock;
    }

    private Mock<IMessageProducer> SetupMessageProducerMock()
    {
        var producerMock = new Mock<IMessageProducer>();
        producerMock.Setup(p => p.ProduceAsync(
                It.IsAny<string>(),
                It.IsAny<DeadLetterMessage>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _serviceProviderMock.Setup(s => s.CreateScope()).Returns(() =>
        {
            var scope = new Mock<IServiceScope>();
            var serviceScopeMock = new Mock<IServiceProvider>();
            serviceScopeMock.Setup(s => s.GetKeyedService<IMessageProducer>(_serviceKey))
                .Returns(producerMock.Object);
            scope.Setup(s => s.ServiceProvider).Returns(serviceScopeMock.Object);
            return scope.Object;
        });

        return producerMock;
    }

    private T InvokePrivateMethod<T>(object obj, string methodName, params object[] parameters)
    {
        var method = obj.GetType().GetMethod(methodName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method == null)
            throw new InvalidOperationException($"Method {methodName} not found");

        return (T)method.Invoke(obj, parameters)!;
    }

    private async Task<T> InvokePrivateMethodAsync<T>(object obj, string methodName, params object[] parameters)
    {
        var method = obj.GetType().GetMethod(methodName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method == null)
            throw new InvalidOperationException($"Method {methodName} not found");

        var result = method.Invoke(obj, parameters);

        if (result is Task<T> taskResult)
            return await taskResult;

        if (result is Task task)
        {
            await task;
            return default(T)!;
        }

        return (T)result!;
    }

    #endregion

    #region Test Classes

    public class TestMessage : BaseMessage
    {
        public string Data { get; set; } = string.Empty;
    }

    public class TestMessageHandler : IMessageHandler<TestMessage>
    {
        public Task HandleAsync(TestMessage message, MessageContext context, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }
    }

    #endregion
}
