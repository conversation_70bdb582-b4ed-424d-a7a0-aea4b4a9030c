﻿using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;

namespace TMS.SharedKernel.Utilities;

/// <summary>
/// Provides extension methods for logging.
/// </summary>
[ExcludeFromCodeCoverage]
public static class StringExtensions
{
    public static string RemoveAccents(this string text)
    {
        string[] array = new string[14]
        {
            "á|à|ả|ã|ạ|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ", "a", "é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ", "e", "í|ì|ỉ|ĩ|ị", "i", "ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ", "o", "ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự", "u",
            "ý|ỳ|ỷ|ỹ|ỵ", "y", "đ", "d"
        };
        for (int i = 0; i < array.Length; i += 2)
        {
            text = Regex.Replace(text, array[i], array[i + 1], RegexOptions.IgnoreCase);
        }

        return text;
    }
}
