﻿namespace TMS.SharedKernel.Domain.Entities.Interfaces;

/// <summary>
/// Interface for auditable entity, including tracking of created and modified date and factor performing the action
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IAuditable<T> : IAuditDateTracking, IFactorTracking<T> where T : notnull, IComparable, IComparable<T>, IEquatable<T> { }


/// <summary>
/// Default type of id for factor tracking is <see cref="Guid"/>
/// </summary>
public interface IAuditable :
    IAuditable<Guid>,
    IFactorTracking
{ }
