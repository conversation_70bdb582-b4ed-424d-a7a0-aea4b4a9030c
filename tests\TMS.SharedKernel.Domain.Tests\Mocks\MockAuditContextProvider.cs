using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.FileProviders;
using TMS.SharedKernel.Domain.Events.AuditLog;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore.Services;

namespace TMS.SharedKernel.Domain.Tests.Mocks;

/// <summary>
/// Mock implementation of AuditContextProvider for testing
/// </summary>
public class MockAuditContextProvider : AuditContextProvider
{
    public MockAuditContextProvider(ICurrentFactorProvider currentFactorProvider)
        : base(currentFactorProvider, new MockHttpContextAccessor(), new MockWebHostEnvironment())
    {
    }
}

/// <summary>
/// Mock HttpContextAccessor that returns null context
/// </summary>
public class MockHttpContextAccessor : IHttpContextAccessor
{
    public HttpContext? HttpContext { get; set; }
}

/// <summary>
/// Mock WebHostEnvironment for testing
/// </summary>
public class MockWebHostEnvironment : IWebHostEnvironment
{
    public string WebRootPath { get; set; } = string.Empty;
    public IFileProvider WebRootFileProvider { get; set; } = null!;
    public string ApplicationName { get; set; } = "TestApp";
    public IFileProvider ContentRootFileProvider { get; set; } = null!;
    public string ContentRootPath { get; set; } = string.Empty;
    public string EnvironmentName { get; set; } = "Test";
}
