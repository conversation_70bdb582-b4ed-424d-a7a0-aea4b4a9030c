﻿namespace TMS.SharedKernel.Domain.Entities.Interfaces;

/// <summary>
/// Create factor tracking
/// </summary>
/// <remarks>
/// <para>Why use term "factor" instead of "user"?</para>
/// <para>Because not only user having ability to interact with system, but also other entities like system, background job, 3rd parties, etc.</para>
/// </remarks>
/// <typeparam name="T">Type of ID for the factor that performs creation.</typeparam>
public interface ICreatedFactorTracking<T> where T : notnull, IComparable, IComparable<T>, IEquatable<T>
{
    /// <summary>
    /// Factor that create the entity
    /// </summary>
    public T CreatedBy { get; set; }
}

/// <summary>
/// ID of factor that create the entity is <see cref="Guid"/>
/// </summary>
public interface ICreatedFactorTracking : ICreatedFactorTracking<Guid> { }

/// <summary>
/// Modified factor tracking
/// </summary>
/// <remarks>
/// <para>Why use term "factor" instead of "user"?</para>
/// <para>Because not only user having ability to interact with system, but also other entities like system, background job, 3rd parties, etc.</para>
/// </remarks>
/// <typeparam name="T">Type of id for factor that performing modification</typeparam>
public interface IUpdatedFactorTracking<T> where T : notnull, IComparable, IComparable<T>, IEquatable<T>
{
    /// <summary>
    /// Factor that modify the entity
    /// </summary>
    public T UpdatedBy { get; set; }
}

/// <summary>
/// ID of factor that modified the entity is <see cref="Guid"/>
/// </summary>
public interface IUpdatedFactorTracking : IUpdatedFactorTracking<Guid> { }

/// <summary>
/// Create and modified factor tracking
/// </summary>
/// <remarks>
/// <para>Why use term "factor" instead of "user"?</para>
/// <para>Because not only user having ability to interact with system, but also other entities like system, background job, 3rd parties, etc.</para>
/// </remarks>
/// <typeparam name="T">Type of id for factor that performing creation and modification</typeparam>
public interface IFactorTracking<T> : ICreatedFactorTracking<T>, IUpdatedFactorTracking<T> where T : notnull, IComparable, IComparable<T>, IEquatable<T> { }


/// <summary>
/// ID of factor that created and modified the entity is <see cref="Guid"/>
/// </summary>
public interface IFactorTracking : IFactorTracking<Guid>, ICreatedFactorTracking, IUpdatedFactorTracking { }
